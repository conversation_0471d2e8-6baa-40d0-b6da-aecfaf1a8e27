<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [ %-5level]  %m%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!--控制台打印日志绑定-->
    <root level="INFO">
        <appender-ref ref="console"/>
    </root>

</configuration>

        <!--        优先级从高到低依次为：OFF、FATAL、ERROR、WARN、INFO、DEBUG、TRACE、 ALL-->
        <!--    7    ALL：打开所有日志记录 -->
        <!--    6    TRACE：很低的日志级别，一般不会使用 -->
        <!--    5    DEBUG：开发过程中打印一些运行信息 -->
        <!--    4    INFO：应用程序的运行过程 -->
        <!--    3    WARN：运行过程中的警告 -->
        <!--    2    ERROR：不影响系统运行的错误 -->
        <!--    1    FATAL：重大错误，直接停止程序的错误 -->
        <!--    0    OFF：关闭所有日志记录 -->
