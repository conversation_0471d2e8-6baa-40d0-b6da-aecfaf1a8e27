<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathListMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathListDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="flow_path_id" property="flowPathId"/>
        <result column="type" property="type"/>
        <result column="uuid" property="uuid"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="create_time" property="createTime"/>
        <result column="extra_content" property="extraContent"/>
        <result column="agent_expiration_time" property="agentExpirationTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        title,
        flow_path_id,
        `type`,
        uuid,
        app_channel_id,
        data_channel_id,
        create_time,
        extra_content,
        agent_expiration_time
    </sql>

    <insert id="addFlowPathList" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowPathListDAO">
        INSERT INTO flow_path_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != title">
                title,
            </if>
            <if test="null != flowPathId">
                flow_path_id,
            </if>
            <if test="null != type">
                `type`,
            </if>
            <if test="null != uuid">
                uuid,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != extraContent">
                extra_content
            </if>
            <if test="null != agentExpirationTime">
                agent_expiration_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != title">
                #{title},
            </if>
            <if test="null != flowPathId">
                #{flowPathId},
            </if>
            <if test="null != type">
                #{type},
            </if>
            <if test="null != uuid">
                #{uuid},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != extraContent">
                #{extraContent}
            </if>
            <if test="null != agentExpirationTime">
                #{agentExpirationTime}
            </if>
        </trim>
    </insert>

    <insert id="batchAddLists">
        <if test="list != null and !list.isEmpty()">
            INSERT INTO flow_path_list (
            title,
            flow_path_id,
            type,
            uuid,
            app_channel_id,
            data_channel_id,
            extra_content,
            agent_expiration_time
            ) VALUES
            <foreach collection="list" item="item" separator=",">
                (
                #{item.title},
                #{item.flowPathId},
                #{item.type},
                #{item.uuid},
                #{item.appChannelId},
                #{item.dataChannelId},
                #{item.extraContent},
                #{item.agentExpirationTime}
                )
            </foreach>
        </if>
    </insert>

    <delete id="deleteFlowPathList">
        DELETE
        FROM flow_path_list
        WHERE id = #{id}
    </delete>

    <delete id="deleteByFlowPathIdAndType" parameterType="com.eu.api.domain.condition.RemoveFlowPathListCondition">
        DELETE
        FROM `flow_path_list`
        WHERE `flow_path_id` = #{flowPathId}
          AND `type` = #{type}
    </delete>

    <delete id="delete" parameterType="com.eu.api.domain.condition.GetFlowPathListDAOListCondition">
        DELETE FROM flow_path_list
        <where>
            <if test="condition.flowPathId != null">
                flow_path_id = #{condition.flowPathId}
            </if>
            <if test="condition.flowPathIdList != null and condition.flowPathIdList.size() &gt; 0">
                AND flow_path_id IN
                <foreach collection="condition.flowPathIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="condition.type != null">
                and type = #{condition.type}
            </if>
            <if test="condition.uuidList != null and condition.uuidList.size() &gt; 0">
                and uuid IN
                <foreach collection="condition.uuidList" item="uuid" open="(" separator="," close=")">
                    #{uuid}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteFlowPathListByMySelf"
            parameterType="com.eu.api.domain.dto.FlowPathListDeleteDTO">
        DELETE FROM flow_path_list
        WHERE uuid = #{userId}
        AND flow_path_id IN
        <foreach collection="ids"
                 item="flowPathId"
                 open="("
                 separator=","
                 close=")">
            #{flowPathId}
        </foreach>
    </delete>


    <update id="updateFlowPathList" parameterType="com.eu.api.domain.dao.FlowPathListDAO">
        UPDATE flow_path_list
        <set>
            <if test="null != title">title = #{title},</if>
            <if test="null != flowPathId">flow_path_id = #{flowPathId},</if>
            <if test="null != type">type = #{type},</if>
            <if test="null != uuid">uuid = #{uuid},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != extraContent">extra_content = #{extraContent}</if>
            <if test="null != agentExpirationTime">agent_expiration_time = #{agentExpirationTime}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="update">
        <if test="null != condition and null != setData">
            UPDATE flow_path_list
            <set>
                <if test="null != setData.title">title = #{setData.title},</if>
                <if test="null != setData.flowPathId">flow_path_id = #{setData.flowPathId},</if>
                <if test="null != setData.type">type = #{setData.type},</if>
                <if test="null != setData.uuid">uuid = #{setData.uuid},</if>
                <if test="null != setData.appChannelId">app_channel_id = #{setData.appChannelId},</if>
                <if test="null != setData.dataChannelId">data_channel_id = #{setData.dataChannelId},</if>
                <if test="null != setData.createTime">create_time = #{setData.createTime},</if>
                <if test="null != setData.extraContent">extra_content = #{setData.extraContent}</if>
                <if test="null != setData.agentExpirationTime">agent_expiration_time = #{agentExpirationTime}</if>
            </set>
            <where>
                <if test="null != condition.id">AND id = #{condition.id}</if>
                <if test="null != condition.flowPathId">AND flow_path_id = #{condition.flowPathId}</if>
                <if test="null != condition.type">AND type = #{condition.type}</if>
                <if test="null != condition.uuid">AND uuid = #{condition.uuid}</if>
                <if test="null != condition.uuidList and !condition.uuidList.isEmpty()">
                    <foreach collection="condition.uuidList" item="uuid" open="AND uuid IN (" close=")" separator=",">
                        #{uuid}
                    </foreach>
                </if>
                <if test="null != condition.typeList and !condition.typeList.isEmpty()">
                    <foreach collection="condition.typeList" item="type" open="AND type IN (" close=")" separator=",">
                        #{type}
                    </foreach>
                </if>
            </where>
        </if>
    </update>

    <select id="getFlowPathList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_list
        WHERE id = #{id}
    </select>

    <select id="getFlowPathListList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_list
        WHERE id IN (
        SELECT MIN(id)
        FROM flow_path_list
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                <foreach collection="condition.flowPathIdList" item="flowPathIdList" open="AND flow_path_id IN ("
                         close=")" separator=",">
                    #{flowPathIdList}
                </foreach>
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.typeList and !condition.typeList.isEmpty()">
                <foreach collection="condition.typeList" item="type" open="AND type IN (" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="null != condition.uuid and condition.uuid != ''">
                AND uuid = #{condition.uuid}
            </if>
            <if test="null != condition.uuidList and !condition.uuidList.isEmpty()">
                <foreach collection="condition.uuidList" item="item" open="AND uuid IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.companyIdList and !condition.companyIdList.isEmpty()">
                AND extra_content -> '$.companyId'
                <foreach collection="condition.companyIdList" item="item" open="IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <!-- 新增showName条件 -->
            <if test="condition.showName != null and condition.showName != ''">
                AND flow_path_id IN (
                SELECT id
                FROM flow_path
                WHERE show_name LIKE CONCAT('%', #{condition.showName}, '%')
                )
            </if>
            <if test="null != condition.type and condition.type == 2">
                AND agent_expiration_time > CURRENT_TIMESTAMP
            </if>
        </where>
        GROUP BY flow_path_id
        )
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT #{start}, #{limit}
        </if>
    </select>

    <select id="getFlowPathListStatistics" resultMap="BaseResultMap">
        SELECT
        id,`type`
        FROM flow_path_list
        WHERE id IN (
        SELECT MIN(id)
        FROM flow_path_list
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                <foreach collection="condition.flowPathIdList" item="flowPathIdList" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathIdList}
                </foreach>
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.typeList and !condition.typeList.isEmpty()">
                <foreach collection="condition.typeList" item="type" open="AND type IN (" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="null != condition.uuid and condition.uuid != ''">
                AND uuid = #{condition.uuid}
            </if>
            <if test="null != condition.uuidList and !condition.uuidList.isEmpty()">
                <foreach collection="condition.uuidList" item="item" open="AND uuid IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.companyIdList and condition.companyIdList.size() > 0">
                AND extra_content -> '$.companyId'
                <foreach collection="condition.companyIdList" item="item" open="IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <!-- 新增showName条件 -->
            <if test="condition.showName != null and condition.showName != ''">
                AND flow_path_id IN (
                SELECT id
                FROM flow_path
                WHERE show_name LIKE CONCAT('%', #{condition.showName}, '%')
                )
            </if>
        </where>
        GROUP BY flow_path_id
        )
    </select>

    <select id="getFlowPathListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM flow_path_list
        WHERE id IN (
        SELECT MIN(id)
        FROM flow_path_list
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                <foreach collection="condition.flowPathIdList" item="flowPathIdList" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathIdList}
                </foreach>
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.typeList and !condition.typeList.isEmpty()">
                <foreach collection="condition.typeList" item="type" open="AND type IN (" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="null != condition.uuid and condition.uuid != ''">
                AND uuid = #{condition.uuid}
            </if>
            <if test="null != condition.uuidList and !condition.uuidList.isEmpty()">
                <foreach collection="condition.uuidList" item="item" open="AND uuid IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.companyIdList and !condition.companyIdList.isEmpty()">
                AND extra_content -> '$.companyId'
                <foreach collection="condition.companyIdList" item="item" open="IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <!-- 新增 showName 条件 -->
            <if test="condition.showName != null and condition.showName != ''">
                AND flow_path_id IN (
                SELECT id
                FROM flow_path
                WHERE show_name LIKE CONCAT('%', #{condition.showName}, '%')
                )
            </if>
            <if test="null != condition.type and condition.type == 2">
                AND agent_expiration_time > CURRENT_TIMESTAMP
            </if>
        </where>
        GROUP BY flow_path_id
        );
    </select>

    <select id="getFlowPathListCountAll" resultType="com.eu.api.domain.dto.FlowPathCountDTO">
        SELECT
        COUNT(DISTINCT CASE WHEN type = 1 THEN flow_path_id END) AS type1Count,
        COUNT(DISTINCT CASE WHEN type = 2 AND agent_expiration_time > CURRENT_TIMESTAMP THEN flow_path_id END) AS type2Count,
        COUNT(DISTINCT CASE WHEN type = 3 THEN flow_path_id END) AS type3Count,
        COUNT(DISTINCT CASE WHEN type = 4 THEN flow_path_id END) AS type4Count
        FROM flow_path_list
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                <foreach collection="condition.flowPathIdList" item="flowPathIdList" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathIdList}
                </foreach>
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.typeList and !condition.typeList.isEmpty()">
                <foreach collection="condition.typeList" item="type" open="AND type IN (" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="null != condition.uuid and condition.uuid != ''">
                AND uuid = #{condition.uuid}
            </if>
            <if test="null != condition.uuidList and !condition.uuidList.isEmpty()">
                <foreach collection="condition.uuidList" item="item" open="AND uuid IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
            <if test="null != condition.companyIdList and !condition.companyIdList.isEmpty()">
                AND extra_content -> '$.companyId'
                <foreach collection="condition.companyIdList" item="item" open="IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <!-- 新增 showName 条件 -->
            <if test="condition.showName != null and condition.showName != ''">
                AND flow_path_id IN (
                SELECT id
                FROM flow_path
                WHERE show_name LIKE CONCAT('%', #{condition.showName}, '%')
                )
            </if>
        </where>
    </select>

    <select id="flowPathCompany" resultType="java.lang.String">
        SELECT DISTINCT REPLACE(extra_content -> '$.companyId','"','')  from flow_path_list
        <where>
            <if test="null != condition.uuid and condition.uuid != ''">
                AND uuid = #{condition.uuid}
            </if>
        </where>


    </select>

    <select id="getFlowPathListBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_list
        WHERE flow_path_id IN
        <foreach item="id" collection="flowPathIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND type = #{type}
        ORDER BY create_time DESC
    </select>

</mapper>