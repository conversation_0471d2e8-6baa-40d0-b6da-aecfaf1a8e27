<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.TemplateMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.TemplateDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="title_config" property="titleConfig"/>
        <result column="desc" property="desc"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="temp_info" property="tempInfo"/>
        <result column="public" property="isPublic"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        title,
        title_config,
        `desc`,
        app_channel_id,
        data_channel_id,
        temp_info,
        `public`,
        create_time,
        update_time,
        `status`
    </sql>

    <insert id="addTemplate" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.TemplateDAO">
        INSERT INTO template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != title">
                title,
            </if>
            <if test="null != titleConfig">
                title_config,
            </if>
            <if test="null != desc">
                `desc`,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != tempInfo">
                temp_info,
            </if>
            <if test="null != isPublic">
                `public`,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != status">
                status
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != title">
                #{title},
            </if>
            <if test="null != titleConfig">
                #{titleConfig},
            </if>
            <if test="null != desc">
                #{desc},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != tempInfo">
                #{tempInfo},
            </if>
            <if test="null != isPublic">
                #{isPublic},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != status">
                #{status}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM template
        WHERE id = #{id}
    </delete>

    <update id="updateTemplate" parameterType="com.eu.api.domain.dao.TemplateDAO">
        UPDATE template
        <set>
            <if test="null != title">title = #{title},</if>
            <if test="null != titleConfig">title_config = #{titleConfig},</if>
            <if test="null != desc">`desc` = #{desc},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != tempInfo">temp_info = #{tempInfo},</if>
            <if test="null != isPublic">`public` = #{isPublic},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != status">status = #{status}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="getTemplate" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM template
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND `app_channel_id` = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND `data_channel_id` = #{condition.dataChannelId}
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="getTemplateList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM template
        <where>
            status = 1
            <if test="condition.isPublic > 0">
                AND `public` = #{condition.isPublic}
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND `app_channel_id` = #{condition.appChannelId}
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND `data_channel_id` = #{condition.dataChannelId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getPrivateTemplateList" resultMap="BaseResultMap">
        SELECT *
        FROM `template`
        WHERE `status` = 1 AND (`public` = 1 OR `data_channel_id` = #{dataChannelId})
        <if test="null != appChannelId and appChannelId != ''">
            AND `app_channel_id` = #{appChannelId}
        </if>
        ORDER BY `create_time` DESC
    </select>
</mapper>