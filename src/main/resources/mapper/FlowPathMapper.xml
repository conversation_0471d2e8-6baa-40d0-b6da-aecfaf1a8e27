<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="flow_id" property="flowId"/>
        <result column="flow_title" property="flowTitle"/>
        <result column="uuid" property="uuid"/>
        <result column="show_name" property="showName"/>
        <result column="avatar" property="avatar"/>
        <result column="temp_info" property="tempInfo"/>
        <result column="temp_data" property="tempData"/>
        <result column="temp_show_data" property="tempShowData"/>
        <result column="step_info" property="stepInfo"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="refuse_url" property="refuseUrl"/>
        <result column="pass_url" property="passUrl"/>
        <result column="approver_url" property="approverUrl"/>
        <result column="cc_create" property="ccCreate"/>
        <result column="withdraw" property="withdraw"/>
        <result column="reject_must_alert" property="rejectMustAlert"/>
        <result column="freedom" property="freedom"/>
        <result column="countersign" property="countersign"/>
        <result column="return_back" property="returnBack"/>
        <result column="autoappr" property="autoappr"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="back_approve" property="backApprove"/>
        <result column="extra_content" property="extraContent"/>
        <result column="is_interrupt" property="isInterrupt"/>
        <result column="withdraw_path_id" property="withdrawPathId"/>
        <result column="is_reject_notice" property="isRejectNotice"/>
        <result column="is_revoke_notice" property="isRevokeNotice"/>
        <result column="is_read_approve" property="isReadApprove"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        title,
        flow_id,
        flow_title,
        uuid,
        show_name,
        avatar,
        temp_info,
        temp_data,
        temp_show_data,
        step_info,
        app_channel_id,
        data_channel_id,
        refuse_url,
        pass_url,
        approver_url,
        cc_create,
        withdraw,
        reject_must_alert,
        freedom,
        countersign,
        return_back,
        autoappr,
        create_time,
        update_time,
        status,
        back_approve,
        extra_content,
        is_interrupt,
        withdraw_path_id,
        is_reject_notice,
        is_revoke_notice,
        is_read_approve
    </sql>

    <insert id="addFlowPath" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowPathDAO">
        INSERT INTO flow_path
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != title">
                title,
            </if>
            <if test="null != flowId">
                flow_id,
            </if>
            <if test="null != flowTitle">
                flow_title,
            </if>
            <if test="null != uuid">
                uuid,
            </if>
            <if test="null != showName">
                show_name,
            </if>
            <if test="null != avatar">
                avatar,
            </if>
            <if test="null != tempInfo">
                temp_info,
            </if>
            <if test="null != tempData">
                temp_data,
            </if>
            <if test="null != tempShowData">
                temp_show_data,
            </if>
            <if test="null != stepInfo">
                step_info,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != refuseUrl">
                refuse_url,
            </if>
            <if test="null != passUrl">
                pass_url,
            </if>
            <if test="null != approverUrl">
                approver_url,
            </if>
            <if test="null != ccCreate">
                cc_create,
            </if>
            <if test="null != withdraw">
                withdraw,
            </if>
            <if test="null != rejectMustAlert">
                reject_must_alert,
            </if>
            <if test="null != freedom">
                freedom,
            </if>
            <if test="null != countersign">
                countersign,
            </if>
            <if test="null != returnBack">
                return_back,
            </if>
            <if test="null != autoappr">
                autoappr,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != status">
                status,
            </if>
            <if test="null != backApprove">
                back_approve,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != isInterrupt">
                is_interrupt,
            </if>
            <if test="null != withdrawPathId">
                withdraw_path_id,
            </if>
            <if test="null != isRejectNotice">
                is_reject_notice,
            </if>
            <if test="null != isRevokeNotice">
                is_revoke_notice,
            </if>
            <if test="null != isRevokeNotice">
                is_read_approve
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != title">
                #{title},
            </if>
            <if test="null != flowId">
                #{flowId},
            </if>
            <if test="null != flowTitle">
                #{flowTitle},
            </if>
            <if test="null != uuid">
                #{uuid},
            </if>
            <if test="null != showName">
                #{showName},
            </if>
            <if test="null != avatar">
                #{avatar},
            </if>
            <if test="null != tempInfo">
                #{tempInfo},
            </if>
            <if test="null != tempData">
                #{tempData},
            </if>
            <if test="null != tempShowData">
                #{tempShowData},
            </if>
            <if test="null != stepInfo">
                #{stepInfo},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != refuseUrl">
                #{refuseUrl},
            </if>
            <if test="null != passUrl">
                #{passUrl},
            </if>
            <if test="null != approverUrl">
                #{approverUrl},
            </if>
            <if test="null != ccCreate">
                #{ccCreate},
            </if>
            <if test="null != withdraw">
                #{withdraw},
            </if>
            <if test="null != rejectMustAlert">
                #{rejectMustAlert},
            </if>
            <if test="null != freedom">
                #{freedom},
            </if>
            <if test="null != countersign">
                #{countersign},
            </if>
            <if test="null != returnBack">
                #{returnBack},
            </if>
            <if test="null != autoappr">
                #{autoappr},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != status">
                #{status},
            </if>
            <if test="null != backApprove">
                #{backApprove},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != isInterrupt">
                #{isInterrupt},
            </if>
            <if test="null != withdrawPathId">
                #{withdrawPathId},
            </if>
            <if test="null != isRejectNotice">
                #{isRejectNotice},
            </if>
            <if test="null != isRevokeNotice">
                #{isRevokeNotice},
            </if>
            <if test="null != isReadApprove">
                #{isReadApprove}
            </if>
        </trim>
    </insert>

    <delete id="deleteFlowPath">
        DELETE
        FROM flow_path
        WHERE id = #{id}
    </delete>

    <update id="updateFlowPath" parameterType="com.eu.api.domain.dao.FlowPathDAO">
        UPDATE flow_path
        <set>
            <if test="null != title">title = #{title},</if>
            <if test="null != flowId">flow_id = #{flowId},</if>
            <if test="null != flowTitle">flow_title = #{flowTitle},</if>
            <if test="null != uuid">uuid = #{uuid},</if>
            <if test="null != showName">show_name = #{showName},</if>
            <if test="null != avatar">avatar = #{avatar},</if>
            <if test="null != tempInfo">temp_info = #{tempInfo},</if>
            <if test="null != tempData">temp_data = #{tempData},</if>
            <if test="null != tempShowData">temp_show_data = #{tempShowData},</if>
            <if test="null != stepInfo">step_info = #{stepInfo},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != refuseUrl">refuse_url = #{refuseUrl},</if>
            <if test="null != passUrl">pass_url = #{passUrl},</if>
            <if test="null != approverUrl">approver_url = #{approverUrl},</if>
            <if test="null != ccCreate">cc_create = #{ccCreate},</if>
            <if test="null != withdraw">withdraw = #{withdraw},</if>
            <if test="null != rejectMustAlert">reject_must_alert = #{rejectMustAlert},</if>
            <if test="null != freedom">freedom = #{freedom},</if>
            <if test="null != countersign">countersign = #{countersign},</if>
            <if test="null != returnBack">return_back = #{returnBack},</if>
            <if test="null != autoappr">autoappr = #{autoappr},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != status">status = #{status},</if>
            <if test="null != backApprove">back_approve = #{backApprove},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != isInterrupt">is_interrupt = #{isInterrupt},</if>
            <if test="null != withdrawPathId">withdraw_path_id = #{withdrawPathId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="batchUpdateFlowPaths" parameterType="java.util.List">
        <if test="list != null and list.size() > 0">
            UPDATE flow_path
            <set>
                title = CASE id
                <foreach collection="list" item="item">
                    <if test="item.title != null">
                        WHEN #{item.id} THEN #{item.title}
                    </if>
                </foreach>
                ELSE title END,

                flow_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.flowId != null">
                        WHEN #{item.id} THEN #{item.flowId}
                    </if>
                </foreach>
                ELSE flow_id END,

                flow_title = CASE id
                <foreach collection="list" item="item">
                    <if test="item.flowTitle != null">
                        WHEN #{item.id} THEN #{item.flowTitle}
                    </if>
                </foreach>
                ELSE flow_title END,

                uuid = CASE id
                <foreach collection="list" item="item">
                    <if test="item.uuid != null">
                        WHEN #{item.id} THEN #{item.uuid}
                    </if>
                </foreach>
                ELSE uuid END,

                show_name = CASE id
                <foreach collection="list" item="item">
                    <if test="item.showName != null">
                        WHEN #{item.id} THEN #{item.showName}
                    </if>
                </foreach>
                ELSE show_name END,

                avatar = CASE id
                <foreach collection="list" item="item">
                    <if test="item.avatar != null">
                        WHEN #{item.id} THEN #{item.avatar}
                    </if>
                </foreach>
                ELSE avatar END,

                temp_info = CASE id
                <foreach collection="list" item="item">
                    <if test="item.tempInfo != null">
                        WHEN #{item.id} THEN #{item.tempInfo}
                    </if>
                </foreach>
                ELSE temp_info END,

                temp_data = CASE id
                <foreach collection="list" item="item">
                    <if test="item.tempData != null">
                        WHEN #{item.id} THEN #{item.tempData}
                    </if>
                </foreach>
                ELSE temp_data END,

                temp_show_data = CASE id
                <foreach collection="list" item="item">
                    <if test="item.tempShowData != null">
                        WHEN #{item.id} THEN #{item.tempShowData}
                    </if>
                </foreach>
                ELSE temp_show_data END,

                step_info = CASE id
                <foreach collection="list" item="item">
                    <if test="item.stepInfo != null">
                        WHEN #{item.id} THEN #{item.stepInfo}
                    </if>
                </foreach>
                ELSE step_info END,

                app_channel_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.appChannelId != null">
                        WHEN #{item.id} THEN #{item.appChannelId}
                    </if>
                </foreach>
                ELSE app_channel_id END,

                data_channel_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.dataChannelId != null">
                        WHEN #{item.id} THEN #{item.dataChannelId}
                    </if>
                </foreach>
                ELSE data_channel_id END,

                refuse_url = CASE id
                <foreach collection="list" item="item">
                    <if test="item.refuseUrl != null">
                        WHEN #{item.id} THEN #{item.refuseUrl}
                    </if>
                </foreach>
                ELSE refuse_url END,

                pass_url = CASE id
                <foreach collection="list" item="item">
                    <if test="item.passUrl != null">
                        WHEN #{item.id} THEN #{item.passUrl}
                    </if>
                </foreach>
                ELSE pass_url END,

                approver_url = CASE id
                <foreach collection="list" item="item">
                    <if test="item.approverUrl != null">
                        WHEN #{item.id} THEN #{item.approverUrl}
                    </if>
                </foreach>
                ELSE approver_url END,

                cc_create = CASE id
                <foreach collection="list" item="item">
                    <if test="item.ccCreate != null">
                        WHEN #{item.id} THEN #{item.ccCreate}
                    </if>
                </foreach>
                ELSE cc_create END,

                withdraw = CASE id
                <foreach collection="list" item="item">
                    <if test="item.withdraw != null">
                        WHEN #{item.id} THEN #{item.withdraw}
                    </if>
                </foreach>
                ELSE withdraw END,

                reject_must_alert = CASE id
                <foreach collection="list" item="item">
                    <if test="item.rejectMustAlert != null">
                        WHEN #{item.id} THEN #{item.rejectMustAlert}
                    </if>
                </foreach>
                ELSE reject_must_alert END,

                freedom = CASE id
                <foreach collection="list" item="item">
                    <if test="item.freedom != null">
                        WHEN #{item.id} THEN #{item.freedom}
                    </if>
                </foreach>
                ELSE freedom END,

                countersign = CASE id
                <foreach collection="list" item="item">
                    <if test="item.countersign != null">
                        WHEN #{item.id} THEN #{item.countersign}
                    </if>
                </foreach>
                ELSE countersign END,

                return_back = CASE id
                <foreach collection="list" item="item">
                    <if test="item.returnBack != null">
                        WHEN #{item.id} THEN #{item.returnBack}
                    </if>
                </foreach>
                ELSE return_back END,

                autoappr = CASE id
                <foreach collection="list" item="item">
                    <if test="item.autoappr != null">
                        WHEN #{item.id} THEN #{item.autoappr}
                    </if>
                </foreach>
                ELSE autoappr END,

                status = CASE id
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        WHEN #{item.id} THEN #{item.status}
                    </if>
                </foreach>
                ELSE status END,

                back_approve = CASE id
                <foreach collection="list" item="item">
                    <if test="item.backApprove != null">
                        WHEN #{item.id} THEN #{item.backApprove}
                    </if>
                </foreach>
                ELSE status END,

                extra_content = CASE id
                <foreach collection="list" item="item">
                    <if test="item.extraContent != null">
                        WHEN #{item.id} THEN #{item.extraContent}
                    </if>
                </foreach>
                ELSE extra_content END,

                is_interrupt = CASE id
                <foreach collection="list" item="item">
                    <if test="item.isInterrupt != null">
                        WHEN #{item.id} THEN #{item.isInterrupt}
                    </if>
                </foreach>
                ELSE is_interrupt END,

                withdraw_path_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.withdrawPathId != null">
                        WHEN #{item.id} THEN #{item.withdrawPathId}
                    </if>
                </foreach>
                ELSE withdraw_path_id END

            </set>

            WHERE id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>

        </if>
    </update>

    <select id="getFlowPath" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
        </where>
    </select>

    <select id="getFlowPathList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
            <if test="null != condition.idList and !condition.idList.isEmpty()">
                <foreach collection="condition.idList" item="id" open="AND id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
                <foreach collection="condition.flowIdList" item="flowId" open="AND flow_id IN (" close=")"
                         separator=",">
                    #{flowId}
                </foreach>
            </if>
            <if test="null != condition.companyId and condition.companyId != ''">
                AND extra_content ->> '$.companyId' = #{condition.companyId}
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.status">
                AND status = #{condition.status}
            </if>
            <if test="null != condition.initiatorUserIdList and !condition.initiatorUserIdList.isEmpty()">
                <foreach collection="condition.initiatorUserIdList" item="uuid" open="AND uuid IN (" close=")" separator=",">
                    #{uuid}
                </foreach>
            </if>
            <if test="null != condition.createStartTime and null != condition.createEndTime">
                AND create_time BETWEEN #{condition.createStartTime} AND #{condition.createEndTime}
            </if>
            <if test="null != condition.updateStartTime and null != condition.updateEndTime">
                AND update_time BETWEEN #{condition.updateStartTime} AND #{condition.updateEndTime}
            </if>
            <if test="null != condition.isInterrupt">
                AND is_interrupt = #{condition.isInterrupt}
            </if>
            <if test="null != condition.withdrawPathId">
                AND withdraw_path_id = #{condition.withdrawPathId}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <!--减少字段查询-->
    <select id="getFlowPathListTwo" resultMap="BaseResultMap">
        SELECT
        id,
        uuid,
        avatar,
        create_time,
        temp_info,
        temp_data,
        temp_show_data,
        status,
        is_interrupt,
        withdraw_path_id
        FROM
        flow_path
        WHERE
        id IN (
        SELECT
        id
        FROM
        flow_path
        WHERE
        1=1
        <if test="null != condition.id">
            AND id = #{condition.id}
        </if>
        <if test="null != condition.idList and !condition.idList.isEmpty()">
            AND id IN
            <foreach collection="condition.idList" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
            AND flow_id IN
            <foreach collection="condition.flowIdList" item="flowId" open="(" close=")" separator=",">
                #{flowId}
            </foreach>
        </if>
        <if test="null != condition.companyId and condition.companyId != ''">
            AND extra_content ->> '$.companyId' = #{condition.companyId}
        </if>
        <if test="null != condition.title and condition.title != ''">
            AND title LIKE CONCAT('%', #{condition.title}, '%')
        </if>
        <if test="null != condition.status">
            AND status = #{condition.status}
        </if>
        <if test="null != condition.initiatorUserIdList and !condition.initiatorUserIdList.isEmpty()">
            AND uuid IN
            <foreach collection="condition.initiatorUserIdList" item="uuid" open="(" close=")" separator=",">
                #{uuid}
            </foreach>
        </if>
        <if test="null != condition.createStartTime and null != condition.createEndTime">
            AND create_time BETWEEN #{condition.createStartTime} AND #{condition.createEndTime}
        </if>
        <if test="null != condition.updateStartTime and null != condition.updateEndTime">
            AND update_time BETWEEN #{condition.updateStartTime} AND #{condition.updateEndTime}
        </if>
        <if test="null != condition.isInterrupt">
            AND is_interrupt = #{condition.isInterrupt}
        </if>
        <if test="null != condition.withdrawPathId">
            AND withdraw_path_id = #{condition.withdrawPathId}
        </if>
        ORDER BY
        create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
        )
    </select>
    <select id="getFlowPathCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM flow_path
        <where>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
            <if test="null != condition.idList and !condition.idList.isEmpty()">
                <foreach collection="condition.idList" item="id" open="AND id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
                <foreach collection="condition.flowIdList" item="flowId" open="AND flow_id IN (" close=")" separator=",">
                    #{flowId}
                </foreach>
            </if>
            <if test="null != condition.companyId and condition.companyId != ''">
                AND extra_content ->> '$.companyId' = #{condition.companyId}
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.status">
                AND status = #{condition.status}
            </if>
            <if test="null != condition.initiatorUserIdList and !condition.initiatorUserIdList.isEmpty()">
                <foreach collection="condition.initiatorUserIdList" item="uuid" open="AND uuid IN (" close=")"
                         separator=",">
                    #{uuid}
                </foreach>
            </if>
            <if test="null != condition.createStartTime and null != condition.createEndTime">
                AND create_time BETWEEN #{condition.createStartTime} AND #{condition.createEndTime}
            </if>
            <if test="null != condition.updateStartTime and null != condition.updateEndTime">
                AND update_time BETWEEN #{condition.updateStartTime} AND #{condition.updateEndTime}
            </if>
            <if test="null != condition.isInterrupt">
                AND is_interrupt = #{condition.isInterrupt}
            </if>
            <if test="null != condition.withdrawPathId">
                AND withdraw_path_id = #{condition.withdrawPathId}
            </if>
        </where>
    </select>
    <select id="getFlowPathBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        flow_path
        <if test="list != null and !list.isEmpty()">
            WHERE id IN
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="list == null or list.isEmpty()">
            WHERE 1 = 0
        </if>
    </select>

    <select id="getFlowPathListBatch" resultType="com.eu.api.domain.dao.FlowPathDAO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path
        WHERE 1=1
        <foreach collection="list" item="condition" index="index" open="" close="" separator="OR">
            (
            <if test="condition.id != null">
                AND id = #{condition.id}
            </if>
            <if test="condition.idList != null and condition.idList.size() > 0">
                AND id IN
                <foreach collection="condition.idList" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.flowIdList != null and condition.flowIdList.size() > 0">
                AND flow_id IN
                <foreach collection="condition.flowIdList" item="flowId" open="(" close=")" separator=",">
                    #{flowId}
                </foreach>
            </if>
            <if test="condition.companyId != null and condition.companyId != ''">
                AND extra_content ->> '$.companyId' = #{condition.companyId}
            </if>
            <if test="condition.appChannelId != null and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="condition.dataChannelId != null and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="condition.title != null and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="condition.status != null">
                AND status = #{condition.status}
            </if>
            <if test="condition.initiatorUserIdList != null and condition.initiatorUserIdList.size() > 0">
                AND uuid IN
                <foreach collection="condition.initiatorUserIdList" item="uuid" open="(" close=")" separator=",">
                    #{uuid}
                </foreach>
            </if>
            <if test="condition.createStartTime != null and condition.createEndTime != null">
                AND create_time BETWEEN #{condition.createStartTime} AND #{condition.createEndTime}
            </if>
            <if test="condition.updateStartTime != null and condition.updateEndTime != null">
                AND update_time BETWEEN #{condition.updateStartTime} AND #{condition.updateEndTime}
            </if>
            <if test="condition.isInterrupt != null">
                AND is_interrupt = #{condition.isInterrupt}
            </if>
            <if test="condition.withdrawPathId != null">
                AND withdraw_path_id = #{condition.withdrawPathId}
            </if>
            )
        </foreach>
        ORDER BY create_time DESC
    </select>

    <select id="selectPathListByDelegationId" resultType="com.eu.api.domain.dao.FlowPathDelegationDAO">
        SELECT
            fp.flow_id,
            fp.uuid,
            fpl.id,
            fpl.extra_content,
            fpl.agent_expiration_time
        FROM
            flow_path fp
                left JOIN flow_path_list fpl ON fp.id = fpl.flow_path_id
        WHERE
            fpl.type = #{type}
          AND fpl.extra_content->>'$.delegationId' = #{delegationId}
          AND fp.status = #{status}
    </select>

    <select id="selectNotificationByDelegationId" resultType="com.eu.api.domain.dao.FlowPathDelegationDAO">
        SELECT
            fp.flow_id,
            fp.uuid,
            fpsn.id,
            fpsn.extra_content,
            fpsn.agent_expiration_time
        FROM
            flow_path fp
                left JOIN flow_path_step_notification fpsn ON fp.id = fpsn.flow_path_id
        WHERE
            fpsn.extra_content->>'$.delegationId' = #{delegationId}
          AND fpsn.status = #{status}
    </select>
</mapper>