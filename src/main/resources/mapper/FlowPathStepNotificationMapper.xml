<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathStepNotificationMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathStepNotificationDAO">
        <result column="id" property="id"/>
        <result column="flow_path_id" property="flowPathId"/>
        <result column="step_no" property="stepNo"/>
        <result column="sort" property="sort"/>
        <result column="uuid" property="uuid"/>
        <result column="extra_content" property="extraContent"/>
        <result column="type" property="type"/>
        <result column="reason" property="reason"/>
        <result column="status" property="status"/>
        <result column="is_countersign" property="isCountersign"/>
        <result column="from_notification_id" property="fromNotificationId"/>
        <result column="target_notification_id" property="targetNotificationId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="agent_expiration_time" property="agentExpirationTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        flow_path_id,
        step_no,
        sort,
        uuid,
        extra_content,
        `type`,
        reason,
        status,
        is_countersign,
        from_notification_id,
        target_notification_id,
        create_time,
        update_time,
        agent_expiration_time
    </sql>

    <insert id="addFlowPathStepNotification" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowPathStepNotificationDAO">
        INSERT INTO flow_path_step_notification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != flowPathId"> flow_path_id,</if>
            <if test="null != stepNo"> step_no,</if>
            <if test="null != sort">sort,</if>
            <if test="null != uuid">uuid, </if>
            <if test="null != extraContent"> extra_content,</if>
            <if test="null != type">type,</if>
            <if test="null != reason">reason,</if>
            <if test="null != status">status,</if>
            <if test="null != isCountersign">is_countersign,</if>
            <if test="null != fromNotificationId">from_notification_id,</if>
            <if test="null != targetNotificationId">target_notification_id, </if>
            <if test="null != createTime">create_time,</if>
            <if test="null != updateTime">update_time,</if>
            <if test="null != agentExpirationTime">agent_expiration_time</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != flowPathId">#{flowPathId},</if>
            <if test="null != stepNo"> #{stepNo},</if>
            <if test="null != sort">#{sort},</if>
            <if test="null != uuid"> #{uuid},</if>
            <if test="null != extraContent">#{extraContent},</if>
            <if test="null != type"> #{type},</if>
            <if test="null != reason">#{reason},</if>
            <if test="null != status"> #{status},</if>
            <if test="null != isCountersign"> #{isCountersign},</if>
            <if test="null != fromNotificationId">#{fromNotificationId},</if>
            <if test="null != targetNotificationId">#{targetNotificationId},</if>
            <if test="null != createTime">#{createTime},</if>
            <if test="null != updateTime">#{updateTime},</if>
            <if test="null != agentExpirationTime">#{agentExpirationTime}</if>
        </trim>
    </insert>

    <delete id="deleteId">
        DELETE
        FROM flow_path_step_notification
        WHERE id = #{id}
    </delete>

    <delete id="deleteByFlowPathIdAndStepNo" parameterType="integer">
        DELETE FROM flow_path_step_notification WHERE `flow_path_id` = #{flowPathId} AND `step_no` = #{stepNo}
        <if test="null != typeList and typeList.size() > 0">
            <foreach collection="typeList" item="type" open="AND type IN (" close=")" separator=",">
                #{type}
            </foreach>
        </if>
    </delete>

    <delete id="deleteByFlowPathIdAndStepNoScope" parameterType="integer">
        DELETE FROM flow_path_step_notification WHERE `flow_path_id` = #{flowPathId} AND `step_no` &gt; #{stepNo}
        <if test="null != typeList and typeList.size() > 0">
            <foreach collection="typeList" item="type" open="AND type IN (" close=")" separator=",">
                #{type}
            </foreach>
        </if>
    </delete>

    <update id="update" parameterType="com.eu.api.domain.dao.FlowPathStepNotificationDAO">
        <if test="null != condition and null != setData">
            UPDATE flow_path_step_notification
            <set>
                <if test="null != setData.flowPathId">flow_path_id = #{setData.flowPathId},</if>
                <if test="null != setData.stepNo">step_no = #{setData.stepNo},</if>
                <if test="null != setData.sort">sort = #{setData.sort},</if>
                <if test="null != setData.uuid">uuid = #{setData.uuid},</if>
                <if test="null != setData.extraContent">extra_content = #{setData.extraContent},</if>
                <if test="null != setData.type">type = #{setData.type},</if>
                <if test="null != setData.reason">reason = #{setData.reason},</if>
                <if test="null != setData.status">status = #{setData.status},</if>
                <if test="null != setData.isCountersign">is_countersign = #{setData.isCountersign},</if>
                <if test="null != setData.fromNotificationId">from_notification_id = #{setData.fromNotificationId},</if>
                <if test="null != setData.targetNotificationId">target_notification_id = #{setData.targetNotificationId},</if>
                <if test="null != setData.createTime">create_time = #{setData.createTime},</if>
                <if test="null != setData.updateTime">update_time = #{setData.updateTime}</if>
                <if test="null != setData.agentExpirationTime">agent_expiration_time = #{setData.agentExpirationTime}</if>
            </set>
            <where>
                <if test="null != condition.id">AND id = #{condition.id}</if>
                <if test="null != condition.flowPathId">AND flow_path_id = #{condition.flowPathId}</if>
                <if test="null != condition.status">AND status = #{condition.status}</if>
                <if test="null != condition.stepNo">AND step_no = #{condition.stepNo}</if>
                <if test="null != condition.type">AND type = #{condition.type}</if>
                <if test="null != condition.uuid and condition.uuid != ''">AND uuid = #{condition.uuid}</if>
                <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                    <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                        AND extra_content -> '$.${key}' = #{val}
                    </foreach>
                </if>
            </where>
        </if>
    </update>
    <update id="batchUpdateNotifications">
        <if test="list != null and !list.isEmpty()">
            UPDATE flow_path_step_notification
            <set>
                flow_path_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.flowPathId != null">
                        WHEN #{item.id} THEN #{item.flowPathId}
                    </if>
                </foreach>
                ELSE flow_path_id END,

                step_no = CASE id
                <foreach collection="list" item="item">
                    <if test="item.stepNo != null">
                        WHEN #{item.id} THEN #{item.stepNo}
                    </if>
                </foreach>
                ELSE step_no END,

                sort = CASE id
                <foreach collection="list" item="item">
                    <if test="item.sort != null">
                        WHEN #{item.id} THEN #{item.sort}
                    </if>
                </foreach>
                ELSE sort END,

                uuid = CASE id
                <foreach collection="list" item="item">
                    <if test="item.uuid != null">
                        WHEN #{item.id} THEN #{item.uuid}
                    </if>
                </foreach>
                ELSE uuid END,

                extra_content = CASE id
                <foreach collection="list" item="item">
                    <if test="item.extraContent != null">
                        WHEN #{item.id} THEN #{item.extraContent}
                    </if>
                </foreach>
                ELSE extra_content END,

                `type` = CASE id  <foreach collection="list" item="item">
                <if test="item.type != null">
                    WHEN #{item.id} THEN #{item.type}
                </if>
            </foreach>
                ELSE `type` END,

                reason = CASE id
                <foreach collection="list" item="item">
                    <if test="item.reason != null">
                        WHEN #{item.id} THEN #{item.reason}
                    </if>
                </foreach>
                ELSE reason END,

                status = CASE id
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        WHEN #{item.id} THEN #{item.status}
                    </if>
                </foreach>
                ELSE status END,

                is_countersign = CASE id
                <foreach collection="list" item="item">
                    <if test="item.isCountersign != null">
                        WHEN #{item.id} THEN #{item.isCountersign}
                    </if>
                </foreach>
                ELSE is_countersign END,

                from_notification_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.fromNotificationId != null">
                        WHEN #{item.id} THEN #{item.fromNotificationId}
                    </if>
                </foreach>
                ELSE from_notification_id END,

                target_notification_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.targetNotificationId != null">
                        WHEN #{item.id} THEN #{item.targetNotificationId}
                    </if>
                </foreach>
                ELSE target_notification_id END,

                update_time = CASE id
                <foreach collection="list" item="item">
                    <if test="item.updateTime != null">
                        WHEN #{item.id} THEN #{item.updateTime}
                    </if>
                </foreach>
                ELSE update_time END
            </set>
            WHERE id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
    </update>


    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_step_notification
        WHERE id = #{id}
    </select>

    <select id="getFlowPathStepNotificationList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_step_notification
        <where>
            <if test="null != id">AND id = #{id}</if>
            <if test="null != flowPathId">AND flow_path_id = #{flowPathId}</if>
            <if test="null != flowPathIdList and !flowPathIdList.isEmpty()">
                <foreach collection="flowPathIdList" item="flowPathId" open="AND flow_path_id IN (" close=")"
                         separator=",">
                    #{flowPathId}
                </foreach>
            </if>
            <if test="null != stepNo">AND step_no = #{stepNo}</if>
            <if test="null != type">AND type = #{type}</if>
            <if test="null != typeList and !typeList.isEmpty()">
                <foreach collection="typeList" item="type" open="AND type IN (" close=")" separator=",">
                    #{type}
                </foreach>
            </if>
            <if test="null != status">AND status = #{status}</if>
            <if test="null != uuid and uuid != ''">AND uuid = #{uuid}</if>
            <if test="null != extraContentMap and extraContentMap.size() > 0">
                <foreach collection="extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
            AND agent_expiration_time > CURRENT_TIMESTAMP
        </where>
        ORDER BY sort ASC
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow_path_step_notification
        <where>
            <if test="null != flowPathId">
                AND flow_path_id = #{flowPathId}
            </if>
            <if test="null != stepNo">
                AND step_no = #{stepNo}
            </if>
            <if test="null != type">
                AND type = #{type}
            </if>
            <if test="null != status">
                AND status = #{status}
            </if>
            <if test="null != uuid and uuid != ''">
                AND uuid = #{uuid}
            </if>
            <if test="null != extraContentMap and extraContentMap.size() > 0">
                <foreach collection="extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </select>

</mapper>