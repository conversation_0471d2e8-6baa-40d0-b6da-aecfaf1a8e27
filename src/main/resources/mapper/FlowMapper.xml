<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="desc" property="desc"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="temp_id" property="tempId"/>
        <result column="refuse_url" property="refuseUrl"/>
        <result column="pass_url" property="passUrl"/>
        <result column="approver_url" property="approverUrl"/>
        <result column="cc_url" property="ccUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="cc_notification" property="ccNotification"/>
        <result column="cc_create" property="ccCreate"/>
        <result column="customize" property="customize"/>
        <result column="withdraw" property="withdraw"/>
        <result column="reject_must_alert" property="rejectMustAlert"/>
        <result column="freedom" property="freedom"/>
        <result column="abnormal" property="abnormal"/>
        <result column="countersign" property="countersign"/>
        <result column="return_back" property="returnBack"/>
        <result column="back_approve" property="backApprove"/>
        <result column="back_approve_notify" property="backApproveNotify"/>
        <result column="autoappr" property="autoappr"/>
        <result column="status" property="status"/>
        <result column="extra_content" property="extraContent"/>
         <result column="is_reject_notice" property="isRejectNotice"/>
         <result column="is_revoke_notice" property="isRevokeNotice"/>
         <result column="is_read_approve" property="isReadApprove"/>
    </resultMap>

    <resultMap id="BaseResultMap2" type="com.eu.api.domain.dao.FlowByListDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="desc" property="desc"/>
        <result column="app_channel_id" property="appChannelId"/>
        <result column="data_channel_id" property="dataChannelId"/>
        <result column="temp_id" property="tempId"/>
        <result column="refuse_url" property="refuseUrl"/>
        <result column="pass_url" property="passUrl"/>
        <result column="approver_url" property="approverUrl"/>
        <result column="cc_url" property="ccUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="cc_notification" property="ccNotification"/>
        <result column="cc_create" property="ccCreate"/>
        <result column="withdraw" property="withdraw"/>
        <result column="reject_must_alert" property="rejectMustAlert"/>
        <result column="freedom" property="freedom"/>
        <result column="abnormal" property="abnormal"/>
        <result column="countersign" property="countersign"/>
        <result column="return_back" property="returnBack"/>
        <result column="autoappr" property="autoappr"/>
        <result column="status" property="status"/>
        <result column="extra_content" property="extraContent"/>
        <result column="is_reject_notice" property="isRejectNotice"/>
        <result column="is_revoke_notice" property="isRevokeNotice"/>
        <result column="is_read_approve" property="isReadApprove"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        title,
        `desc`,
        app_channel_id,
        data_channel_id,
        temp_id,
        refuse_url,
        pass_url,
        approver_url,
        cc_url,
        create_time,
        update_time,
        cc_notification,
        cc_create,
        customize,
        withdraw,
        reject_must_alert,
        freedom,
        abnormal,
        countersign,
        return_back,
        back_approve,
        back_approve_notify,
        autoappr,
        `status`,
        extra_content,
        is_reject_notice,
        is_revoke_notice,
        is_read_approve
    </sql>

    <sql id="Base_Column_List_Alone">
        id,
        title,
        `desc`,
        app_channel_id,
        data_channel_id,
        temp_id,
        refuse_url,
        pass_url,
        approver_url,
        cc_url,
        create_time,
        update_time,
        cc_notification,
        cc_create,
        withdraw,
        reject_must_alert,
        freedom,
        abnormal,
        countersign,
        return_back,
        autoappr,
        `status`,
        extra_content,
        is_reject_notice,
        is_revoke_notice,
        is_read_approve
    </sql>

    <insert id="addFlow" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowDAO">
        INSERT INTO flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != title">
                title,
            </if>
            <if test="null != desc">
                `desc`,
            </if>
            <if test="null != appChannelId">
                app_channel_id,
            </if>
            <if test="null != dataChannelId">
                data_channel_id,
            </if>
            <if test="null != tempId">
                temp_id,
            </if>
            <if test="null != refuseUrl">
                refuse_url,
            </if>
            <if test="null != passUrl">
                pass_url,
            </if>
            <if test="null != approverUrl">
                approver_url,
            </if>
            <if test="null != ccUrl">
                cc_url,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != ccNotification">
                cc_notification,
            </if>
            <if test="null != ccCreate">
                cc_create,
            </if>
            <if test="null != customize">
                customize,
            </if>
            <if test="null != withdraw">
                withdraw,
            </if>
            <if test="null != rejectMustAlert">
                reject_must_alert,
            </if>
            <if test="null != freedom">
                freedom,
            </if>
            <if test="null != abnormal">
                abnormal,
            </if>
            <if test="null != countersign">
                countersign,
            </if>
            <if test="null != returnBack">
                return_back,
            </if>
            <if test="null != backApprove">
                back_approve,
            </if>
            <if test="null != backApproveNotify">
                back_approve_notify,
            </if>
            <if test="null != autoappr">
                autoappr,
            </if>
            <if test="null != status">
                status,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != isRejectNotice">
                is_reject_notice,
            </if>
            <if test="null != isRevokeNotice">
                is_revoke_notice,
            </if>
            <if test="null != isReadApprove">
                is_read_approve
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != title">
                #{title},
            </if>
            <if test="null != desc">
                #{desc},
            </if>
            <if test="null != appChannelId">
                #{appChannelId},
            </if>
            <if test="null != dataChannelId">
                #{dataChannelId},
            </if>
            <if test="null != tempId">
                #{tempId},
            </if>
            <if test="null != refuseUrl">
                #{refuseUrl},
            </if>
            <if test="null != passUrl">
                #{passUrl},
            </if>
            <if test="null != approverUrl">
                #{approverUrl},
            </if>
            <if test="null != ccUrl">
                #{ccUrl},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != ccNotification">
                #{ccNotification},
            </if>
            <if test="null != ccCreate">
                #{ccCreate},
            </if>
            <if test="null != customize">
                #{customize},
            </if>
            <if test="null != withdraw">
                #{withdraw},
            </if>
            <if test="null != rejectMustAlert">
                #{rejectMustAlert},
            </if>
            <if test="null != freedom">
                #{freedom},
            </if>
            <if test="null != abnormal">
                #{abnormal},
            </if>
            <if test="null != countersign">
                #{countersign},
            </if>
            <if test="null != returnBack">
                #{returnBack},
            </if>
            <if test="null != backApprove">
                #{backApprove},
            </if>
            <if test="null != backApproveNotify">
                #{backApproveNotify},
            </if>
            <if test="null != autoappr">
                #{autoappr},
            </if>
            <if test="null != status">
                #{status},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != isRejectNotice">
                #{isRejectNotice},
            </if>
            <if test="null != isRevokeNotice">
                #{isRevokeNotice},
            </if>
            <if test="null != isReadApprove">
                #{isReadApprove}
            </if>
        </trim>
    </insert>

    <delete id="deleteFlow">
        DELETE
        FROM flow
        WHERE id = #{id}
    </delete>

    <update id="updateFlow" parameterType="com.eu.api.domain.dao.FlowDAO">
        UPDATE flow
        <set>
            <if test="null != title">title = #{title},</if>
            <if test="null != desc">`desc` = #{desc},</if>
            <if test="null != appChannelId">app_channel_id = #{appChannelId},</if>
            <if test="null != dataChannelId">data_channel_id = #{dataChannelId},</if>
            <if test="null != tempId">temp_id = #{tempId},</if>
            <if test="null != refuseUrl">refuse_url = #{refuseUrl},</if>
            <if test="null != passUrl">pass_url = #{passUrl},</if>
            <if test="null != approverUrl">approver_url = #{approverUrl},</if>
            <if test="null != ccUrl">cc_url = #{ccUrl},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != ccNotification">cc_notification = #{ccNotification},</if>
            <if test="null != ccCreate">cc_create = #{ccCreate},</if>
            <if test="null != customize">customize = #{customize},</if>
            <if test="null != withdraw">withdraw = #{withdraw},</if>
            <if test="null != rejectMustAlert">reject_must_alert = #{rejectMustAlert},</if>
            <if test="null != freedom">freedom = #{freedom},</if>
            <if test="null != abnormal">abnormal = #{abnormal},</if>
            <if test="null != countersign">countersign = #{countersign},</if>
            <if test="null != returnBack">return_back = #{returnBack},</if>
            <if test="null != backApprove">back_approve = #{backApprove},</if>
            <if test="null != backApproveNotify">back_approve_notify = #{backApproveNotify},</if>
            <if test="null != autoappr">autoappr = #{autoappr},</if>
            <if test="null != status">status = #{status},</if>
            <if test="null != extraContent">extra_content = #{extraContent}</if>
        </set>
        WHERE id = #{id}
    </update>


    <select id="getFlow" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow
        <where>
            <if test="null != flowId">
                AND id = #{flowId}
            </if>
        </where>
        LIMIT 1
    </select>

    <select id="getFlowList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow
        <where>
            <if test="null != condition.flowId and '' != condition.flowId">
                AND id = #{condition.flowId}
            </if>
            <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
                <foreach collection="condition.flowIdList" item="id" open="AND id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.titleList and !condition.titleList.isEmpty()">
                <foreach collection="condition.titleList" item="title" open="AND title IN (" close=")" separator=",">
                    #{title}
                </foreach>
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.tempId and condition.tempId != ''">
                AND temp_id = #{condition.tempId}
            </if>
            <if test="null != condition.tempIdList and !condition.tempIdList.isEmpty()">
                <foreach collection="condition.tempIdList" item="id" open="AND temp_id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.freedom">
                AND freedom = #{condition.freedom}
            </if>
            <if test="null != condition.statusList and !condition.statusList.isEmpty()">
                <foreach collection="condition.statusList" item="status" open="AND status IN (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getFlowListByList" resultMap="BaseResultMap2">
        SELECT
        <include refid="Base_Column_List_Alone"/>
        FROM flow
        <where>
            <if test="null != condition.flowId and condition.flowId != ''">
                AND id = #{condition.flowId}
            </if>
            <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
                <foreach collection="condition.flowIdList" item="id" open="AND id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.titleList and !condition.titleList.isEmpty()">
                <foreach collection="condition.titleList" item="title" open="AND title IN (" close=")" separator=",">
                    #{title}
                </foreach>
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.tempId and condition.tempId != ''">
                AND temp_id = #{condition.tempId}
            </if>
            <if test="null != condition.tempIdList and !condition.tempIdList.isEmpty()">
                <foreach collection="condition.tempIdList" item="id" open="AND temp_id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.freedom">
                AND freedom = #{condition.freedom}
            </if>
            <if test="null != condition.statusList and !condition.statusList.isEmpty()">
                <foreach collection="condition.statusList" item="status" open="AND status IN (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content ->> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getFlowCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow
        <where>
            <if test="null != condition.flowId and condition.flowId != ''">
                AND id = #{condition.flowId}
            </if>
            <if test="null != condition.flowIdList and !condition.flowIdList.isEmpty()">
                <foreach collection="condition.flowIdList" item="id" open="AND id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.dataChannelId and condition.dataChannelId != ''">
                AND data_channel_id = #{condition.dataChannelId}
            </if>
            <if test="null != condition.title and condition.title != ''">
                AND title LIKE CONCAT('%', #{condition.title}, '%')
            </if>
            <if test="null != condition.appChannelId and condition.appChannelId != ''">
                AND app_channel_id = #{condition.appChannelId}
            </if>
            <if test="null != condition.tempId and condition.tempId != ''">
                AND temp_id = #{condition.tempId}
            </if>
            <if test="null != condition.tempIdList and !condition.tempIdList.isEmpty()">
                <foreach collection="condition.tempIdList" item="id" open="AND temp_id IN (" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="null != condition.freedom">
                AND freedom = #{condition.freedom}
            </if>
            <if test="null != condition.statusList and !condition.statusList.isEmpty()">
                <foreach collection="condition.statusList" item="status" open="AND status IN (" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="null != condition.extraContentMap and condition.extraContentMap.size() > 0">
                <foreach collection="condition.extraContentMap.entrySet()" index="key" item="val">
                    AND extra_content -> '$.${key}' = #{val}
                </foreach>
            </if>
        </where>
    </select>

</mapper>