<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.StepMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.StepDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="flow_id" property="flowId"/>
        <result column="pass_cons" property="passCons"/>
        <result column="notification" property="notification"/>
        <result column="cc_notification" property="ccNotification"/>
        <result column="pass_all" property="passAll"/>
        <result column="withdraw" property="withdraw"/>
        <result column="goback" property="goback"/>
        <result column="extra_content" property="extraContent"/>
        <result column="step_id" property="stepId"/>
        <result column="src_id" property="srcId"/>
        <result column="next_id" property="nextId"/>
        <result column="conditions" property="conditions"/>
        <result column="weight" property="weight"/>
        <result column="type" property="type"/>
        <result column="step_type" property="stepType"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,flow_id, pass_cons,notification,cc_notification,pass_all,withdraw,goback,extra_content,step_id,src_id,next_id,conditions,weight,step_type,`type`
    </sql>

    <insert id="addStep" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.eu.api.domain.dao.StepDAO">
        INSERT INTO step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != title">title,</if>
            <if test="null != flowId">flow_id,</if>
            <if test="null != passCons">pass_cons,</if>
            <if test="null != notification">notification,</if>
            <if test="null != ccNotification">cc_notification,</if>
            <if test="null != passAll">pass_all,</if>
            <if test="null != withdraw">withdraw,</if>
            <if test="null != goback">goback,</if>
            <if test="null != extraContent">extra_content,</if>
            <if test="null != stepId">step_id,</if>
            <if test="null != srcId">src_id,</if>
            <if test="null != conditions">conditions,</if>
            <if test="null != weight">weight,</if>
            <if test="null != stepType">step_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != title">#{title},</if>
            <if test="null != flowId">#{flowId},</if>
            <if test="null != passCons"> #{passCons},</if>
            <if test="null != notification">#{notification},</if>
            <if test="null != ccNotification">#{ccNotification},</if>
            <if test="null != passAll">#{passAll},</if>
            <if test="null != withdraw">#{withdraw},</if>
            <if test="null != goback">#{goback},</if>
            <if test="null != extraContent">#{extraContent},</if>
            <if test="null != stepId">#{stepId},</if>
            <if test="null != srcId">#{srcId},</if>
            <if test="null != conditions">#{conditions},</if>
            <if test="null != weight">#{weight},</if>
            <if test="null != stepType">#{stepType},</if>
        </trim>
    </insert>

    <delete id="deleteStep">
        DELETE
        FROM step
        WHERE id = #{id}
    </delete>

    <delete id="deleteByFlowId">
        DELETE
        FROM step
        WHERE flow_id = #{flowId}
    </delete>

    <update id="updateStep" parameterType="com.eu.api.domain.dao.StepDAO">
        UPDATE step
        <set>
            <if test="null != stepDAO.title">title = #{stepDAO.title}</if>
            <if test="null != stepDAO.flowId">flow_id = #{stepDAO.flowId}</if>
            <if test="null != stepDAO.passCons">pass_cons = #{stepDAO.passCons}</if>
            <if test="null != stepDAO.notification">notification = #{stepDAO.notification}</if>
            <if test="null != stepDAO.ccNotification">cc_notification = #{stepDAO.ccNotification}</if>
            <if test="null != stepDAO.passAll">pass_all = #{stepDAO.passAll}</if>
            <if test="null != stepDAO.withdraw">withdraw = #{stepDAO.withdraw}</if>
            <if test="null != stepDAO.goback">goback = #{stepDAO.goback}</if>
            <if test="null != stepDAO.extraContent">extra_content = #{stepDAO.extraContent}</if>
            <if test="null != stepDAO.stepType">step_type =#{stepDAO.stepType}</if>
        </set>
        <where>
            <if test="null != stepDAO.id">AND id = #{stepDAO.id}</if>
            <if test="null != flowId and flowId != ''">AND flow_id = #{flowId}</if>
        </where>
    </update>


    <select id="getStep" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM step
        WHERE id = #{id}
    </select>

    <select id="getStepList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM step
        <where>
            <if test="null != condition.flowId and condition.flowId != ''">AND flow_id = #{condition.flowId}</if>
        </where>
        <if test="null == order">ORDER BY id DESC</if>
        <if test="null != order">ORDER BY<if test="null != order.id">id ${order.id}</if></if>
        <if test="limit > 0">LIMIT #{start}, #{limit}</if>
    </select>

    <select id="getStepCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM step
        <where>
            <if test="null != condition.flowId">
                AND flow_id = #{condition.flowId}
            </if>
        </where>
    </select>


    <select id="getStepListByFlowIds" parameterType="map" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM step
        <where>
            <if test="flowIds != null and flowIds.size() > 0">
                AND flow_id IN
                <foreach collection="flowIds" item="flowId" open="(" separator="," close=")">
                    #{flowId}
                </foreach>
            </if>
        </where>
        ORDER BY id ASC
    </select>


    <insert id="batchSaveStep" parameterType="com.eu.api.domain.dao.StepDAO">
        INSERT INTO step (flow_id, title, pass_cons, notification, cc_notification, pass_all, withdraw, goback,
                          extra_content, step_id, src_id, conditions, weight, step_type, `type`)
        values
        <foreach collection="stepDAOList" item="item" index="index" separator=",">
            (
            #{item.flowId},#{item.title},#{item.passCons},#{item.notification},#{item.ccNotification},#{item.passAll}
            ,#{item.withdraw},#{item.goback},#{item.extraContent},#{item.stepId},#{item.srcId}
            ,#{item.conditions},#{item.weight},#{item.stepType},#{item.type}
            )
        </foreach>
    </insert>
</mapper>