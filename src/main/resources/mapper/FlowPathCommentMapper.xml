<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathCommentMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathCommentDAO">
        <result column="comment_id" property="commentId"/>
        <result column="flow_path_id" property="flowPathId"/>
        <result column="uuid" property="uuid"/>
        <result column="show_name" property="showName"/>
        <result column="quote_id" property="quoteId"/>
        <result column="give_user_list" property="giveUserList"/>
        <result column="avatar" property="avatar"/>
        <result column="remark" property="remark"/>
        <result column="is_delete" property="isDelete"/>
        <result column="attachment_list" property="attachmentList"/>
        <result column="extra_content" property="extraContent"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
      comment_id,
      flow_path_id,
      uuid,
      show_name,
      quote_id,
      give_user_list,
      avatar,
      remark,
      is_delete,
      attachment_list,
      extra_content,
      create_time,
      update_time
    </sql>

    <insert id="addComment" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowPathCommentDAO">
        INSERT INTO flow_path_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != commentId">
                comment_id,
            </if>
            <if test="null != flowPathId">
                flow_path_id,
            </if>
            <if test="null != uuid">
                uuid,
            </if>
            <if test="null != showName">
                show_name,
            </if>
            <if test="null != avatar">
                avatar,
            </if>
            <if test="null != remark">
                remark,
            </if>
            <if test="null != attachmentList">
                attachment_list,
            </if>
            <if test="null != extraContent">
                extra_content,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != quoteId">
                quote_id,
            </if>
            <if test="null != isDelete">
                is_delete,
            </if>
            <if test="null != giveUserList">
                give_user_list
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != commentId">
                #{commentId},
            </if>
            <if test="null != flowPathId">
                #{flowPathId},
            </if>
            <if test="null != uuid">
                #{uuid},
            </if>
            <if test="null != showName">
                #{showName},
            </if>
            <if test="null != avatar">
                #{avatar},
            </if>
            <if test="null != remark">
                #{remark},
            </if>
            <if test="null != attachmentList">
                #{attachmentList},
            </if>
            <if test="null != extraContent">
                #{extraContent},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != quoteId">
                #{quoteId},
            </if>
            <if test="null != isDelete">
                #{isDelete},
            </if>
            <if test="null != giveUserList">
                #{giveUserList}
            </if>
        </trim>
    </insert>

    <delete id="delete">
        DELETE
        FROM flow_path_comment
        WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.eu.api.domain.dao.FlowPathCommentDAO">
        UPDATE flow_path_comment
        <set>
            <if test="null != commentId">comment_id = #{commentId},</if>
            <if test="null != flowPathId">flow_path_id = #{flowPathId},</if>
            <if test="null != uuid">uuid = #{uuid},</if>
            <if test="null != showName">show_name = #{showName},</if>
            <if test="null != quoteId">quote_id = #{quoteId},</if>
            <if test="null != giveUserList">give_user_list = #{giveUserList},</if>
            <if test="null != avatar">avatar = #{avatar},</if>
            <if test="null != remark">remark = #{remark},</if>
            <if test="null != isDelete">is_delete = #{isDelete},</if>
            <if test="null != attachmentList">attachment_list = #{attachmentList},</if>
            <if test="null != extraContent">extra_content = #{extraContent},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime}</if>
        </set>
        WHERE comment_id = #{commentId}
    </update>

    <select id="getCommentCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow_path_comment
        <where>
            is_delete = 0
            <if test="null != condition.flowPathIdList">
                <foreach collection="condition.flowPathIdList" item="flowPathId" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathId}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getCommentList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_comment
        <where>
            is_delete = 0
            <if test="null != condition.flowPathIdList">
                <foreach collection="condition.flowPathIdList" item="flowPathId" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathId}
                </foreach>
            </if>
        </where>
        ORDER BY create_time ASC
        <if test="limit > 0">
            LIMIT ${start}, ${limit}
        </if>
    </select>

    <select id="getCommentByCommentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_comment
        WHERE comment_id = #{commentId}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_comment
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM flow_path_comment
    </select>

</mapper>