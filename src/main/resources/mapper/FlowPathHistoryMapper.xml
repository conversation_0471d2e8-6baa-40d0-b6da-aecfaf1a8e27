<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathHistoryMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathHistoryDAO">
        <result column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="flow_path_id" property="flowPathId"/>
        <result column="step_id" property="stepId"/>
        <result column="next_step_id" property="nextStepId"/>
        <result column="action" property="action"/>
        <result column="uuid" property="uuid"/>
        <result column="reason" property="reason"/>
        <result column="create_time" property="createTime"/>
        <result column="extra_content" property="extraContent"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        title,
        type,
        flow_path_id,
        step_id,
        next_step_id,
        action,
        uuid,
        reason,
        create_time,
        extra_content
    </sql>

    <insert id="addFlowPathHistory" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.eu.api.domain.dao.FlowPathHistoryDAO">
        INSERT INTO flow_path_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != title">
                title,
            </if>
            <if test="null != type">
                type,
            </if>
            <if test="null != flowPathId">
                flow_path_id,
            </if>
            <if test="null != stepId">
                step_id,
            </if>
            <if test="null != nextStepId">
                next_step_id,
            </if>
            <if test="null != action">
                action,
            </if>
            <if test="null != uuid">
                uuid,
            </if>
            <if test="null != reason">
                reason,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != extraContent">
                extra_content
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != title">
                #{title},
            </if>
            <if test="null != type">
                #{type},
            </if>
            <if test="null != flowPathId">
                #{flowPathId},
            </if>
            <if test="null != stepId">
                #{stepId},
            </if>
            <if test="null != nextStepId">
                #{nextStepId},
            </if>
            <if test="null != action">
                #{action},
            </if>
            <if test="null != uuid">
                #{uuid},
            </if>
            <if test="null != reason">
                #{reason},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != extraContent">
                #{extraContent}
            </if>
        </trim>
    </insert>

    <insert id="batchAddHistories">
        <if test="list != null and !list.isEmpty()">
            INSERT INTO flow_path_history (
            title,
            type,
            flow_path_id,
            step_id,
            next_step_id,
            action,
            uuid,
            reason,
            create_time,
            extra_content
            ) VALUES
            <foreach collection="list" item="history" separator=",">
                (
                #{history.title},
                #{history.type},
                #{history.flowPathId},
                #{history.stepId},
                #{history.nextStepId},
                #{history.action},
                #{history.uuid},
                #{history.reason},
                #{history.createTime},
                #{history.extraContent}
                )
            </foreach>
        </if>
    </insert>

    <delete id="deleteFlowPathHistory">
        DELETE
        FROM flow_path_history
        WHERE id = #{id}
    </delete>

    <update id="updateFlowPathHistory" parameterType="com.eu.api.domain.dao.FlowPathHistoryDAO">
        UPDATE flow_path_history
        <set>
            <if test="null != title">title = #{title},</if>
            <if test="null != type">type = #{type},</if>
            <if test="null != flowPathId">flow_path_id = #{flowPathId},</if>
            <if test="null != stepId">step_id = #{stepId},</if>
            <if test="null != nextStepId">next_step_id = #{nextStepId},</if>
            <if test="null != action">action = #{action},</if>
            <if test="null != uuid">uuid = #{uuid},</if>
            <if test="null != reason">reason = #{reason},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != extraContent">extra_content = #{extraContent}</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="getFlowPathHistory" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_history
        WHERE id = #{id}
    </select>

    <select id="getFlowPathHistoryList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_history
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.neStepId">
                AND step_id != #{condition.neStepId}
            </if>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
        </where>
        ORDER BY create_time DESC, id DESC
        <if test="limit > 0">
            LIMIT #{start}, #{limit}
        </if>
    </select>


    <!--优化-->
    <select id="getFlowPathHistoryListTwo" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM (
        SELECT <include refid="Base_Column_List"/>,
        ROW_NUMBER() OVER (PARTITION BY flow_path_id ORDER BY create_time DESC, id DESC) AS rn
        FROM flow_path_history
        <where>
            <if test="condition.flowPathIdList != null and !condition.flowPathIdList.isEmpty()">
                AND flow_path_id IN (
                <foreach collection="condition.flowPathIdList" item="flowPathId" separator=",">
                    #{flowPathId}
                </foreach>
                )
            </if>
            <if test="condition.type != null">
                AND type = #{condition.type}
            </if>
            <if test="condition.neStepId != null">
                AND step_id != #{condition.neStepId}
            </if>
            <if test="condition.id != null">
                AND id = #{condition.id}
            </if>
        </where>
        ) sub
        WHERE sub.rn = 1
    </select>


    <select id="getFlowPathHistoryCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow_path_history
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.type">
                AND type = #{condition.type}
            </if>
            <if test="null != condition.neStepId">
                AND step_id != #{condition.neStepId}
            </if>
        </where>
    </select>

    <select id="getFlowPathHistoryBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        flow_path_history
        <if test="list != null and !list.isEmpty()">
            WHERE flow_path_id IN
            <foreach collection="list" item="flowPathId" open="(" close=")" separator=",">
                #{flowPathId}
            </foreach>
        </if>
        <if test="list == null or list.isEmpty()">
            WHERE 1 = 0
        </if>
    </select>

</mapper>