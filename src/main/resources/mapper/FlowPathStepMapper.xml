<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eu.api.mapper.FlowPathStepMapper">

    <resultMap id="BaseResultMap" type="com.eu.api.domain.dao.FlowPathStepDAO">
        <result column="id" property="id"/>
        <result column="flow_path_id" property="flowPathId"/>
        <result column="step_no" property="stepNo"/>
        <result column="pre_id" property="preId"/>
        <result column="title" property="title"/>
        <result column="pass_cons" property="passCons"/>
        <result column="pass_all" property="passAll"/>
        <result column="withdraw" property="withdraw"/>
        <result column="goback" property="goback"/>
        <result column="status" property="status"/>
        <result column="is_countersign" property="isCountersign"/>
        <result column="is_return_back" property="isReturnBack"/>
        <result column="step_type" property="stepType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="extra_content" property="extraContent"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,`flow_path_id`,step_no, pre_id, title, pass_cons, pass_all, withdraw, goback, status, is_countersign, is_return_back, step_type, create_time, update_time, extra_content
    </sql>

    <insert id="addFlowPathStep" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.eu.api.domain.dao.FlowPathStepDAO">
        INSERT INTO flow_path_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != flowPathId">flow_path_id,</if>
            <if test="null != stepNo">step_no,</if>
            <if test="null != preId">pre_id,</if>
            <if test="null != title">title,</if>
            <if test="null != passCons">pass_cons,</if>
            <if test="null != passAll">pass_all,</if>
            <if test="null != withdraw">withdraw,</if>
            <if test="null != goback">goback,</if>
            <if test="null != status">status,</if>
            <if test="null != isCountersign">is_countersign,</if>
            <if test="null != isReturnBack">is_return_back,</if>
            <if test="null != stepType">step_type,</if>
            <if test="null != createTime">create_time,</if>
            <if test="null != updateTime">update_time,</if>
            <if test="null != extraContent">extra_content</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != flowPathId">#{flowPathId},</if>
            <if test="null != stepNo">#{stepNo},</if>
            <if test="null != preId">#{preId},</if>
            <if test="null != title">#{title},</if>
            <if test="null != passCons">#{passCons},</if>
            <if test="null != passAll">#{passAll},</if>
            <if test="null != withdraw">#{withdraw},</if>
            <if test="null != goback">#{goback},</if>
            <if test="null != status">#{status},</if>
            <if test="null != isCountersign">#{isCountersign},</if>
            <if test="null != isReturnBack">#{isReturnBack},</if>
            <if test="null != stepType">#{stepType},</if>
            <if test="null != createTime">#{createTime},</if>
            <if test="null != updateTime">#{updateTime},</if>
            <if test="null != extraContent">#{extraContent}</if>
        </trim>
    </insert>

    <delete id="delById">
        DELETE
        FROM flow_path_step
        WHERE id = #{id}
    </delete>

    <update id="update">
        <if test="null != condition and null != setData">
            UPDATE flow_path_step
            <set>
                <if test="null != setData.flowPathId">flow_path_id = #{setData.flowPathId},</if>
                <if test="null != setData.stepNo">step_no = #{setData.stepNo},</if>
                <if test="null != setData.preId">pre_id = #{setData.preId},</if>
                <if test="null != setData.title">title = #{setData.title},</if>
                <if test="null != setData.passCons">pass_cons = #{setData.passCons},</if>
                <if test="null != setData.passAll">pass_all = #{setData.passAll},</if>
                <if test="null != setData.withdraw">withdraw = #{setData.withdraw},</if>
                <if test="null != setData.goback">goback = #{setData.goback},</if>
                <if test="null != setData.status">status = #{setData.status},</if>
                <if test="null != setData.isCountersign">is_countersign = #{setData.isCountersign},</if>
                <if test="null != setData.isCountersign">is_return_back = #{setData.isReturnBack},</if>
                <if test="null != setData.stepType">step_type = #{setData.stepType},</if>
                <if test="null != setData.createTime">create_time = #{setData.createTime},</if>
                <if test="null != setData.updateTime">update_time = #{setData.updateTime},</if>
                <if test="null != setData.extraContent">extra_content = #{setData.extraContent}</if>
            </set>
            <where>
                <if test="null != condition.flowPathId">AND flow_path_id = #{condition.flowPathId}</if>
                <if test="null != condition.status">AND type = #{condition.status}</if>
                <if test="null != condition.stepNo">AND step_no = #{condition.stepNo}</if>
                <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                    <foreach collection="condition.flowPathIdList" item="flowPathId" open="AND flow_path_id IN ("
                             close=")" separator=",">
                        #{flowPathId}
                    </foreach>
                </if>
                <if test="null != condition.id">AND id = #{condition.id}</if>
            </where>
        </if>
    </update>
    <update id="batchUpdateSteps">
        <if test="list != null and !list.isEmpty()">
            UPDATE flow_path_step
            <set>
                flow_path_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.flowPathId != null">
                        WHEN #{item.id} THEN #{item.flowPathId}
                    </if>
                </foreach>
                ELSE flow_path_id END,

                step_no = CASE id
                <foreach collection="list" item="item">
                    <if test="item.stepNo != null">
                        WHEN #{item.id} THEN #{item.stepNo}
                    </if>
                </foreach>
                ELSE step_no END,

                pre_id = CASE id
                <foreach collection="list" item="item">
                    <if test="item.preId != null">
                        WHEN #{item.id} THEN #{item.preId}
                    </if>
                </foreach>
                ELSE pre_id END,

                title = CASE id
                <foreach collection="list" item="item">
                    <if test="item.title != null">
                        WHEN #{item.id} THEN #{item.title}
                    </if>
                </foreach>
                ELSE title END,

                pass_cons = CASE id
                <foreach collection="list" item="item">
                    <if test="item.passCons != null">
                        WHEN #{item.id} THEN #{item.passCons}
                    </if>
                </foreach>
                ELSE pass_cons END,

                pass_all = CASE id
                <foreach collection="list" item="item">
                    <if test="item.passAll != null">
                        WHEN #{item.id} THEN #{item.passAll}
                    </if>
                </foreach>
                ELSE pass_all END,

                withdraw = CASE id
                <foreach collection="list" item="item">
                    <if test="item.withdraw != null">
                        WHEN #{item.id} THEN #{item.withdraw}
                    </if>
                </foreach>
                ELSE withdraw END,

                goback = CASE id
                <foreach collection="list" item="item">
                    <if test="item.goback != null">
                        WHEN #{item.id} THEN #{item.goback}
                    </if>
                </foreach>
                ELSE goback END,

                status = CASE id
                <foreach collection="list" item="item">
                    <if test="item.status != null">
                        WHEN #{item.id} THEN #{item.status}
                    </if>
                </foreach>
                ELSE status END,

                is_countersign = CASE id
                <foreach collection="list" item="item">
                    <if test="item.isCountersign != null">
                        WHEN #{item.id} THEN #{item.isCountersign}
                    </if>
                </foreach>
                ELSE is_countersign END,

                is_return_back = CASE id
                <foreach collection="list" item="item">
                    <if test="item.isReturnBack != null">
                        WHEN #{item.id} THEN #{item.isReturnBack}
                    </if>
                </foreach>
                ELSE is_return_back END,

                step_type = CASE id
                <foreach collection="list" item="item">
                    <if test="item.stepType != null">
                        WHEN #{item.id} THEN #{item.stepType}
                    </if>
                </foreach>
                ELSE step_type END,

                update_time = CASE id
                <foreach collection="list" item="item">
                    <if test="item.updateTime != null">
                        WHEN #{item.id} THEN #{item.updateTime}
                    </if>
                </foreach>
                ELSE update_time END,

                extra_content = CASE id
                <foreach collection="list" item="item">
                    <if test="item.extraContent != null">
                        WHEN #{item.id} THEN #{item.extraContent}
                    </if>
                </foreach>
                ELSE extra_content END
            </set>
            WHERE id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item.id}
            </foreach>
        </if>
    </update>

    <update id="updateBatch" parameterType="map">
        <foreach collection="daos"
                 item="dao"
                 index="idx"
                 separator=";">
            UPDATE flow_path_step
            <set>
                <if test="dao.extraContent != null">
                    extra_content = #{dao.extraContent},
                </if>
                dummy = dummy  -- 占位 防止上面逗号出错
            </set>
            WHERE flow_path_id = #{conds[ idx ].flowPathId}
            AND step_no = #{conds[ idx ].stepNo}
            AND status = #{conds[ idx ].status}
        </foreach>
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_step
        WHERE id = #{id}
    </select>

    <select id="getFlowPathStepList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM flow_path_step
        <where>
            <if test="null != condition.flowPathId">
                AND flow_path_id = #{condition.flowPathId}
            </if>
            <if test="null != condition.flowPathIdList and !condition.flowPathIdList.isEmpty()">
                <foreach collection="condition.flowPathIdList" item="flowPathId" open="AND flow_path_id IN (" close=")" separator=",">
                    #{flowPathId}
                </foreach>
            </if>
            <if test="null != condition.stepNo">
                AND step_no = #{condition.stepNo}
            </if>
            <if test="null != condition.preId">
                AND pre_id = #{condition.preId}
            </if>
            <if test="null != condition.status">
                AND status = #{condition.status}
            </if>
            <if test="null != condition.id">
                AND id = #{condition.id}
            </if>
        </where>
        ORDER BY step_no ASC
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM flow_path_step
    </select>

    <select id="getFlowPathStepFlowPathIdList" resultType="java.lang.Integer">
        SELECT DISTINCT flow_path_id
        FROM flow_path_step
    </select>
    <select id="getFlowPathStepBatch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        flow_path_step
        <if test="list != null and !list.isEmpty()">
            WHERE flow_path_id IN
            <foreach collection="list" item="flowPathId" open="(" close=")" separator=",">
                #{flowPathId}
            </foreach>
        </if>
        <if test="list == null or list.isEmpty()">
            WHERE 1 = 0
        </if>
    </select>
</mapper>