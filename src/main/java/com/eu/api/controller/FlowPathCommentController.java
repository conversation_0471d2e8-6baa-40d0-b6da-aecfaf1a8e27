package com.eu.api.controller;

import com.eu.api.domain.condition.GetFlowPathCommentCondition;
import com.eu.api.domain.condition.GetFlowPathCondition;
import com.eu.api.domain.dao.FlowPathCommentDAO;
import com.eu.api.domain.dao.FlowPathDAO;
import com.eu.api.domain.dto.FlowPathCommentDTO;
import com.eu.api.domain.ms.MsFlowPathCommentAddRequest;
import com.eu.api.domain.ms.MsFlowPathCommentListRequest;
import com.eu.api.domain.ms.MsUpdateDetailsAttachmentRequest;
import com.eu.api.domain.ms.UpdateFlowCommentRequest;
import com.eu.api.service.FlowPathCommentService;
import com.eu.api.service.FlowPathService;
import com.eu.common.annotation.LogRecord;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.result.DResult;
import com.eu.common.result.LResult;
import com.eu.common.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/flowPathComment")
public class FlowPathCommentController {
    @Autowired
    private FlowPathService flowPathService;
    @Autowired
    private FlowPathCommentService flowPathCommentService;

    /**
     * 设置审批流
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/add")
    public Result add(@RequestBody MsFlowPathCommentAddRequest request) {
        FlowPathDAO flowPathDAO = flowPathService.getFlowPath(GetFlowPathCondition.builder()
                .id(request.getFlowPathId())
                .build());
        if (flowPathDAO == null) {
            return DResult.fail( "参数错误");
        }
        if (request.getExtraContent() == null) {
            request.setExtraContent(new HashMap<>());
        }
        FlowPathCommentDAO flowPathCommentDAO = flowPathCommentService.addComment(request);
        FlowPathCommentDTO flowPathCommentDTO = FlowPathCommentDTO.create(flowPathCommentDAO);
        return DResult.success(flowPathCommentDTO);
    }

    /**
     * 获取审批流列表
     */
    @RequestMapping("/list")
    public Result list(@RequestBody(required = false) MsFlowPathCommentListRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail( "请求参数不能为空");
        }

        int page = Math.max(Integer.parseInt(String.valueOf(request.getPage())), 1);
        int limit = Math.max(Integer.parseInt(String.valueOf(request.getPageCount())), 0);
        //查询条件
        GetFlowPathCommentCondition condition = GetFlowPathCommentCondition.builder().build();

        if (!request.getFlowPathIdList().isEmpty()) {
            condition.setFlowPathIdList(request.getFlowPathIdList());
        }
        int commentCount = flowPathCommentService.getCommentCount(condition);
        List<FlowPathCommentDTO> commentList = flowPathCommentService.getCommentList(condition, (page - 1) * limit, limit);

        return LResult.success(commentList, (long) commentCount);
    }

    /**
     * 审批详情备注重命名附件
     *
     * @param request
     * @return
     */
    @PostMapping("/updateCommentAttachment")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批详情备注重命名附件", name = "审批详情备注重命名附件", type = ApiTypeConstant.UPDATE)
    public Result updateCommentAttachment(@RequestBody(required = false) MsUpdateDetailsAttachmentRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getCommentId())) {
            return DResult.fail("commentId不能为空");
        }
        if (Objects.isNull(request.getAttachmentList())) {
            return DResult.fail("表单数据不能为空");
        }
        flowPathCommentService.updateCommentAttachment(request);
        return DResult.success();
    }


    /**
     * 删除审批备注
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "删除审批备注", name = "删除审批备注", type = ApiTypeConstant.UPDATE)
    public Result delete(@RequestBody(required = false) UpdateFlowCommentRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getCommentId())) {
            return DResult.fail("commentId不能为空");
        }
        if (Objects.isNull(request.getIsDelete())) {
            return DResult.fail("删除状态不能为空");
        }
        flowPathCommentService.updateComment(request);
        return DResult.success();
    }
}
