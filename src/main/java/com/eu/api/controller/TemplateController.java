package com.eu.api.controller;

import com.eu.api.domain.ms.MsTemplateAddRequest;
import com.eu.api.domain.ms.MsTemplateInfoRequest;
import com.eu.api.domain.ms.MsTemplateListRequest;
import com.eu.api.domain.ms.MsTemplateUpdateRequest;
import com.eu.api.service.TemplateService;
import com.eu.common.annotation.LogRecord;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.context.ServletContext;
import com.eu.common.result.DResult;
import com.eu.common.result.LResult;
import com.eu.common.result.Result;
import com.eu.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping("/temp")
public class TemplateController {

    @Autowired
    private TemplateService templateService;

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/add")
    @LogRecord(module = "模版模块", name = "模版新增", type = ApiTypeConstant.INSERT)
    public Result add(@RequestBody MsTemplateAddRequest request) {
        if (StringUtil.isEmpty(request.getTitle())) {
            return DResult.fail("参数错误");
        }
        if (request.getTempInfo() == null) {
            return DResult.fail( "参数错误");
        }
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        return DResult.success(templateService.addTemplate(request));
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/update")
    @LogRecord(module = "模版模块", name = "模版更新", type = ApiTypeConstant.UPDATE)
    public Result update(@RequestBody MsTemplateUpdateRequest request) {
        if (StringUtil.isEmpty(request.getId())) {
            return DResult.fail("参数错误");
        }
        if (StringUtil.isEmpty(request.getTitle())) {
            return DResult.fail("参数错误");
        }
        if (request.getTempInfo() == null) {
            return DResult.fail("参数错误");
        }
        return DResult.success(templateService.updateTemplate(request));
    }

    @RequestMapping("/list")
    @LogRecord(module = "模版模块", name = "模版列表", type = ApiTypeConstant.SELECT)
    public Result list(@RequestBody MsTemplateListRequest request) {
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        return LResult.success(templateService.getTemplateList(request));
    }

    @RequestMapping("/info")
    @LogRecord(module = "模版模块", name = "模版详情", type = ApiTypeConstant.INFO)
    public Result info(@RequestBody MsTemplateInfoRequest request) {
        return DResult.success(templateService.getTemplate(request));
    }
}
