package com.eu.api.controller;

import cn.hutool.json.JSONUtil;
import com.eu.api.domain.dto.FlowPathDTO;
import com.eu.api.domain.ms.MsFlowPathCreateRequest;
import com.eu.api.service.ApprovealService;
import com.eu.api.service.FlowPathService;
import com.eu.api.service.GoFlowService;
import com.eu.common.annotation.LogRecord;
import com.eu.common.base.BaseController;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.context.ServletContext;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import com.eu.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 审批相关接口
 * &#064;DATE: 2025/1/23 16:19
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@RestController
@RequestMapping("/approve")
public class ApproveController extends BaseController {

    @Autowired
    private ApprovealService approvealService;
    @Autowired
    private FlowPathService flowPathService;
    @Autowired
    private GoFlowService goFlowService;

    /**
     * 发起审批
     * @return
     */
    @PostMapping("/initiateApproval")
//    @LogRecord(module = "审批模块", name = "发起审批", type = ApiTypeConstant.INSERT)
    public Result initiateApproval(@RequestBody(required = false) MsFlowPathCreateRequest request) {
        if (Objects.isNull(request)) {
            return none();
        }
        log.info("发起审批入参 -> {}", JSONUtil.toJsonStr(request));
        Integer id = approvealService.initiateApproval(request);
        String appChannelId = request.getAppChannelId();
        if (StringUtil.isEmpty(appChannelId)) {
            appChannelId = ServletContext.getAppChannelId();
        }
        String dataChannelId = request.getDataChannelId();
        if (StringUtil.isEmpty(dataChannelId)) {
            dataChannelId = ServletContext.getDataChannelId();
        }

        Boolean async = request.getAsync();
        if (Objects.nonNull(async) && async) {
            String finalAppChannelId = appChannelId;
            String finalDataChannelId = dataChannelId;
            CompletableFuture.runAsync(() -> {
                goFlowService.goFlowV2(id, 0, request.getUuid(), request.getShowName(), "", finalAppChannelId, finalDataChannelId, request.getTitle(), false);
            });
        } else {
            //审批判定
            goFlowService.goFlowV2(id, 0, request.getUuid(), request.getShowName(), "", appChannelId, dataChannelId, request.getTitle(), false);
        }

        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(id);
        return DResult.success(flowPathDetail);
    }

}