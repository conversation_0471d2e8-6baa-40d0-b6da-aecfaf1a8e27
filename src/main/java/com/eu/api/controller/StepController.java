package com.eu.api.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.eu.api.domain.condition.GetFlowCondition;
import com.eu.api.domain.condition.GetStepCondition;
import com.eu.api.domain.dao.FlowDAO;
import com.eu.api.domain.dao.StepDAO;
import com.eu.api.domain.dto.FlowDTO;
import com.eu.api.domain.ms.MsFlowAddRequest;
import com.eu.api.domain.ms.MsFlowInfoRequest;
import com.eu.api.domain.ms.MsFlowUpdateRequest;
import com.eu.api.domain.ms.MsStepListRequest;
import com.eu.api.service.BPService;
import com.eu.api.service.FlowService;
import com.eu.api.service.StepService;
import com.eu.api.service.extend.StepServiceExtend;
import com.eu.common.annotation.LogRecord;
import com.eu.common.base.BaseController;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.context.ServletContext;
import com.eu.common.result.DResult;
import com.eu.common.result.LResult;
import com.eu.common.result.Result;
import com.eu.common.util.StringUtil;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * &#064;DATE: 2024/11/29
 * &#064;AUTHOR: XSL
 */
@RestController
@RequestMapping("/step")
public class StepController extends BaseController {

    @Autowired
    private StepService stepService;
    @Autowired
    private StepServiceExtend stepServiceExtend;
    @Autowired
    private BPService bpService;
    @Autowired
    private FlowService flowService;

    /**
     * 获取流程所有的节点
     *
     * @param
     * @return
     */
    @PostMapping("/getStepList")
    public Result getStepList(@RequestBody(required = false) MsStepListRequest request) {
        if (Objects.isNull(request)) {
            return none();
        }
        if (StrUtil.isEmpty(request.getFlowId())) {
            return none("流程ID不能为空");
        }
        List<StepDAO> stepList = stepService.getStepList(GetStepCondition.builder().flowId(request.getFlowId()).build());
        String stepId = request.getStepId();
        if (StrUtil.isEmpty(stepId)) {
            return LResult.success(stepList);
        }
        List<StepDAO> stepDAOList = stepList.stream().filter(step -> step.getSrcId().equals(stepId)).collect(Collectors.toList());
        return LResult.success(stepServiceExtend.getStepList(stepList, stepDAOList.stream().map(StepDAO::getStepId).collect(Collectors.toList()), request.isConditionFlag()));
    }


    /**
     * 设置流程节点
     *
     * @param request
     * @return
     */
    @PostMapping("/createReviewStep")
    @LogRecord(module = "流程模块", name = "流程新增", type = ApiTypeConstant.INSERT)
    @Transactional
    public Result createReviewStep(@RequestBody(required = false) MsFlowAddRequest request) {
        //渠道号
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        //获取最新数据
        FlowDAO flowDAO = flowService.addFlowByStep(request);
        FlowDTO flowDTO = FlowDTO.create(flowDAO);

        // 为吉牛做判断 如果节点数据存在stepInfo中
//        if (Objects.isNull(request.getCustomize())) {
//            List<Map<String, Object>> customize = new ArrayList<>();
//            customize.add((Map<String, Object>) request.getStepInfo());
//            request.setCustomize(customize);
//        }

        // 获取节点json
        JsonNode jsonNode = bpService.addStepList(request.getCustomize(), flowDTO.getId());

        if (jsonNode != null) {
            // 设置节点json
            flowDAO.setCustomize(jsonNode.toString());
        } else {
            flowDAO.setCustomize("");
        }

        request.setId(flowDTO.getId());
        // 二次更新
        flowService.addFlowByStep(flowDAO);

        return DResult.success(request);
    }

    /**
     * 更新审批流程
     *
     * @param request
     * @return
     */
    @PostMapping("/updateReviewStep")
    @LogRecord(module = "流程模块", name = "流程修改", type = ApiTypeConstant.DELETE)
    @Transactional
    public Result updateReviewStep(@RequestBody(required = false) MsFlowUpdateRequest request) {

        //渠道号
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        //获取最新数据
        FlowDAO flowDAO = flowService.updateFlowByStep(request);
        FlowDTO flowDTO = FlowDTO.create(flowDAO);
//        // 为吉牛做判断 如果节点数据存在stepInfo中
//        if (Objects.isNull(request.getCustomize())) {
//            List<Map<String, Object>> customize = new ArrayList<>();
//            customize.add((Map<String, Object>) request.getStepInfo());
//            request.setCustomize(customize);
//        }

        JsonNode jsonNode = bpService.updateStepList(request.getCustomize(), flowDTO.getId());

        if (jsonNode != null) {
            flowDAO.setCustomize(jsonNode.toString());
        } else {
            flowDAO.setCustomize("");
        }

        request.setId(flowDTO.getId());
        // 二次更新
        flowService.addFlowByStep(flowDAO);

        return DResult.success(request);
    }

    /**
     * 获取审批流详情
     *
     * @param request
     * @return
     */
    @RequestMapping("/infoReviewStep")
    @LogRecord(module = "流程模块", name = "流程详情", type = ApiTypeConstant.INFO)
    public Result info(@RequestBody(required = false) MsFlowInfoRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (StringUtil.isEmpty(request.getId())) {
            return DResult.fail("参数错误");
        }
//        FlowDTO flow = flowService.getFlow(GetFlowCondition.builder().flowId(request.getId()).build());
        FlowDTO flow = flowService.getFlowStep(GetFlowCondition.builder().flowId(request.getId()).build());
        if (Objects.isNull(flow)) {
            return DResult.fail("未查询到ID为[" + request.getId() + "]的数据");
        }
        return DResult.success(flow);
    }
    /**
     * 停用/删除专用接口
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/update")
    @LogRecord(module = "流程模块", name = "流程更新", type = ApiTypeConstant.UPDATE)
    public Result update(@RequestBody Map<String, Object> req) {
        MsFlowUpdateRequest request = StringUtil.jsonDecode(JSONUtil.toJsonStr(req), MsFlowUpdateRequest.class);
        if (StringUtil.isEmpty(request.getId())) {
            return DResult.fail("参数错误");
        }
        //获取最新数据
        FlowDTO flowDTO = FlowDTO.create(flowService.updateFlow(request));
        return DResult.success(flowDTO);
    }

}

