package com.eu.api.controller;

import com.eu.api.domain.condition.GetFlowPathCondition;
import com.eu.api.domain.condition.GetFlowPathStepNotificationCondition;
import com.eu.api.domain.dao.FlowPathDAO;
import com.eu.api.domain.dto.FlowPathStepNotificationDTO;
import com.eu.api.domain.ms.FlowPathStepNotificationRequest;
import com.eu.api.service.FlowPathService;
import com.eu.api.service.FlowPathStepNotificationService;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 节点办理人相关操作
 * &#064;DATE: 2024/10/21
 * &#064;AUTHOR: XSL
 *
 */
@RestController
@RequestMapping("/notification")
public class FlowPathStepNotificationController {

    @Autowired
    private FlowPathStepNotificationService flowPathStepNotificationService;
    @Autowired
    private FlowPathService flowPathService;

    /**
     * 根据条件获取审批流数据
     * @param flowPathStepNotificationRequest
     * @return
     */
    @PostMapping("/flowPathStepNotificationList")
    public Result getFlowPathStepNotificationList(@RequestBody(required = false) FlowPathStepNotificationRequest flowPathStepNotificationRequest) {
        GetFlowPathStepNotificationCondition getFlowPathStepNotificationCondition = new GetFlowPathStepNotificationCondition();
        BeanUtils.copyProperties(flowPathStepNotificationRequest, getFlowPathStepNotificationCondition);
        List<String> flowIdList = flowPathStepNotificationRequest.getFlowIdList();
        if (Objects.nonNull(flowIdList) && !flowIdList.isEmpty()) {
            List<FlowPathDAO> flowPathList = flowPathService.getFlowPathList(GetFlowPathCondition.builder().flowIdList(flowIdList).build());
            getFlowPathStepNotificationCondition.setFlowPathIdList(flowPathList.stream().map(FlowPathDAO::getId).collect(Collectors.toList()));
        }

        List<FlowPathStepNotificationDTO> flowPathStepNotificationList = flowPathStepNotificationService.getFlowPathStepNotificationList(getFlowPathStepNotificationCondition);
        return DResult.success(flowPathStepNotificationList);
    }
}
