package com.eu.api.controller;

import com.eu.api.domain.bo.FlowPathListBO;
import com.eu.api.domain.dto.FlowPathListByStetDTO;
import com.eu.api.domain.dto.FlowPathListDeleteDTO;
import com.eu.api.domain.entity.FlowPathListEntity;
import com.eu.api.service.FlowPathListService;
import com.eu.common.annotation.LogRecord;
import com.eu.common.base.BaseController;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * flowPathList
 * &#064;DATE: 2024/11/1
 * &#064;AUTHOR: XSL
 */
@RestController
@RequestMapping("/flowPathList")
public class FlowPathListController extends BaseController {

    @Autowired
    private FlowPathListService flowPathListService;

    @PostMapping("/flowPathListByStepUpdate")
    @LogRecord(module = "审批流水模块", name = "审批节点拼接", type = ApiTypeConstant.INSERT)
    public Result flowPathListByStepUpdate(@RequestBody(required = false) FlowPathListByStetDTO flowPathListByStetDTO) {
        if (Objects.isNull(flowPathListByStetDTO)) {
            return none();
        }
        List<FlowPathListBO> flowPathList = flowPathListByStetDTO.getFlowPathList();
        for (FlowPathListBO flowPathListBO : flowPathList) {
            FlowPathListEntity flowPathListEntity = new FlowPathListEntity();
            BeanUtils.copyProperties(flowPathListBO, flowPathListEntity);
            int status = flowPathListBO.getStatus();
            if (status == 0) {  //新增
                flowPathListService.save(flowPathListEntity);
            } else if (status == 1) {   //修改
                if (flowPathListEntity.getId() != null) {
                    flowPathListService.updateById(flowPathListEntity);
                }
            } else if (status == 2) {   //删除
                if (flowPathListEntity.getId() != null) {
                    flowPathListService.removeById(flowPathListEntity.getId());
                }
            }
        }
        return DResult.success();
    }

    @PostMapping("/deleteFlowPathListByMySelf")
    @LogRecord(module = "批量审批", name = "审批批量删除", type = ApiTypeConstant.INSERT)
    public Result deleteFlowPathListByMySelf(@RequestBody FlowPathListDeleteDTO dto) {
        if (dto.getIds().isEmpty() || dto.getUserId().isEmpty()) {
            return DResult.success();
        }
        int success = flowPathListService.deleteFlowPathList(dto);
        return DResult.success(success);
    }


}
