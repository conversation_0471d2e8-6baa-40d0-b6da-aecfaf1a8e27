package com.eu.api.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eu.api.domain.vo.PingResult;
import com.eu.api.domain.vo.PingVO;
import com.eu.api.service.PingService;
import com.eu.api.service.extend.CallbackMQRelationExtend;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import com.eu.common.util.ServletUtil;
import com.eu.common.util.StringUtil;
import com.mssdk.BalancerManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * &#064;DATE: 2025/1/17 15:46
 * &#064;AUTHOR: XSL
 */
@RestController
@RequestMapping
public class PingController {

    @Autowired
    BalancerManager balancerManager;

    @Autowired
    private PingService pingService;
    @Autowired
    private CallbackMQRelationExtend callbackMQRelationExtend;

    /**
     * 获取信息
     *
     * @return
     */
    @GetMapping("/ping")
    public PingResult ping() {
        PingVO pingVO = pingService.ping();
        String key = ServletUtil.getParameter("key");
        if (StringUtil.isNotEmpty(key)) {
            JSONObject jsonObject = JSONUtil.parseObj(pingVO);
            Map<String, Object> data = new HashMap<>();
            data.put(key, jsonObject.get(key));
            return PingResult.success(data);
        }
        pingVO.setRedis(null);
        pingVO.setMysql(null);
        return PingResult.success(JSONUtil.parseObj(pingVO));
    }

    /**
     * 获取信息
     *
     * @return
     */
    @PostMapping("/ping")
    public PingResult pingPost(@RequestBody(required = false) JSONObject request) {
        PingVO pingVO = pingService.ping();
        String key = ServletUtil.getParameter("key");
        if (StringUtil.isNotEmpty(key)) {
            JSONObject jsonObject = JSONUtil.parseObj(pingVO);
            Map<String, Object> data = new HashMap<>();
            data.put(key, jsonObject.get(key));
            return PingResult.success(data);
        }
        pingVO.setRedis(null);
        pingVO.setMysql(null);
        JSONObject jsonObject = JSONUtil.parseObj(pingVO);
        if (Objects.nonNull(request)) {
            jsonObject.set("echoStr", request.getStr("echoStr", "ping"));
        }

        return PingResult.success(JSONUtil.parseObj(jsonObject));
    }

    /**
     * 热更新参数
     *
     * @return
     */
    @PostMapping("/inject")
    public Result inject() {
        pingService.inject();
        callbackMQRelationExtend.updateMap();
        return DResult.success("热更新完成");
    }

//    /**
//     * 系统健康监测
//     * @return
//     */
//    @PostMapping("/heart")
//    public Result heartBeat() {
//        JSONObject heartbeat = pingService.heartbeat();
//        return DResult.success(heartbeat);
//    }

    /**
     * 获取注册信息
     *
     * @return MsResponse
     */
    @GetMapping("/registry")
    public Result registry() {
        return DResult.success(balancerManager.getLoadBalancer());
    }

}