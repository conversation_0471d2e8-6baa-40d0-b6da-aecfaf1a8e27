package com.eu.api.controller;

import cn.hutool.json.JSONUtil;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.domain.condition.GetFlowCondition;
import com.eu.api.domain.dto.FlowByListDTO;
import com.eu.api.domain.dto.FlowDTO;
import com.eu.api.domain.ms.MsFlowAddRequest;
import com.eu.api.domain.ms.MsFlowInfoRequest;
import com.eu.api.domain.ms.MsFlowUpdateRequest;
import com.eu.api.service.FlowService;
import com.eu.common.annotation.LogRecord;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.context.ServletContext;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import com.eu.common.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/flow")
public class FlowController {

    @Autowired
    private FlowService flowService;

    /**
     * 设置审批流
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/add")
//    @LogRecord(module = "流程模块", name = "流程新增", type = ApiTypeConstant.INSERT)
    public Result add(@RequestBody MsFlowAddRequest request, HttpServletRequest httpServletRequest) {
        //渠道号
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        //获取最新数据
        FlowDTO flowDTO = FlowDTO.create(flowService.addFlow(request));
        //获取步骤
        flowDTO.setStepInfo(flowService.getStepTree(flowDTO.getId()));
        return DResult.success(flowDTO);
    }

    /**
     * 更新审批流
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/update")
//    @LogRecord(module = "流程模块", name = "流程更新", type = ApiTypeConstant.UPDATE)
    public Result update(@RequestBody Map<String, Object> req) {
        MsFlowUpdateRequest request = StringUtil.jsonDecode(JSONUtil.toJsonStr(req), MsFlowUpdateRequest.class);
        if (StringUtil.isEmpty(request.getId())) {
            return DResult.fail("参数错误");
        }
        //获取最新数据
        FlowDTO flowDTO = FlowDTO.create(flowService.updateFlow(request));
        //获取步骤
        flowDTO.setStepInfo(flowService.getStepTree(flowDTO.getId()));
        return DResult.success(flowDTO);
    }

    /**
     * 获取审批流详情
     * @param request
     * @return
     */
    @RequestMapping("/info")
//    @LogRecord(module = "流程模块", name = "流程详情", type = ApiTypeConstant.INFO)
    public Result info(@RequestBody(required = false) MsFlowInfoRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (StringUtil.isEmpty(request.getId())) {
            return DResult.fail("参数错误");
        }
        FlowDTO flow = flowService.getFlow(GetFlowCondition.builder().flowId(request.getId()).build());
        if (Objects.isNull(flow)) {
            return DResult.fail("未查询到ID为[" + request.getId() + "]的数据");
        }
        return DResult.success(flow);
    }

    /**
     * 获取审批流列表
     */
    @RequestMapping("/list")
    @LogRecord(module = "流程模块", name = "流程列表", type = ApiTypeConstant.SELECT)
    public Result list(@RequestBody(required = false) Map<String, Object> request, HttpServletRequest httpServletRequest) {
        if (request == null) {
            request = new HashMap<>();
        }
        if (!request.containsKey("page") || request.get("page") == null || !StringUtil.isNumberic(String.valueOf(request.get("page")))) {
            request.put("page", 1);
        }
        if (!request.containsKey("pageCount") || request.get("pageCount") == null || !StringUtil.isNumberic(String.valueOf(request.get("pageCount")))) {
            request.put("pageCount", 0);
        }
        int page = Math.max(Integer.parseInt(request.get("page").toString()), 1);
        int limit = Math.max(Integer.parseInt(request.get("pageCount").toString()), 0);
        //查询条件
        GetFlowCondition condition = GetFlowCondition.builder().build();
        //渠道号
        condition.setDataChannelId(request.get("dataChannelId") == null ? ServletContext.getDataChannelId() : request.get("dataChannelId").toString());
        condition.setStatusList(Collections.singletonList(1));
        if (request.get("id") != null) {
            condition.setFlowId(request.get("id").toString());
        }
        if (request.get("ids") != null) {
            condition.setFlowIdList(JSONUtil.toList(JSONUtil.toJsonStr(request.get("ids")), String.class));
        }
        if (request.get("appChannelId") != null) {
            condition.setAppChannelId(request.get("appChannelId").toString());
        }
        if (request.get("freedom") != null) {
            condition.setFreedom(Integer.parseInt(request.get("freedom").toString()) > 0 ? 1 : 0);
        }
        if (request.get("title") != null) {
            condition.setTitle(request.get("title").toString());
        }
        if (request.get("titleList") != null) {
            condition.setTitle(null);
            condition.setTitleList(JSONUtil.toList(JSONUtil.toJsonStr(request.get("titleList")), String.class));
        }
        if (request.get("status") != null) {
            int status = Integer.parseInt(request.get("status").toString());
            condition.setStatusList(new ArrayList<Integer>() {{
                add(status);
            }});
        }
        if (request.get("statusList") != null) {
            condition.setStatusList(JSONUtil.toList(JSONUtil.toJsonStr(request.get("statusList")), Integer.class));
        }
        if (request.get("showStop") != null && Integer.parseInt(request.get("showStop").toString()) > 0) {
            condition.setStatusList(null);
        }
        String listUsedAlone="";
        if (request.get("listUsedAloneFlag") != null) {
            listUsedAlone=request.get("listUsedAloneFlag").toString();
        }
        request.remove("page");
        request.remove("pageCount");
        request.remove("id");
        request.remove("ids");
        request.remove("appChannelId");
        request.remove("freedom");
        request.remove("title");
        request.remove("titleList");
        request.remove("status");
        request.remove("statusList");
        request.remove("showAll");
        request.remove("showStop");
        request.remove("listUsedAloneFlag");

        //扩展字段
        ArrayList<String> flowColumnList = new ArrayList<>(Arrays.asList(ApproveConstant.flowColumnList));
        Map<String, String> extraContentMap = new HashMap<>();
        for (Map.Entry<String, Object> extraContent : request.entrySet()) {
            if (!flowColumnList.contains(extraContent.getKey())) {
                extraContentMap.put(extraContent.getKey(), String.valueOf(extraContent.getValue()));
            }
        }
        condition.setExtraContentMap(extraContentMap);
        List<FlowByListDTO> flowListByList=new ArrayList<>();
        List<FlowDTO> flowList=new ArrayList<>();
        if(listUsedAlone.equals("list")){
            flowListByList = flowService.getFlowListByList(condition, (page - 1) * limit, limit);
            return DResult.success(flowListByList);
        }else{
            flowList = flowService.getFlowList(condition, (page - 1) * limit, limit);
            return DResult.success(flowList);
        }
    }

//    @RequestMapping("/info")
//    @LogRecord(module = "流程模块", name = "流程详情", type = ApiTypeConstant.INFO)
//    public Result info(@RequestBody(required = false) MsFlowInfoRequest request) {
//        if (Objects.isNull(request)) {
//            return DResult.fail("请求参数不能为空");
//        }
//        if (StringUtil.isEmpty(request.getId())) {
//            return DResult.fail("参数错误");
//        }
////        FlowDTO flow = flowService.getFlow(GetFlowCondition.builder().flowId(request.getId()).build());
//        FlowDTO flow = flowService.getFlowStep(GetFlowCondition.builder().flowId(request.getId()).build());
//        if (Objects.isNull(flow)) {
//            return DResult.fail("未查询到ID为[" + request.getId() + "]的数据");
//        }
//        return DResult.success(flow);
//    }
}
