package com.eu.api.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.constant.mq.BatchApprovalTopic;
import com.eu.api.domain.condition.*;
import com.eu.api.domain.dao.*;
import com.eu.api.domain.dto.*;
import com.eu.api.domain.dto.flow.ApprovalRequest;
import com.eu.api.domain.dto.flow.BatchFlowPathInfoDTO;
import com.eu.api.domain.dto.flow.FlowPathListQueryDTO;
import com.eu.api.domain.entity.FlowPathEntity;
import com.eu.api.domain.ms.*;
import com.eu.api.domain.vo.flow.FLowPathListVO;
import com.eu.api.domain.vo.flow.FlowPathStatisticsVO;
import com.eu.api.service.*;
import com.eu.common.annotation.LogRecord;
import com.eu.common.client.RedisCache;
import com.eu.common.constant.ApiTypeConstant;
import com.eu.common.context.ServletContext;
import com.eu.common.exception.BsException;
import com.eu.common.ms.log.LogMs;
import com.eu.common.ms.log.pojo.request.LogRequest;
import com.eu.common.ms.mq.MQMs;
import com.eu.common.result.DResult;
import com.eu.common.result.DataList;
import com.eu.common.result.LResult;
import com.eu.common.result.Result;
import com.eu.common.util.StringUtil;
import com.eu.common.util.TimeUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/flowpath")
public class FlowPathController {

    @Autowired
    private FlowService flowService;
    @Autowired
    private FlowPathService flowPathService;
    @Autowired
    private FlowPathListService flowPathListService;
    @Autowired
    private FlowPathHistoryService flowPathHistoryService;
    @Autowired
    private FlowPathStepService flowPathStepService;
    @Autowired
    private FlowPathStepNotificationService flowPathStepNotificationService;
    @Autowired
    private ApprovealService approvealService;
    @Autowired
    private GoFlowService goFlowService;
    @Autowired
    private RedisCache redisCache;


    /**
     * 发起审批
     *
     * @param request
     * @return
     * @throws JsonProcessingException
     */
    @RequestMapping("/create")
    @Transactional(rollbackFor = Exception.class)
//    @LogRecord(module = "审批模块", name = "发起审批", type = ApiTypeConstant.INSERT)
    public Result create(@RequestBody MsFlowPathCreateRequest request) throws JsonProcessingException {
        if (StringUtil.isEmpty(request.getFlowId())) {
            return DResult.fail("审批流id错误");
        }
        //渠道号
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        log.info("发起审批入参 -> {}", JSONUtil.toJsonStr(request));
        FlowPathDTO flowPathDTO = approvealService.create(request);
        //审批判定
        goFlowService.goFlowV2(flowPathDTO.getId(), 0, request.getUuid(), request.getShowName(), "", request.getAppChannelId(), request.getDataChannelId(), request.getTitle(), false);
        return DResult.success(flowPathDTO);
    }

    /**
     * 审批列表
     */
    @RequestMapping("/flowList")
//    @LogRecord(module = "审批模块", name = "审批列表", type = ApiTypeConstant.SELECT)
    public Result flowList(@RequestBody(required = false) Map<String, Object> request) {
        if (request == null) {
            request = new HashMap<>();
        }
        GetFlowPathListDAOListCondition condition = GetFlowPathListDAOListCondition.builder().build();
        //渠道号
        condition.setAppChannelId(request.get("appChannelId") == null ? null : request.get("appChannelId").toString());
        condition.setDataChannelId(request.get("dataChannelId") == null ? ServletContext.getDataChannelId() : request.get("dataChannelId").toString());
        if (!request.containsKey("page") || request.get("page") == null || !StringUtil.isNumberic(String.valueOf(request.get("page")))) {
            request.put("page", 1);
        }
        if (!request.containsKey("pageCount") || request.get("pageCount") == null || !StringUtil.isNumberic(String.valueOf(request.get("pageCount")))) {
            request.put("pageCount", 0);
        }
        int page = Math.max(Integer.parseInt(request.get("page").toString()), 1);
        int limit = Math.max(Integer.parseInt(request.get("pageCount").toString()), 0);
        //查询参数
        if (request.containsKey("flowPathId") && request.get("flowPathId") != null) {
            condition.setFlowPathId(Integer.parseInt(request.get("flowPathId").toString()));
        }
        if (request.containsKey("type") && request.get("type") != null) {
            condition.setType(Integer.parseInt(request.get("type").toString()));
        }
        if (request.containsKey("typeList") && request.get("typeList") != null) {
            condition.setTypeList((ArrayList<Integer>) request.getOrDefault("typeList", new ArrayList<Integer>()));
        }
        if (request.containsKey("uuid") && request.get("uuid") != null) {
            condition.setUuid(request.get("uuid").toString());
        }
        if (request.containsKey("uuidList") && request.get("uuidList") != null) {
            condition.setUuidList((ArrayList<String>) request.getOrDefault("uuidList", new ArrayList<Integer>()));
        }
        if (request.containsKey("title") && request.get("title") != null) {
            condition.setTitle(request.get("title").toString());
        }
        if (request.containsKey("showName") && request.get("showName") != null) {
            condition.setShowName(request.get("showName").toString());
        }
        if (request.containsKey("companyIdList") && request.get("companyIdList") != null) {
            condition.setCompanyIdList((ArrayList<String>) request.getOrDefault("companyIdList", new ArrayList<Integer>()));
        }
        ArrayList<Integer> flowPathIdList = new ArrayList<>();
        if (request.containsKey("flowPathIdList")) {
            flowPathIdList = (ArrayList<Integer>) request.getOrDefault("flowPathIdList", new ArrayList<>());
        }
        if (request.containsKey("flowIdList")) {
            List<String> flowIdList = (ArrayList<String>) request.getOrDefault("flowIdList", new ArrayList<>());
            if (!flowIdList.isEmpty()) {
                List<FlowPathDAO> flowPathDAOList = flowPathService.getFlowPathList(GetFlowPathCondition.builder()
                        .flowIdList(flowIdList)
                        .build());
                if (!flowPathIdList.isEmpty()) {
                    //求交集
                    flowPathIdList.retainAll(flowPathDAOList.stream().map(FlowPathDAO::getId).collect(Collectors.toList()));
                } else {
                    flowPathIdList = (ArrayList<Integer>) flowPathDAOList.stream().map(FlowPathDAO::getId).collect(Collectors.toList());
                }
            }
        }
        if (!flowPathIdList.isEmpty()) {
            condition.setFlowPathIdList(flowPathIdList.stream().distinct().collect(Collectors.toList()));
        }

        /*
         * 查询字段
         */
        List<String> selectList = new ArrayList<>();
        if (request.containsKey("selectList")) {
            List<String> selectListItem = (ArrayList<String>) request.getOrDefault("selectList", new ArrayList<>());
            selectList.addAll(selectListItem);
        }

        request.remove("flowIdList");
        request.remove("flowPathIdList");
        request.remove("typeList");
        request.remove("uuidList");
        request.remove("companyIdList");
        request.remove("page");
        request.remove("pageCount");
        request.remove("selectList");

        //扩展字段查询参数
        ArrayList<String> flowPathListColumnList = new ArrayList<>(Arrays.asList(ApproveConstant.flowPathListColumnList));
        Map<String, String> extraContentMap = new HashMap<>();
        for (Map.Entry<String, Object> extraContent : request.entrySet()) {
            if (!flowPathListColumnList.contains(extraContent.getKey())) {
                if (Objects.equals(extraContent.getKey(), "showName")) continue;
                extraContentMap.put(extraContent.getKey(), String.valueOf(extraContent.getValue()));
            }
        }
        condition.setExtraContentMap(extraContentMap);
        //查询数据
        int dataCount = flowPathListService.getFlowPathListCount(condition);
        if (dataCount == 0) {
            return DResult.success(DataListDTO.<FlowPathListDTO>builder().build());
        }
        if (limit < 1) {
            return LResult.success(new ArrayList<>(), dataCount);
        }
        List<FlowPathListDAO> flowPathListDAOList = flowPathListService.getFlowPathListList(condition, (page - 1) * limit, limit);
        //获取flowPathId
        Set<Integer> flowPathIdSet = flowPathListDAOList.stream().map(FlowPathListDAO::getFlowPathId).collect(Collectors.toSet());
        //取关联的flow_path_history
        Map<Integer, FlowPathHistoryDAO> flowPathHistoryDAOMap = flowPathHistoryService.getFlowPathHistoryListTwo(GetFlowPathHistoryListCondition.builder()
                .flowPathIdList(new ArrayList<>(flowPathIdSet))
                .build()).stream().collect(Collectors.toMap(FlowPathHistoryDAO::getFlowPathId, dao -> dao, (v1, v2) -> v1));

        // 优化接口
        //Map<Integer, FlowPathHistoryDAO> flowPathHistoryDAOMap = flowPathHistoryService.getFlowPathHistoryListTwo(GetFlowPathHistoryListCondition.builder()
        //.flowPathIdList(new ArrayList<>(flowPathIdSet))
        //.build()).stream().collect(Collectors.toMap(FlowPathHistoryDAO::getFlowPathId, dao -> dao, (v1, v2) -> v1));


        //取关联的flow_path
//        Map<Integer, FlowPathDAO> flowPathDAOMap = flowPathService.getFlowPathList(GetFlowPathCondition.builder()
//                .idList(new ArrayList<>(flowPathIdSet))
//                .build()).stream().collect(Collectors.toMap(FlowPathDAO::getId, dao1 -> dao1, (v1, v2) -> v2));

        if (flowPathIdSet.isEmpty()) {
            return LResult.success(new ArrayList<>(), 0);
        }

        /*
         * 查询审批数据
         */
        QueryWrapper<FlowPathEntity> flowPathEntityQueryWrapper = new QueryWrapper<>();
        if (!selectList.isEmpty()) {
            Class<FlowPathEntity> flowPathEntityClass = FlowPathEntity.class;
            Field[] fields = flowPathEntityClass.getDeclaredFields();
            List<String> columnList = Arrays.stream(fields).map(Field::getName).collect(Collectors.toList());
            List<String> columnNameList = new ArrayList<>();
            for (String column : selectList) {
                if (!columnList.contains(column)) {
                    continue;
                }
                String symbolCase = StrUtil.toSymbolCase(column, '_');
                columnNameList.add("`" + symbolCase + "`");

            }
            if (!columnNameList.isEmpty()) {
                flowPathEntityQueryWrapper.select(columnNameList);
            }
        }
        LambdaQueryWrapper<FlowPathEntity> flowPathEntityLambdaQueryWrapper = flowPathEntityQueryWrapper.lambda();
        flowPathEntityLambdaQueryWrapper.in(FlowPathEntity::getId, flowPathIdSet);

        CompletableFuture<List<FlowPathEntity>> listCompletableFuture = CompletableFuture.supplyAsync(() -> flowPathService.list(flowPathEntityLambdaQueryWrapper));

        // 优化接口
        //Map<Integer, FlowPathDAO> flowPathDAOMap = flowPathService.getFlowPathListTwo(GetFlowPathCondition.builder()
        //.idList(new ArrayList<>(flowPathIdSet))
        //.build()).stream().collect(Collectors.toMap(FlowPathDAO::getId, dao1 -> dao1, (v1, v2) -> v2));

        List<FlowPathStepDTO> flowPathStepList = flowPathStepService.getFlowPathStepList(
                GetFlowPathStepCondition
                        .builder()
                        .flowPathIdList(new ArrayList<>(flowPathIdSet))
                        .build()
        );
        Map<Integer, List<FlowPathStepDTO>> flowPathStepMap = flowPathStepList.stream().collect(Collectors.groupingBy(FlowPathStepDTO::getFlowPathId));

        List<FlowPathEntity> flowPathList = listCompletableFuture.join();
        Map<Integer, List<FlowPathEntity>> flowPathListById = flowPathList.stream().collect(Collectors.groupingBy(FlowPathEntity::getId));


        /*
         * 拼装返回集合
         */
        List<FlowPathListDTO> returnFlowPathListDTO = new ArrayList<>();
        for (FlowPathListDAO dao : flowPathListDAOList) {
            FlowPathListDTO dto = FlowPathListDTO.create(dao);
            dto.setStepInfoList(flowPathStepMap.get(dao.getFlowPathId()));
            List<FlowPathEntity> flowPathListItem = flowPathListById.get(dao.getFlowPathId());
            if (flowPathListItem != null) {
                FlowPathEntity flowPathEntity = flowPathListItem.get(0);
                if (flowPathEntity.getExtraContent() != null) {
                    dto.setExtraContent(JSONUtil.parseObj(JSONUtil.parseObj(flowPathEntity.getExtraContent())));
                }
                dto.setCreateUuid(Optional.ofNullable(flowPathEntity.getUuid()).orElse(""));
                dto.setCreateAvatar(Optional.ofNullable(flowPathEntity.getAvatar()).orElse(""));
                dto.setFlowCreateTime(TimeUtil.formatLocalDateTime(flowPathEntity.getCreateTime()));
                String tempInfo = flowPathEntity.getTempInfo();
                if (JSONUtil.isTypeJSONArray(tempInfo)) {
                    dto.setTempInfo(JSONUtil.parseArray(tempInfo));
                }
                String tempData = flowPathEntity.getTempData();
                if (JSONUtil.isTypeJSONObject(tempData)) {
                    dto.setTempData(JSONUtil.parseObj(tempData));
                }
                String tempShowData = flowPathEntity.getTempShowData();
                if (JSONUtil.isTypeJSONArray(tempShowData)) {
                    dto.setTempShowData(JSONUtil.parseArray(tempShowData));
                }
                dto.setStatus(Optional.ofNullable(flowPathEntity.getStatus()).orElse(4));
                dto.setCreateTime(TimeUtil.toStringDate(dao.getCreateTime()));
                dto.setIsInterrupt(flowPathEntity.getIsInterrupt());
                dto.setWithdrawPathId(flowPathEntity.getWithdrawPathId());
            }
            FlowPathHistoryDAO flowPathHistoryDAO = flowPathHistoryDAOMap.get(dao.getFlowPathId());
            if (flowPathHistoryDAO != null) {
                dto.setLatestTime(TimeUtil.toStringDate(flowPathHistoryDAO.getCreateTime()));
            }
            returnFlowPathListDTO.add(dto);
        }
        return LResult.success(returnFlowPathListDTO, dataCount);
    }

    /**
     * 审批列表
     *
     * @return
     */
    @RequestMapping("/flowPathlist")
    @LogRecord(module = "审批模块", name = "审批列表(简)", type = ApiTypeConstant.SELECT)
    public Result flowPathList(@RequestBody(required = false) FlowPathListQueryDTO flowPathListQueryDTO) {
        if (flowPathListQueryDTO == null) {
            flowPathListQueryDTO = new FlowPathListQueryDTO();
        }
        log.info("审批列表入参 -> {}", JSONUtil.toJsonStr(flowPathListQueryDTO));
        DataList<FLowPathListVO> fLowPathListVODataList = flowPathListService.flowPathList(flowPathListQueryDTO);
        return LResult.success(fLowPathListVODataList);
    }

    /**
     * 审批人审批
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/approve")
    @LogRecord(module = "审批模块", name = "进行审批", type = ApiTypeConstant.UPDATE)
    public Result approve(@RequestBody MsFlowPathApproveRequest request) {
        if (request.getFlowPathId() == 0 || request.getAction() == 0) {
            return DResult.fail("参数错误");
        }
        // 判断是否有更新的 审批流程数据
        if (request.getTempShowData() != null || request.getTempData() != null || request.getExtraContent() != null) {
            FlowPathDAO flowPathDAO = FlowPathDAO.builder().id(request.getFlowPathId()).build();
            if (request.getTempShowData() != null) {
                flowPathDAO.setTempShowData(JSONUtil.toJsonStr(request.getTempShowData()));
            }
            if (request.getTempData() != null) {
                flowPathDAO.setTempData(JSONUtil.toJsonStr(request.getTempData()));
            }
            if (request.getExtraContent() != null) {
                flowPathDAO.setExtraContent(JSONUtil.toJsonStr(request.getExtraContent()));
            }
            flowPathService.updateFlowPath(flowPathDAO);
        }
        /*
         * 获取最新的审批状态
         */
        request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
        request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
        //审批判定 及执行审批
        log.info("进行审批入参 -> {}", JSONUtil.toJsonStr(request));
        goFlowService.goFlowV2(request.getFlowPathId(), request.getAction(), request.getUuid(), request.getShowName(), request.getReason(), request.getAppChannelId(), request.getDataChannelId(), "", false);
        FlowPathDTO flowPathDTO = flowPathService.getFlowPathDetail(request.getFlowPathId());
        return DResult.success(flowPathDTO);
    }

    /**
     * 审批备注
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/remark")
    @LogRecord(module = "审批模块", name = "审批备注", type = ApiTypeConstant.INSERT)
    public Result remark(@RequestBody MsFlowPathRemarkRequest request, @RequestHeader MultiValueMap<String, String> header) {
        if (request.getFlowPathId() == 0 || StringUtil.isEmpty(request.getShowName()) || StringUtil.isEmpty(request.getUuid()) || StringUtil.isEmpty(request.getRemark())) {
            return DResult.fail("参数错误");
        }
        if (!StringUtil.isEmpty(header.get("x-app_channel_id").toString())) {
            request.setAppChannelId(header.get("x-app_channel_id").toString());
        }
        if (!StringUtil.isEmpty(header.get("x-data_channel_id").toString())) {
            request.setDataChannelId(header.get("x-data_channel_id").toString());
        }
        log.info("审批备注入参 -> {}", JSONUtil.toJsonStr(request));
        return DResult.success(flowPathService.remark(request));
    }

    /**
     * 获取审批流实例详情
     */
    @RequestMapping("/info")
    @LogRecord(module = "审批模块", name = "审批详情", type = ApiTypeConstant.INFO)
    public Result info(@RequestBody MsFlowPathInfoRequest request) {
        if (request.getFlowPathId() <= 0) {
            return DResult.fail("参数错误");
        }
        log.info("审批详情入参 -> {}", JSONUtil.toJsonStr(request));
        return DResult.success(flowPathService.getFlowPathDetail(request.getFlowPathId()));
    }

    /**
     * 获取审批流实例详情
     */
    @RequestMapping("/batchInfo")
    @LogRecord(module = "审批模块", name = "批量获取审批详情", type = ApiTypeConstant.INFO)
    public Result batchInfo(@RequestBody BatchFlowPathInfoDTO request) {

        for (Integer flowPathId : request.getFlowPathIds()) {
            if (flowPathId <= 0) {
                return DResult.fail("参数错误 : " + flowPathId);
            }
        }

        log.info("审批详情入参 -> {}", JSONUtil.toJsonStr(request));

        List<FlowPathDTO> flowPathDetails = flowPathService.getFlowPathDetails(request.getFlowPathIds());

        Map<Integer, FlowPathDTO> flowPathMap = flowPathDetails.stream().collect(Collectors.toMap(
                FlowPathDTO::getId,
                item -> item
        ));

        return DResult.success(flowPathMap);
    }

    /**
     * 撤回
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/withdraw")
    @LogRecord(module = "审批模块", name = "审批撤回", type = ApiTypeConstant.UPDATE)
    public Result withdraw(@RequestBody MsFlowPathWithdrawRequest request) {
        if (request.getFlowPathId() <= 0 || StringUtil.isEmpty(request.getShowName()) || StringUtil.isEmpty(request.getUuid())) {
            return DResult.fail("参数错误");
        }
        log.info("审批撤回入参 -> {}", JSONUtil.toJsonStr(request));
        return DResult.success(approvealService.withdraw(request));
    }

    /**
     * 转审
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/transfer")
    @LogRecord(module = "审批模块", name = "审批转审", type = ApiTypeConstant.UPDATE)
    public Result transfer(@RequestBody MsFlowPathTransferRequest request) {
        if (request.getFlowPathId() <= 0 || StringUtil.isEmpty(request.getUuid())
                || request.getTarget() == null || StringUtil.isEmpty(request.getTarget().getUuid()) || StringUtil.isEmpty(request.getTarget().getShowName())) {
            return DResult.fail("参数错误");
        }
        String redisKey = String.format(ApproveConstant.APPROVE_LOCK_PREFIX, request.getFlowPathId());
        long redisValue = System.currentTimeMillis();
        boolean getLockResult = redisCache.setIfAbsent(redisKey, redisValue, ApproveConstant.APPROVE_LOCK_EXPIRE_TIME);
        if (!getLockResult) {
            throw new BsException("该审批正在处理中，请稍后重试");
        }
        FlowPathDTO flowPathDTO = null;
        try{
            flowPathDTO = flowPathService.transfer(request);
        } finally {
            redisCache.deleteIfExists(redisKey, redisValue);
        }
        return DResult.success(flowPathDTO);
    }

    /**
     * 委派/加签
     *
     * @return
     */
    @RequestMapping("/appoint")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批加签", type = ApiTypeConstant.INSERT)
    public Result appoint(@RequestBody MsFlowPathAppointRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getFlowPathId()) || request.getFlowPathId() == 0) {
            return DResult.fail("审批流程ID不能为空");
        }
        if (StringUtil.isEmpty(request.getUuid())) {
            return DResult.fail("当前操作人ID不能为空");
        }
        if (StringUtil.isEmpty(request.getShowName())) {
            return DResult.fail("当前操作人名称不能为空");
        }
        if (Objects.isNull(request.getTarget())) {
            return DResult.fail("节点人员不能为空");
        }
        if (Objects.isNull(request.getLocation())) {
            return DResult.fail("加签位置不合法");
        }
        if (Objects.isNull(request.getStepInfo())) {
            return DResult.fail("节点信息不能为空");
        }
        String redisKey = String.format(ApproveConstant.APPROVE_LOCK_PREFIX, request.getFlowPathId());
        long redisValue = System.currentTimeMillis();
        boolean getLockResult = redisCache.setIfAbsent(redisKey, redisValue, ApproveConstant.APPROVE_LOCK_EXPIRE_TIME);
        if (!getLockResult) {
            throw new BsException("该审批正在处理中，请稍后重试");
        }
        FlowPathDTO flowPathDTO = null;
        try{
            flowPathDTO = flowPathService.appoint(request);
        } finally {
            redisCache.deleteIfExists(redisKey, redisValue);
        }
        return DResult.success(flowPathDTO);
    }

    /**
     * 回退
     *
     * @return
     */
    @RequestMapping("/returnBack")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批回退", type = ApiTypeConstant.UPDATE)
    public Result returnBack(@RequestBody(required = false) MsReturnBackRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getFlowPathId()) || request.getFlowPathId() == 0) {
            return DResult.fail("审批流程ID不能为空");
        }
        if (StringUtil.isEmpty(request.getUuid())) {
            return DResult.fail("当前操作人ID不能为空");
        }
        if (StringUtil.isEmpty(request.getShowName())) {
            return DResult.fail("当前操作人名称不能为空");
        }
        if (Objects.isNull(request.getReturnStatus())) {
            return DResult.fail("退回方式不合法");
        }
        String redisKey = String.format(ApproveConstant.APPROVE_LOCK_PREFIX, request.getFlowPathId());
        long redisValue = System.currentTimeMillis();
        boolean getLockResult = redisCache.setIfAbsent(redisKey, redisValue, ApproveConstant.APPROVE_LOCK_EXPIRE_TIME);
        if (!getLockResult) {
            throw new BsException("该审批正在处理中，请稍后重试");
        }
        FlowPathDTO flowPathDTO = null;
        try{
            flowPathDTO = flowPathService.returnBack(request);
        } finally {
            redisCache.deleteIfExists(redisKey, redisValue);
        }
        return DResult.success(flowPathDTO);
    }

    /**
     * 修改流程表单内容并将流程重新发起
     *
     * @param request
     * @return
     */
    @RequestMapping("/updateFlowPath")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批重新发起", type = ApiTypeConstant.INSERT)
    public Result updateFlowPath(@RequestBody(required = false) UpdateFlowPathRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getFlowPathId())) {
            return DResult.fail("审批流ID不能为空");
        }
        if (StringUtil.isEmpty(request.getUuid())) {
            return DResult.fail("操作人(用户)ID不能为空");
        }
        if (Objects.isNull(request.getTempShowData()) || Objects.isNull(request.getFreeStepInfo())) {
            return DResult.fail("必要参数不能为空");
        }
        return DResult.success(flowPathService.updateFlowPathAndCommit(request));
    }

    /**
     * 审批内容修改
     *
     * @param request
     * @return
     */
    @PostMapping("/update")
    @LogRecord(module = "审批模块", name = "审批内容修改", type = ApiTypeConstant.UPDATE)
    public Result flowPathUpdate(@RequestBody(required = false) FlowPathUpdateRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getId())) {
            return DResult.fail("数据ID不能为空");
        }
        FlowPathDAO flowPathDAO = new FlowPathDAO();
        BeanUtils.copyProperties(request, flowPathDAO);
        flowPathService.updateFlowPath(flowPathDAO);
        return DResult.success();
    }

    /**
     * 强制覆盖节点办理人
     *
     * @param updateStepNotificationRequest
     */
    @RequestMapping("/updateStepNotification")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批节点办理人覆盖", type = ApiTypeConstant.UPDATE)
    public Result updateStepNotification(@RequestBody(required = false) UpdateStepNotificationRequest updateStepNotificationRequest) {
        if (Objects.isNull(updateStepNotificationRequest)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(updateStepNotificationRequest.getFlowPathId())) {
            return DResult.fail("流程ID不能为空");
        }
        if (Objects.isNull(updateStepNotificationRequest.getStepNo())) {
            return DResult.fail("流程节点步骤不能为空");
        }
        log.info("批节点办理人覆盖入参 -> {}", JSONUtil.toJsonStr(updateStepNotificationRequest));
        Map<String, List<String>> changeUuidMap = flowPathStepNotificationService.coverNotification(updateStepNotificationRequest);

        Map<String, Object> stepExtraContent = updateStepNotificationRequest.getStepExtraContent();
        if (Objects.nonNull(stepExtraContent)) {
            flowPathStepService.update(
                    FlowPathStepDAO.builder().extraContent(JSONUtil.toJsonStr(stepExtraContent)).build(),
                    GetFlowPathStepCondition.builder().
                            flowPathId(updateStepNotificationRequest.getFlowPathId()).stepNo(updateStepNotificationRequest.getStepNo()).status(ApproveConstant.flowPathStatus.WAITING.getCode()).build());
        }
        if (Objects.nonNull(updateStepNotificationRequest.getIsNextStep()) && updateStepNotificationRequest.getIsNextStep()) {
            approvealService.addFlowPathListAndNotify(updateStepNotificationRequest, changeUuidMap);
        }
          //这里重新查库返回值业务没有用到，删除掉查库操作只返回一个实例化的对象
//        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(updateStepNotificationRequest.getFlowPathId());
        FlowPathDTO flowPathDetail=new FlowPathDTO();
        return DResult.success(flowPathDetail);
    }


    /**
     * 批量覆盖审批节点的办理人和通知配置，并可批量更新 extraContent
     */
    @PostMapping("/batchUpdateStepNotification")
    public Result batchUpdateStepNotification(@RequestBody(required = false) List<UpdateStepNotificationRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return DResult.fail("请求列表不能为空");
        }

        // 1. 参数校验
        for (UpdateStepNotificationRequest req : requests) {
            if (req.getFlowPathId() == null) {
                return DResult.fail("流程ID不能为空");
            }
            if (req.getStepNo() == null) {
                return DResult.fail("流程节点步骤不能为空");
            }
        }

        // 2. 批量覆盖通知
        for (UpdateStepNotificationRequest req : requests) {
            // 没有微服务调用可以直接循环
            flowPathStepNotificationService.coverNotification(req);
        }

        // 3. 收集需要更新 extraContent 的请求，构建批量更新参数
        List<FlowPathStepDAO> daosToUpdate = new ArrayList<>();
        List<GetFlowPathStepCondition> conditions = new ArrayList<>();
        for (UpdateStepNotificationRequest req : requests) {
            Map<String, Object> extra = req.getStepExtraContent();
            if (extra != null) {
                FlowPathStepDAO dao = FlowPathStepDAO.builder()
                        .flowPathId(req.getFlowPathId())
                        .extraContent(JSONUtil.toJsonStr(extra))
                        .build();

                GetFlowPathStepCondition cond = GetFlowPathStepCondition.builder()
                        .flowPathId(req.getFlowPathId())
                        .stepNo(req.getStepNo())
                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                        .build();

                daosToUpdate.add(dao);
                conditions.add(cond);
            }
        }
        // 4. 批量更新 extraContent
        if (!daosToUpdate.isEmpty()) {
            flowPathStepService.updateBatch(daosToUpdate, conditions);
        }

        // 5. 批量拉取最新的流程详情
        List<Integer> flowPathIds = requests.stream()
                .map(UpdateStepNotificationRequest::getFlowPathId)
                .distinct()
                .collect(Collectors.toList());
        List<FlowPathDTO> details = flowPathService.getFlowPathDetails(flowPathIds);

        return DResult.success(details);
    }


    /**
     * 增加 审批流程列表接口
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/list")
    @LogRecord(module = "审批模块", name = "审批列表", type = ApiTypeConstant.SELECT)
    public Result list(@RequestBody MsFlowPathListRequest request) {
        GetFlowPathCondition condition = GetFlowPathCondition.builder().build();
        // 三个条件互斥
        if (request.getFlowPathId() > 0) {
            condition.setId(request.getFlowPathId());
        } else if (request.getFlowPathIdList() != null && !request.getFlowPathIdList().isEmpty()) {
            condition.setIdList(request.getFlowPathIdList());
        } else if ((request.getFlowIdList() != null && !request.getFlowIdList().isEmpty()) || (request.getTempIdList() != null && !request.getTempIdList().isEmpty())) {
            List<String> flowIdAllList = new ArrayList<>();
            if ((request.getFlowIdList() != null && !request.getFlowIdList().isEmpty())) {
                flowIdAllList.addAll(request.getFlowIdList());
            }
            if ((request.getTempIdList() != null && !request.getTempIdList().isEmpty())) {
                GetFlowCondition getFlowCondition = new GetFlowCondition();
                getFlowCondition.setTempIdList(request.getTempIdList());
                Integer flowCount = flowService.getFlowCount(getFlowCondition);
                if (flowCount > 0) {
                    List<FlowDTO> flowList = flowService.getFlowList(getFlowCondition);
                    List<String> collect = flowList.stream().map(FlowDTO::getId).collect(Collectors.toList());
                    flowIdAllList.addAll(collect);
                } else {
                    return LResult.success();
                }
            }
            condition.setFlowIdList(flowIdAllList);
        }
        // 设置扩展信息查询字段
        if (!StringUtil.isEmpty(request.getCompanyId())) {
            condition.setCompanyId(request.getCompanyId());
        }
        if (request.getTitle() != null) {
            condition.setTitle(request.getTitle());
        }
        if (request.getStatus() != null) {
            condition.setStatus(request.getStatus());
        }
        if (request.getInitiatorUserIdList() != null && !request.getInitiatorUserIdList().isEmpty()) {
            condition.setInitiatorUserIdList(request.getInitiatorUserIdList());
        }
        if (!StringUtil.isEmpty(request.getCreateStartTime()) && !StringUtil.isEmpty(request.getCreateEndTime())) {
            condition.setCreateStartTime(request.getCreateStartTime());
            condition.setCreateEndTime(request.getCreateEndTime());
        }
        if (!StringUtil.isEmpty(request.getUpdateStartTime()) && !StringUtil.isEmpty(request.getUpdateEndTime())) {
            condition.setUpdateStartTime(request.getUpdateStartTime());
            condition.setUpdateEndTime(request.getUpdateEndTime());
        }
        if (request.getIsInterrupt() != null) {
            condition.setIsInterrupt(request.getIsInterrupt());
        }
        if (request.getWithdrawPathId() != null) {
            condition.setWithdrawPathId(request.getWithdrawPathId());
        }
        condition.setAppChannelId(request.getAppChannelId() == null ? null : request.getAppChannelId());
        condition.setDataChannelId(request.getDataChannelId() == null ? ServletContext.getDataChannelId() : request.getDataChannelId());

        int page = Math.max(request.getPage(), 1);
        int limit = Math.max(request.getPageCount(), 0);
        // 获取所有符合条件的审批流程
        int flowPathCount = flowPathService.getFlowPathCount(condition);
        if (flowPathCount == 0) {
            return LResult.success();
        }
        List<FlowPathDAO> flowPathListDao = flowPathService.getFlowPathList(condition, (page - 1) * limit, limit);
        List<FlowPathDTO> flowPathListDto = flowPathListDao.stream().map(FlowPathDTO::create).collect(Collectors.toList());
        List<Integer> flowPathIdList = flowPathListDto.stream().map(FlowPathDTO::getId).collect(Collectors.toList());
        // 获取所有 审批流程的步骤
        List<FlowPathStepDTO> flowPathStepDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathIdList(flowPathIdList)
                .build());

        Map<Integer, List<FlowPathStepDTO>> flowPathStepDTOMap = flowPathStepDTOList.stream().collect(Collectors.groupingBy(FlowPathStepDTO::getFlowPathId));

        List<FlowPathDTO> flowPathList = flowPathListDto.stream().peek(dto -> {
            List<FlowPathStepDTO> flowPathStepDTOList1 = flowPathStepDTOMap.getOrDefault(dto.getId(), null);
            if (flowPathStepDTOList1 != null) {
                dto.setStepInfoList(flowPathStepDTOList1);
            }
        }).collect(Collectors.toList());


        return LResult.success(flowPathList, flowPathCount);
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateFlowPathByBack")
    @LogRecord(module = "审批模块", name = "审批修改", type = ApiTypeConstant.UPDATE)
    public Result updateByBack(@RequestBody Map<String, Object> flowPathDAOMap) {
        Object createTime = flowPathDAOMap.remove("createTime");
        Object updateTime = flowPathDAOMap.remove("updateTime");
        FlowPathDAO flowPathDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathDAOMap), FlowPathDAO.class);

        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }
        if (updateTime != null && !StringUtil.isEmpty(updateTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(updateTime.toString());
                flowPathDAO.setUpdateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        if (flowPathDAO.getId() == null || flowPathDAO.getId() <= 0) {
            return DResult.fail("缺少必要参数");
        }
        flowPathService.updateFlowPath(flowPathDAO);
        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(flowPathDAO.getId());
        return DResult.success(flowPathDetail);
    }

    /**
     * 强制更新审批流节点
     *
     * @param flowPathStepDAOMap
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateFlowPathStepByBack")
    @LogRecord(module = "审批模块", name = "审批节点修改", type = ApiTypeConstant.UPDATE)
    public Result updateFlowPathStepByBack(@RequestBody Map<String, Object> flowPathStepDAOMap) {
        Object createTime = flowPathStepDAOMap.remove("createTime");
        Object updateTime = flowPathStepDAOMap.remove("updateTime");
        FlowPathStepDAO flowPathStepDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathStepDAOMap), FlowPathStepDAO.class);

        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathStepDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }
        if (updateTime != null && !StringUtil.isEmpty(updateTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(updateTime.toString());
                flowPathStepDAO.setUpdateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        if (flowPathStepDAO.getId() == null || flowPathStepDAO.getId() <= 0) {
            return DResult.fail("缺少必要参数");
        }
        flowPathStepService.update(flowPathStepDAO, GetFlowPathStepCondition.builder().id(flowPathStepDAO.getId()).build());
        List<FlowPathStepDTO> flowPathStepList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder().id(flowPathStepDAO.getId()).build());
         return DResult.success(flowPathStepList);
    }

    /**
     * 节点强制变动
     *
     * @param flowPathStepDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/flowPathStep")
    @LogRecord(module = "审批模块", name = "审批节点修改", type = ApiTypeConstant.UPDATE)
    public Result flowPathStep(@RequestBody(required = false) FlowPathStepDTO flowPathStepDTO) {
        if (Objects.isNull(flowPathStepDTO)) {
            return DResult.fail("请求参数不能为空");
        }
        log.info("审批节点修改入参 -> {}", JSONUtil.toJsonStr(flowPathStepDTO));
        approvealService.flowPathStep(flowPathStepDTO);
        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(flowPathStepDTO.getFlowPathId());
        return DResult.success(flowPathDetail);
    }


    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateFlowPathStepNotificationByBack")
    @LogRecord(module = "审批模块", name = "审批节点人修改", type = ApiTypeConstant.UPDATE)
    public Result updateFlowPathStepNotificationByBack(@RequestBody Map<String, Object> flowPathStepNotificationDAOMap) {
        Object createTime = flowPathStepNotificationDAOMap.remove("createTime");
        Object updateTime = flowPathStepNotificationDAOMap.remove("updateTime");
        FlowPathStepNotificationDAO flowPathStepNotificationDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathStepNotificationDAOMap), FlowPathStepNotificationDAO.class);

        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathStepNotificationDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }
        if (updateTime != null && !StringUtil.isEmpty(updateTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(updateTime.toString());
                flowPathStepNotificationDAO.setUpdateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        if (flowPathStepNotificationDAO.getId() == null || flowPathStepNotificationDAO.getId() <= 0) {
            return DResult.fail("缺少必要参数");
        }
        flowPathStepNotificationService.update(flowPathStepNotificationDAO, GetFlowPathStepNotificationCondition.builder().id(flowPathStepNotificationDAO.getId()).build());
        List<FlowPathStepNotificationDTO> flowPathStepNotificationList = flowPathStepNotificationService.getFlowPathStepNotificationList(GetFlowPathStepNotificationCondition.builder().id(flowPathStepNotificationDAO.getId()).build());
        return DResult.success(flowPathStepNotificationList);
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addFlowPathListByBack")
    @LogRecord(module = "审批模块", name = "审批办理人新增", type = ApiTypeConstant.INSERT)
    public Result addFlowPathListByBack(@RequestBody Map<String, Object> flowPathListDAOMap) {
        Object createTime = flowPathListDAOMap.remove("createTime");
        FlowPathListDAO flowPathListDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathListDAOMap), FlowPathListDAO.class);
        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathListDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        return DResult.success(flowPathListService.addFlowPathList(
                flowPathListDAO.getTitle(),
                flowPathListDAO.getFlowPathId(),
                flowPathListDAO.getType(),
                flowPathListDAO.getUuid(),
                flowPathListDAO.getExtraContent(),
                flowPathListDAO.getAppChannelId(),
                flowPathListDAO.getDataChannelId(),
                flowPathListDAO.getCreateTime()
        ));
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/uploadFlowPathListByBack")
    @LogRecord(module = "审批模块", name = "审批办理人修改", type = ApiTypeConstant.UPDATE)
    public Result uploadFlowPathListByBack(@RequestBody Map<String, Object> flowPathListDAOMap) {
        Object createTime = flowPathListDAOMap.remove("createTime");
        FlowPathListDAO flowPathListDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathListDAOMap), FlowPathListDAO.class);
        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathListDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        if (flowPathListDAO.getId() == null || flowPathListDAO.getId() <= 0) {
            return DResult.fail("缺少必要参数");
        }
        return DResult.success(flowPathListService.updateFlowPathList(
                flowPathListDAO,
                GetFlowPathListDAOListCondition.builder().id(flowPathListDAO.getId()).build()
        ));
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/addFlowPathHistoryByBack")
    @LogRecord(module = "审批模块", name = "审批历史记录新增", type = ApiTypeConstant.INSERT)
    public Result addFlowPathHistoryByBack(@RequestBody Map<String, Object> flowPathHistoryDAOMap) {
        Object createTime = flowPathHistoryDAOMap.remove("createTime");
        FlowPathHistoryDAO flowPathHistoryDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathHistoryDAOMap), FlowPathHistoryDAO.class);
        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathHistoryDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        return DResult.success(flowPathHistoryService.addFlowPathHistory(
                flowPathHistoryDAO.getFlowPathId(),
                flowPathHistoryDAO.getTitle(),
                flowPathHistoryDAO.getType(),
                flowPathHistoryDAO.getAction(),
                flowPathHistoryDAO.getUuid(),
                flowPathHistoryDAO.getReason(),
                flowPathHistoryDAO.getCreateTime()
        ));
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateFlowPathHistoryByBack")
    @LogRecord(module = "审批模块", name = "审批办理人修改", type = ApiTypeConstant.UPDATE)
    public Result updateFlowPathHistoryByBack(@RequestBody Map<String, Object> flowPathHistoryDAOMap) {

        Object createTime = flowPathHistoryDAOMap.remove("createTime");
        FlowPathHistoryDAO flowPathHistoryDAO = StringUtil.jsonDecode(JSONUtil.toJsonStr(flowPathHistoryDAOMap), FlowPathHistoryDAO.class);
        if (createTime != null && !StringUtil.isEmpty(createTime.toString())) {
            String pattern = "yyyy-MM-dd HH:mm:ss"; // 字符串的日期格式
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            try {
                Date date = sdf.parse(createTime.toString());
                flowPathHistoryDAO.setCreateTime(date);
            } catch (ParseException e) {
                return DResult.fail("日期转换失败");
            }
        }

        if (flowPathHistoryDAO.getId() == null || flowPathHistoryDAO.getId() <= 0) {
            return DResult.fail("缺少必要参数");
        }
        int i = flowPathHistoryService.updateFlowPathHistory(flowPathHistoryDAO);
        List<FlowPathHistoryDAO> flowPathHistoryList = flowPathHistoryService.getFlowPathHistoryList(GetFlowPathHistoryListCondition.builder().id(flowPathHistoryDAO.getId()).build());
        return DResult.success(flowPathHistoryList);
    }

    /**
     * 节点拼接
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @PostMapping("/flowPathAddByCondition")
    @LogRecord(module = "审批模块", name = "审批节点拼接", type = ApiTypeConstant.INSERT)
    public Result flowPathAddByCondition(@RequestBody(required = false) MsFlowPathConditionRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getFlowPathId())) {
            return DResult.fail("审批流ID不能为空");
        }
        if (Objects.isNull(request.getTempData())) {
            return DResult.fail("模版参数不能为空");
        }
        if (Objects.isNull(request.getNodeAgentDTOList())) {
            return DResult.fail("节点信息不嫩为空");
        }
        approvealService.flowPathAddByCondition(request);
        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(request.getFlowPathId());
        return DResult.success(flowPathDetail);
    }


    /**
     * 分支变动
     *
     * @param request
     * @return
     */
    @PostMapping("/embranchmentUpdate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批分支变更", type = ApiTypeConstant.UPDATE)
    public Result embranchmentUpdate(@RequestBody(required = false) EmbranchmentUpdateRequest request) {
        if (Objects.isNull(request)) {
            return DResult.fail("请求参数不能为空");
        }
        if (Objects.isNull(request.getFlowPathId())) {
            return DResult.fail("审批ID不能为空");
        }
        if (Objects.isNull(request.getTempShowData())) {
            return DResult.fail("表单数据不能为空");
        }
        if (Objects.isNull(request.getTempData())) {
            return DResult.fail("表单内容不能为空");
        }
        if (Objects.isNull(request.getNodeAgentDTOList())) {
            return DResult.fail("变动节点数据不能为空");
        }
        log.info("分支变动如参数 -> {}", JSONUtil.toJsonStr(request));
        approvealService.embranchmentUpdate(request);
        //这里重新查库返回值业务没有用到，删除掉查库操作只返回一个实例化的对象
//        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(request.getFlowPathId());
        FlowPathDTO flowPathDetail=new FlowPathDTO();
        return DResult.success(flowPathDetail);
    }


    /**
     * 批量分支变动
     *
     * @param requests 批量请求列表
     * @return
     */
    @PostMapping("/batchEmbranchmentUpdate")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(module = "审批模块", name = "审批分支变更(批量)", type = ApiTypeConstant.UPDATE)
    public Result batchEmbranchmentUpdate(@RequestBody(required = false) List<EmbranchmentUpdateRequest> requests) {
        if (Objects.isNull(requests) || requests.isEmpty()) {
            return DResult.fail("请求参数不能为空");
        }

        // 使用 stream 过滤出合法的请求
        List<EmbranchmentUpdateRequest> validRequests = requests.stream()
                .filter(req -> Objects.nonNull(req.getFlowPathId()))
                .filter(req -> Objects.nonNull(req.getTempShowData()))
                .filter(req -> Objects.nonNull(req.getTempData()))
                .filter(req -> Objects.nonNull(req.getNodeAgentDTOList()))
                .collect(Collectors.toList());

        // 如果没有合法请求，返回错误
        if (validRequests.isEmpty()) {
            return DResult.fail("所有请求均不满足条件");
        }

        log.info("批量分支变动入参 -> {}", JSONUtil.toJsonStr(validRequests));

        // 提取 flowPathIds
        List<Integer> flowPathIds = requests.stream()
                .map(EmbranchmentUpdateRequest::getFlowPathId)
                .collect(Collectors.toList());

        // 批量执行分支更新
        approvealService.batchEmbranchmentUpdate(requests);

        // 批量获取详情
        List<FlowPathDTO> results = flowPathService.getFlowPathDetails(flowPathIds);

        // 提取所有成功的 flowPathId
        List<Integer> successfulFlowPathIds = results.stream()
                .map(FlowPathDTO::getId)
                .collect(Collectors.toList());

        // 将 flowPathId 返回
        Map<String, Object> response = new HashMap<>();
        response.put("results", results);
        response.put("successfulFlowPathIds", successfulFlowPathIds);

        return DResult.success(response);
    }

    /**
     * 审批列表统计
     *
     * @param requestMap
     * @return
     */
    @PostMapping("/flowPathStatistics")
    @LogRecord(module = "审批模块", name = "审批数量统计", type = ApiTypeConstant.SELECT)
    public Result flowPathStatistics(@RequestBody(required = false) Map<String, Object> requestMap) {
        if (Objects.isNull(requestMap)) {
            requestMap = new HashMap<>();
        }
        FlowPathStatisticsVO flowPathStatisticsVO = flowPathService.flowPathStatistics(requestMap);
        return DResult.success(flowPathStatisticsVO);
    }


    /**
     * 批量审批人审批
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/batchApprove")
    @LogRecord(module = "审批模块", name = "进行批量审批", type = ApiTypeConstant.UPDATE)
    public Result batchApprove(@RequestBody List<MsFlowPathApproveRequest> requests) {
        List<Result> results = new ArrayList<>();
        // 提取所有 flowPathIds
        List<Integer> flowPathIds = requests.stream()
                .map(MsFlowPathApproveRequest::getFlowPathId)
                .collect(Collectors.toList());

        // 批量获取流程数据
        List<FlowPathDAO> flowPathDAOs = flowPathService.batchGetFlowPaths(flowPathIds);
        Map<Integer, FlowPathDAO> flowPathMap = flowPathDAOs.stream()
                .collect(Collectors.toMap(FlowPathDAO::getId, Function.identity()));


        List<FlowPathDAO> toUpdate = new ArrayList<>();
        List<ApprovalRequest> toApprove = new ArrayList<>();

        // 处理每个请求的验证和准备
        for (MsFlowPathApproveRequest request : requests) {
            int flowPathId = request.getFlowPathId();
            FlowPathDAO flowPathDAO = flowPathMap.get(flowPathId);

            // 验证流程是否存在及状态
            if (flowPathDAO == null) {
                results.add(DResult.fail("参数错误"));
                continue;
            }
            if (flowPathDAO.getStatus() != ApproveConstant.flowPathStatus.WAITING.getCode()) {
                results.add(DResult.fail("审批流程已结束"));
                continue;
            }
            if (flowPathDAO.getIsInterrupt() == ApproveConstant.flowPathIsInterrupt.YES.getCode()) {
                results.add(DResult.fail("审批流程已被暂停"));
                continue;
            }

            // 如果需要更新流程数据
            if (request.getTempShowData() != null || request.getTempData() != null || request.getExtraContent() != null) {
                FlowPathDAO updateDAO = FlowPathDAO.builder().id(flowPathId).build();
                if (request.getTempShowData() != null) {
                    updateDAO.setTempShowData(JSONUtil.toJsonStr(request.getTempShowData()));
                }
                if (request.getTempData() != null) {
                    updateDAO.setTempData(JSONUtil.toJsonStr(request.getTempData()));
                }
                if (request.getExtraContent() != null) {
                    updateDAO.setExtraContent(JSONUtil.toJsonStr(request.getExtraContent()));
                }
                // 针对TempShowData啥的
                toUpdate.add(updateDAO);
            }

            // 准备审批请求
            request.setAppChannelId(Optional.ofNullable(request.getAppChannelId()).orElse(ServletContext.getAppChannelId()));
            request.setDataChannelId(Optional.ofNullable(request.getDataChannelId()).orElse(ServletContext.getDataChannelId()));
            toApprove.add(new ApprovalRequest(
                    request.getFlowPathId(), request.getAction(), request.getUuid(),
                    request.getShowName(), request.getReason(), request.getAppChannelId(),
                    request.getDataChannelId(), ""));
        }

        // 批量更新流程数据
        if (!toUpdate.isEmpty()) {
            flowPathService.batchUpdateFlowPaths(toUpdate);
        }

        // 给MQ
        String jsonString = JSON.toJSONString(toApprove);
        MQMs.push(
                BatchApprovalTopic.DEFAULT_TOPIC.getTopicNo(),
                jsonString,
                0 // 立即推送，无延时
        );


        // 批量获取更新后的流程详情
        List<FlowPathDTO> flowPathDTOs = flowPathService.getFlowPathDetails(flowPathIds);

        // 暂留结果 后续在返回结果添加上失败版本
        for (int i = 0; i < requests.size(); i++) {
            if (results.size() > i && results.get(i) != null) {
                continue; // 已标记为失败的请求
            }
            results.add(DResult.success(flowPathDTOs.get(i)));
        }

        return DResult.success(flowPathDTOs);
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/goFlow")
    @LogRecord(module = "审批模块", name = "审批", type = ApiTypeConstant.UPDATE)
    public boolean goFlow(@RequestBody String json) {
        // json判空
        if (json == null || json.isEmpty()) {
            return false;
        }
        JSONObject root = JSON.parseObject(json);
        // 得到 data 数组
        JSONArray dataArr = root.getJSONArray("data");
        if (dataArr == null || dataArr.isEmpty()) {
            return false;
        }
        // 直接转成 List<ApprovalRequest>
        List<ApprovalRequest> requests = dataArr.toJavaList(ApprovalRequest.class);

        //添加一个记录失败请求id的集合
        List<Integer> failIds = new ArrayList<>();
        boolean isSuccess = true;
        for (ApprovalRequest request : requests) {
            isSuccess = goFlowService.goFlowV2(request.getFlowPathId(), request.getAction(), request.getUuid(), request.getShowName(), request.getReason(), request.getAppChannelId(), request.getDataChannelId(), "", false);
            if (!isSuccess) {
                failIds.add(request.getFlowPathId());
                LogMs.logAdd(LogRequest.builder().content(Collections.singletonList("审批失败 - 表单详情 ：" + request)).build());
                isSuccess = true;
            }
        }
        return isSuccess;
    }

    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/batchList")
    @LogRecord(module = "审批模块", name = "审批列表", type = ApiTypeConstant.SELECT)
    public Result batchList(@RequestBody List<MsFlowPathListRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return LResult.success(Collections.emptyList(), 0);
        }

        // 1. 构建查询条件列表
        List<GetFlowPathCondition> conditions = new ArrayList<>();
        for (MsFlowPathListRequest req : requests) {
            conditions.add(buildConditionFromSingle(req));
        }
        // 2. 批量查询 FlowPath
        List<FlowPathDAO> allPaths = flowPathService.getFlowPathListBatch(conditions);
        if (allPaths == null || allPaths.isEmpty()) {
            List<List<FlowPathDTO>> emptyResults = new ArrayList<>();
            for (int i = 0; i < requests.size(); i++) {
                emptyResults.add(Collections.emptyList());
            }
            return LResult.success(emptyResults, 0);
        }
        // 3. 批量查询步骤
        List<Integer> allIds = allPaths.stream()
                .map(FlowPathDAO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<FlowPathStepDTO> allSteps = flowPathStepService.getFlowPathStepList(
                GetFlowPathStepCondition.builder().flowPathIdList(allIds).build()
        );
        Map<Integer, List<FlowPathStepDTO>> stepMap = allSteps.stream()
                .collect(Collectors.groupingBy(FlowPathStepDTO::getFlowPathId));


        // 4. 拆分结果
        List<List<FlowPathDTO>> perRequestList = new ArrayList<>();
        for (GetFlowPathCondition cond : conditions) {
            List<FlowPathDTO> dtos = allPaths.stream()
                    .filter(dao -> matchesCondition(dao, cond))
                    .map(FlowPathDTO::create)
                    .peek(dto -> dto.setStepInfoList(
                            stepMap.getOrDefault(dto.getId(), Collections.emptyList())))
                    .collect(Collectors.toList());
            perRequestList.add(dtos);
        }
        int totalCount = perRequestList.stream().mapToInt(List::size).sum();
        return LResult.success(perRequestList, totalCount);
    }

    /**
     * 将单个请求转换为完整的查询条件
     */
    private GetFlowPathCondition buildConditionFromSingle(MsFlowPathListRequest req) {
        GetFlowPathCondition cond = GetFlowPathCondition.builder().build();

        // 互斥 ID 过滤
        if (Objects.nonNull(req.getFlowPathId()) && req.getFlowPathId() > 0) {
            cond.setId(req.getFlowPathId());
        } else if (req.getFlowPathIdList() != null && !req.getFlowPathIdList().isEmpty()) {
            cond.setIdList(req.getFlowPathIdList());
        } else if ((req.getFlowIdList() != null && !req.getFlowIdList().isEmpty()) ||
                (req.getTempIdList() != null && !req.getTempIdList().isEmpty())) {
            Set<String> flowIds = new HashSet<>();
            if (req.getFlowIdList() != null && !req.getFlowIdList().isEmpty()) {
                flowIds.addAll(req.getFlowIdList());
            }
            if (req.getTempIdList() != null && !req.getTempIdList().isEmpty()) {
                GetFlowCondition flowCond = new GetFlowCondition();
                flowCond.setTempIdList(req.getTempIdList());
                List<FlowDTO> flows = flowService.getFlowList(flowCond);
                if (flows != null) {
                    for (FlowDTO f : flows) {
                        flowIds.add(f.getId());
                    }
                }
            }
            cond.setFlowIdList(new ArrayList<>(flowIds));
        }

        // 可选字段
        if (req.getCompanyId() != null && org.springframework.util.StringUtils.hasText(req.getCompanyId())) {
            cond.setCompanyId(req.getCompanyId());
        }
        if (req.getTitle() != null && org.springframework.util.StringUtils.hasText(req.getTitle())) {
            cond.setTitle(req.getTitle());
        }
        if (req.getStatus() != null) {
            cond.setStatus(req.getStatus());
        }
        if (req.getInitiatorUserIdList() != null && !req.getInitiatorUserIdList().isEmpty()) {
            cond.setInitiatorUserIdList(req.getInitiatorUserIdList());
        }
        if (req.getCreateStartTime() != null && req.getCreateEndTime() != null) {
            cond.setCreateStartTime(req.getCreateStartTime());
            cond.setCreateEndTime(req.getCreateEndTime());
        }
        if (req.getUpdateStartTime() != null && req.getUpdateEndTime() != null) {
            cond.setUpdateStartTime(req.getUpdateStartTime());
            cond.setUpdateEndTime(req.getUpdateEndTime());
        }
        if (req.getIsInterrupt() != null) {
            cond.setIsInterrupt(req.getIsInterrupt());
        }
        if (req.getWithdrawPathId() != null) {
            cond.setWithdrawPathId(req.getWithdrawPathId());
        }

        // 渠道
        cond.setAppChannelId(req.getAppChannelId());
        cond.setDataChannelId(
                req.getDataChannelId() != null ? req.getDataChannelId() : ServletContext.getDataChannelId()
        );
        //分页
        return cond;
    }

    private boolean matchesCondition(FlowPathDAO dao, GetFlowPathCondition cond) {
        if (cond.getId() != null && !cond.getId().equals(dao.getId())) {
            return false;
        }
        if (cond.getIdList() != null && !cond.getIdList().isEmpty()
                && !cond.getIdList().contains(dao.getId())) {
            return false;
        }
        if (cond.getFlowIdList() != null && !cond.getFlowIdList().isEmpty()
                && !cond.getFlowIdList().contains(dao.getFlowId())) {
            return false;
        }
        return true;
    }

    /**
     * 审批列表企业
     *
     * @param requestMap
     * @return
     */
    @PostMapping("/flowPathCompany")
    @LogRecord(module = "审批模块 ", name = "审批列表企业", type = ApiTypeConstant.SELECT)
    public Result flowPathCompany(@RequestBody(required = false) Map<String, Object> requestMap) {
        if (Objects.isNull(requestMap)) {
            requestMap = new HashMap<>();
        }
        //解决合并丢代码
        List<String> companyIdList = flowPathService.flowPathCompany(requestMap);
        return DResult.success(companyIdList);
    }


    /**
     * 代办数据处理
     */
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping("/updateAgent")
    @LogRecord(module = "审批模块", name = "更新代办数据", type = ApiTypeConstant.UPDATE)
    public Result approve(@RequestBody AgentRequest request) {
        flowPathService.updateFlowPathByDelegation(request);
        return DResult.success();
    }

}
