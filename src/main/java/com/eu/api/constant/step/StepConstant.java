package com.eu.api.constant.step;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 节点类型枚举管理
 * &#064;DATE: 2025/1/23 17:58
 * &#064;AUTHOR: XSL
 *
 */
public class StepConstant {

    /**
     * 节点类型
     */
    @Getter
    @AllArgsConstructor
    public enum Type {

        STATER(0, "申请人"),
        AUDITOR(1, "审核人"),
        REIPIENTS(2, "抄送人"),
        CONDITION(3, "条件");

        private final int code;
        private final String msg;

    }
}
