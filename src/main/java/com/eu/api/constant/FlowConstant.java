package com.eu.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

public class FlowConstant {

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowWithdraw {
        PROHIBIT(0, "禁止"),
        PERMIT(1, "允许"),
        CONFIRMATION(2, "撤销需经审核人同意"),
        CONFIRMATION_CURRENT_AUDIT(3, "撤销续经当前审核人同意"),
        CONFIRMATION_AND_CURRENT_AUDIT (4, "同时包含2和3"),
        ;

        private int code;
        private String name;
    }

}
