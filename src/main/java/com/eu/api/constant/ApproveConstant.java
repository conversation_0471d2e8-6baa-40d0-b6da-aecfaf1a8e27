package com.eu.api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

public class ApproveConstant {

    /**
     * 流程状态 [0:进行中,1:已驳回,2:已通过,3:已撤销,4:中断终止,5:退回终止,6:转审,7:加签]
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathStatus {
        WAITING(0, "进行中"),
        REFUSED(1, "已驳回"),
        PASSED(2, "已通过"),
        WITHDRAWN(3, "已撤销"),
        INTERRUPTED(4, "中断终止"),
        BACK(5, "退回终止"),
        TRANSFERRED(6, "转审"),
        RETURN_BACL(7, "退回");

        private int code;
        private String name;
    }

    /**
     * 审批流流水过程状态 [1:我的;2:待我;3:我已;4:抄送;]
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathListType {

        NONE(0, ""),
        MY(1, "我的"), // 已提交
        WAIT(2, "待我"), // 待处理
        PROCESSED(3, "我已"), // 已处理
        CC(4, "抄送"), // 抄送我
        TRANSFER(5, "转审"),
        ;

        private int code;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathAction {
        //动作[0:创建;1:驳回;2:已通过;3:撤销;]
        CREATE(0, "创建"),
        REFUSE(1, "驳回"),
        PASS(2, "已通过"),
        WITHDRAW(3, "撤销"),
        BACK(4, "退回"),
        TRANSFER(5, "转审"),
        APPIONT(6, "加签"),
        ;
        private int code;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPassAll {
        //是否全员通过[0:否;1:是;2:依次;]
        ONE(0, "单人通过"),
        ALL(1, "全员通过"),
        ONE_BY_ONE(2, "依次通过"),
        ;
        private int code;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathStepNotificationType {
        APPROVE(1, "审核数据"),
        CC(2, "抄送数据"),
        ;
        private int code;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathHistoryType {
        //类型：1:操作日志;2:备注
        APPROVE(1, "操作日志"),
        REMARK(2, "备注"),
        ;
        private int code;
        private String name;
    }

    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowPathIsInterrupt {
        // 是否暂停：0:否;1:是
        NO(0, "否"),
        YES(1, "是"),
        ;
        private int code;
        private String name;
    }

    /**
     * 流程设置中加签位置
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowAppiont {
        // 0:在...之前加签,1:在...之后加签
        NONE(0, "不允许加签"),
        BEFOR(1, "允许在之前加签"),
        AFTER(2, "允许在之后加签"),
        ALL(3, "自由加签");
        private int code;
        private String name;
    }

    /**
     * 是否是加签添加的节点
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum appiont {
        NO(0, "不是"),
        YES(1, "是");
        private int code;
        private String name;
    }


    /**
     * 是否是退回添加的节点
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum returnBack {
        NO(0, "不是"),
        YES(1, "是");
        private int code;
        private String name;
    }


    /**
     * 流程设置中加签位置
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum flowReturnBack {
        // 0:在...之前加签,1:在...之后加签
        NONE(0, "不允许回退"),
        BEFOR_ONE(1, "允许回退至上一节点"),
        BEFOR_ALL(2, "允许回退到初始节点"),
        ALL(3, "允许两种退回");
        private int code;
        private String name;
    }


    public static String[] flowColumnList = {
            "id",
            "title",
            "desc",
            "appChannelId",
            "dataChannelId",
            "tempId",
            "refuseUrl",
            "passUrl",
            "approverUrl",
            "ccUrl",
            "createTime",
            "updateTime",
            "ccNotification",
            "ccCreate",
            "customize",
            "withdraw",
            "rejectMustAlert",
            "freedom",
            "abnormal",
            "autoappr",
            "status"
    };

    public static String[] flowPathListColumnList = {
            "id",
            "title",
            "flowPathId",
            "type",
            "uuid",
            "appChannelId",
            "dataChannelId",
            "createTime"
    };

    /**
     * 审批过期时间，单位 s
     **/
    public final static long APPROVE_LOCK_EXPIRE_TIME = 60;

    /**
     * 审批锁key前缀
     */
    public final static String APPROVE_LOCK_PREFIX = "perform_approval_lock_1_%s";

    /**
     * 代办操作类型
     */
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum agentAction {
        DELETE(0, "删除"),
        UPDATE(1, "修改");

        private int code;
        private String name;
    }

    /**
     * 审批类型枚举
     **/
    @Getter
    @AllArgsConstructor
    @NoArgsConstructor
    public enum approvalTypeEnum {

        SPONSOR(1, "发起"),
        AWAIT_REVIEW(2, "待审核"),
        ALREADY_REVIEW(3, "已审核"),
        CARBON_COPY_RECIPIENTS(4, "抄送"),
        ;

        private int code;

        private String name;
    }
}
