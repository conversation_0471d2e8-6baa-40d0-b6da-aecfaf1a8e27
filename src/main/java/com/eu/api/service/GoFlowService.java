package com.eu.api.service;

/**
 * <AUTHOR>
 * @date 2025/5/19 15:16
 **/
public interface GoFlowService {

    /**
     * 执行审批
     *
     * @param id            审批流程ID
     * @param action        审批操作类型
     * @param uuid          操作人身份标识
     * @param showName      操作人名称
     * @param reason        操作备注
     * @param appChannelId  操作客户端渠道ID
     * @param dataChannelId 操作数据源ID
     * @param title         操作标题
     * @param isSkip        是否跳过加锁， true 跳过   false 不跳过
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2025/5/19 15:20
     **/
    Boolean goFlowV2(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title, Boolean isSkip);
}
