package com.eu.api.service;


import com.eu.api.domain.condition.GetFlowPathHistoryListCondition;
import com.eu.api.domain.dao.FlowPathHistoryDAO;
import com.eu.api.domain.dto.FlowPathHistoryDTO;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface FlowPathHistoryService {

    public List<FlowPathHistoryDAO> getFlowPathHistoryList(GetFlowPathHistoryListCondition condition);

    public List<FlowPathHistoryDAO> getFlowPathHistoryListTwo(GetFlowPathHistoryListCondition condition);

    public void remark(int flowPathId, String uuid, String showName, String remark, String appChannelId, String dataChannelId);

    public FlowPathHistoryDAO addFlowPathHistory(int flowPathId, String title, int type, int action, String uuid, String reason);

    public FlowPathHistoryDAO addFlowPathHistory(int flowPathId, String title, int type, int action, String uuid, String reason, Date createTime);

    public int updateFlowPathHistory(FlowPathHistoryDAO flowPathHistory);

    List<FlowPathHistoryDTO> getFlowPathHistoryBatch(List<Integer> flowPathIds);

}
