package com.eu.api.service;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.condition.GetFlowPathStepCondition;
import com.eu.api.domain.dao.FlowPathStepDAO;
import com.eu.api.domain.dto.FlowPathStepAddDTO;
import com.eu.api.domain.dto.FlowPathStepDTO;
import com.eu.api.domain.dto.StepInfoNotificationDTO;
import com.eu.api.domain.entity.FlowPathStepEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface FlowPathStepService extends IService<FlowPathStepEntity> {

    /**
     * 新增节点 及办理人
     * @param flowPathStepAddDTO
     * @return
     */
    public FlowPathStepDAO addFlowPathStep(FlowPathStepAddDTO flowPathStepAddDTO);


    /**
     * 发起审批节点新增
     * @param flowPathId 审批ID
     * @param stepNo 节点顺序
     * @param title 节点名称
     * @param passCons
     * @param notificationList  审批人数据集合
     * @param ccNotificationList 抄送人数据集合
     * @param passAll 审批通过方式
     * @param withdraw
     * @param goback
     * @param status
     * @param isCountersign
     * @param isReturnBack
     * @param extraContent
     * @param fromNotificationId
     * @param preId
     * @return
     */
    public FlowPathStepDAO addFlowPathStep(int flowPathId, int stepNo, String title, String passCons,
                                           List<StepInfoNotificationDTO> notificationList, List<StepInfoNotificationDTO> ccNotificationList,
                                           int passAll, int withdraw, int goback, int status, int isCountersign, int isReturnBack, Map<String, Object> extraContent, int fromNotificationId, int preId, int stepType);

    public List<FlowPathStepDTO> getFlowPathStepList(GetFlowPathStepCondition condition);

    public void update(FlowPathStepDAO flowPathStepDAO, GetFlowPathStepCondition condition);

    public void updateNotUpdateTime(FlowPathStepDAO flowPathStepDAO, GetFlowPathStepCondition condition);

    public int deleteById(Integer id);

    public Set<Integer> getFlowPathStepFlowPathIdList();

//    /**
//     * 节点强制变动
//     * @param flowPathStepDAO
//     */
//    public void flowPathStep(FlowPathStepDTO flowPathStepDTO);

    public int deleteByQueryWrapper(LambdaQueryWrapper<FlowPathStepEntity> queryWrapper);

    List<FlowPathStepDTO> getFlowPathStepBatch(List<Integer> flowPathIds);

    void batchUpdateSteps(List<FlowPathStepDAO> toUpdateSteps);

    void updateBatch(List<FlowPathStepDAO> daosToUpdate, List<GetFlowPathStepCondition> conditions);

//    Map<Integer, List<FlowPathStepDTO>> getFlowPathStepListByFlowPathIds(List<Integer> flowPathIds);
}
