package com.eu.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.condition.GetFlowPathListDAOListCondition;
import com.eu.api.domain.dao.FlowPathListDAO;
import com.eu.api.domain.dto.FlowPathCountDTO;
import com.eu.api.domain.dto.FlowPathListDTO;
import com.eu.api.domain.dto.FlowPathListDeleteDTO;
import com.eu.api.domain.dto.flow.FlowPathListQueryDTO;
import com.eu.api.domain.entity.FlowPathListEntity;
import com.eu.api.domain.vo.flow.FLowPathListVO;
import com.eu.common.result.DataList;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 */
public interface FlowPathListService extends IService<FlowPathListEntity> {

    public List<FlowPathListDAO> getFlowPathListDAOList(GetFlowPathListDAOListCondition condition);

    public List<FlowPathListDAO> getFlowPathListList(GetFlowPathListDAOListCondition condition, int start, int limit);

    public List<FlowPathListDAO> getFlowPathListStatistics(GetFlowPathListDAOListCondition condition);

    public Integer getFlowPathListCount(GetFlowPathListDAOListCondition condition);

    public FlowPathCountDTO getFlowPathListCountAll(GetFlowPathListDAOListCondition condition);

    public FlowPathListDAO addFlowPathList(String title, int flowPathId, int type, String uuid, String extraContent,
                                           String appChannelId, String dataChannelId);

    public FlowPathListDAO addFlowPathList(String title, int flowPathId, int type, String uuid, String extraContent,
                                           String appChannelId, String dataChannelId, Date createTime);

    public int updateFlowPathList(FlowPathListDAO setData, GetFlowPathListDAOListCondition condition);


    public int deleteFlowPathList(FlowPathListDeleteDTO dto);

    /**
     * 查询审批列表
     *
     * @return
     */
    public DataList<FLowPathListVO> flowPathList(FlowPathListQueryDTO flowPathListQueryDTO);

    List<FlowPathListDTO> getFlowPathListBatch(@Param("flowPathIds") List<Integer> flowPathIds,
                                               @Param("type") int type);

    List<String> flowPathCompany(GetFlowPathListDAOListCondition condition);
}
