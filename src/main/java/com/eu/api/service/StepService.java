package com.eu.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.condition.GetStepCondition;
import com.eu.api.domain.dao.StepDAO;
import com.eu.api.domain.dto.StepInfoDTO;
import com.eu.api.domain.entity.StepEntity;
import com.eu.api.domain.order.GetStepOrder;

import java.util.List;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface StepService extends IService<StepEntity> {

//    public List<StepDAO> getStepList(MsStepListRequest request);

    public List<StepDAO> getStepList(GetStepCondition condition);

    public List<StepDAO> getStepList(GetStepCondition condition, GetStepOrder order);

    /**
     * 创建流程下节点
     * @param stepInfoDTO 节点集合
     * @param flowId 流程ID
     * @return
     */
    public List<StepInfoDTO> createStep(StepInfoDTO stepInfoDTO, String flowId);

    public List<StepInfoDTO> createStepDfs(StepInfoDTO stepInfoDTO, int index, int parentIndex, List<StepInfoDTO> stepInfoDTOList);

    public void saveStep(List<StepInfoDTO> stepInfoDTOList, String flowId);

    /**
     * 创建流程下节点集合
     * @param stepInfoDTOList 节点集合
     * @param flowId 流程ID
     */
    public void saveStepList(List<StepInfoDTO> stepInfoDTOList, String flowId);

    public void batchSaveStep(List<StepDAO> stepDAOList);

    public void updateStep(StepDAO stepDAO, String flowId);

}
