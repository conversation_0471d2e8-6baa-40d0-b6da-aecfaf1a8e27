package com.eu.api.service;

import com.eu.api.domain.dao.FlowPathDAO;
import com.eu.api.domain.dto.FlowPathDTO;
import com.eu.api.domain.dto.FlowPathStepDTO;
import com.eu.api.domain.dto.flow.ApprovalRequest;
import com.eu.api.domain.ms.*;

import java.util.List;
import java.util.Map;

/**
 * 审批操作业务
 * &#064;DATE: 2025/1/22 10:44
 * &#064;AUTHOR: XSL
 */
public interface ApprovealService {

    /**
     * 发起审批 新
     */
    public Integer initiateApproval(MsFlowPathCreateRequest request);

    /**
     * 发起审批
     *
     * @param request
     * @return
     */
    public FlowPathDTO create(MsFlowPathCreateRequest request);

    public FlowPathDTO withdraw(MsFlowPathWithdrawRequest request);

    public void flowPathAddByCondition(MsFlowPathConditionRequest request);

    /**
     * 分支变动
     *
     * @param request
     */
    void embranchmentUpdate(EmbranchmentUpdateRequest request);

    /**
     * 审批 数据推进方法
     *
     * @param id            审批流程ID
     * @param action        审批操作类型
     * @param uuid          操作人身份标识
     * @param showName      操作人名称
     * @param reason        操作备注
     * @param appChannelId  操作客户端渠道ID
     * @param dataChannelId 操作数据源ID
     * @param title         操作标题
     * @return
     */
    public boolean goFlow(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title);

    /**
     * 节点强制变动
     *
     * @param flowPathStepDTO
     */
    public void flowPathStep(FlowPathStepDTO flowPathStepDTO);

    /**
     * 批量分支变动
     * @param requests
     */
    void batchEmbranchmentUpdate(List<EmbranchmentUpdateRequest> requests);

    /**
     * 修改我的审批列表和发送消息通知
     *
     * @param request
     * @param changeUuidMap
     * @return void
     * <AUTHOR>
     * @date 2025/5/28 20:41
     **/
    void addFlowPathListAndNotify(UpdateStepNotificationRequest request, Map<String, List<String>> changeUuidMap);

}
