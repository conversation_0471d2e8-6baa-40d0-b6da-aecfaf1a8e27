package com.eu.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.dto.flow.Step;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.List;
import java.util.Map;

/**
 * @description: 编辑流程接口
 * @author: Xin<PERSON>en
 * @create: 2025-02-02 21:38
 **/
public interface BPService extends IService<Step> {
    /**
     * 添加流程
     *
     * @param customize
     * @param flowId
     */
    JsonNode addStepList(List<Map<String, Object>> customize, String flowId);

    /**
     * 更新流程
     *
     * @param customize
     * @param flowId
     */
    void updateSteps(List<Map<String, Object>> customize, String flowId);

    JsonNode updateStepList(List<Map<String, Object>> customize, String flowId);
}