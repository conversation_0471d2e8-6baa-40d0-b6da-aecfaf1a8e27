package com.eu.api.service;


import com.eu.api.domain.condition.GetFlowPathStepNotificationCondition;
import com.eu.api.domain.dao.FlowPathStepNotificationDAO;
import com.eu.api.domain.dto.FlowPathStepNotificationDTO;
import com.eu.api.domain.dto.StepInfoNotificationDTO;
import com.eu.api.domain.ms.UpdateStepNotificationRequest;

import java.util.List;
import java.util.Map;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface FlowPathStepNotificationService {

    public FlowPathStepNotificationDAO addFlowPathStepNotification(int flowPathId, int stepNo, int sort, int type, int status, String uuid, StepInfoNotificationDTO notificationDTO, int fromNotificationId);

    public List<FlowPathStepNotificationDTO> getFlowPathStepNotificationList(GetFlowPathStepNotificationCondition condition);

    public void update(FlowPathStepNotificationDAO flowPathStepNotificationDAO, GetFlowPathStepNotificationCondition condition);

    public int deleteById(Integer id);

    /**
     * 根据流程ID和步骤删除对应的办理人
     * @param flowPathId
     * @param stepNo
     * @return
     */
    public int deleteByflowPathIdAndStepNo(Integer flowPathId, Integer stepNo, List<Integer> typeList);

    /**
     * 强制覆盖流程办理人
     * @param updateStepNotificationRequest
     * @return
     */
    public Map<String, List<String>> coverNotification(UpdateStepNotificationRequest updateStepNotificationRequest);


    void batchUpdateNotifications(List<FlowPathStepNotificationDAO> toUpdateNotifications);
}
