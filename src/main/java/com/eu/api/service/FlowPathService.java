package com.eu.api.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.condition.GetFlowPathCondition;
import com.eu.api.domain.dao.FlowPathDAO;
import com.eu.api.domain.dto.FlowPathDTO;
import com.eu.api.domain.entity.FlowPathEntity;
import com.eu.api.domain.ms.*;
import com.eu.api.domain.vo.flow.FlowPathStatisticsVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 审批接口
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface FlowPathService extends IService<FlowPathEntity> {

    /**
     *  发起审批
     * @param request
     * @return
     */
//    public FlowPathDTO create(MsFlowPathCreateRequest request);

    /**
     * 获取审批详情
     * @param flowPathId
     * @return
     */
    public FlowPathDTO getFlowPathDetail(int flowPathId);

    public List<FlowPathDTO> getFlowPathDetails(List<Integer> flowPathIds);

    public List<FlowPathDAO> getFlowPathList(GetFlowPathCondition condition);

    public List<FlowPathDAO> getFlowPathListTwo(GetFlowPathCondition condition);

    public int getFlowPathCount(GetFlowPathCondition condition);

    public List<FlowPathDAO> getFlowPathList(GetFlowPathCondition condition, Integer start, Integer limit);

    public FlowPathDAO getFlowPath(GetFlowPathCondition condition);

    public FlowPathDTO remark(MsFlowPathRemarkRequest request);

//    public FlowPathDTO withdraw(MsFlowPathWithdrawRequest request);

    public FlowPathDTO transfer(MsFlowPathTransferRequest request);

    public FlowPathDTO appoint(MsFlowPathAppointRequest request);

    public FlowPathDTO returnBack(MsReturnBackRequest request);

    public FlowPathDTO updateFlowPathAndCommit(UpdateFlowPathRequest request);

    public void updateFlowPath(FlowPathDAO condition);

    /**
     * 审批列表统计
     * @param request
     */
    public FlowPathStatisticsVO flowPathStatistics(Map<String, Object> request);

    public List<FlowPathDAO> batchGetFlowPaths(List<Integer> ids);

    void batchUpdateFlowPaths(List<FlowPathDAO> updateList);

    List<FlowPathDAO> getFlowPathListBatch(List<GetFlowPathCondition> conditions);

    /**
     * 获取审批列表企业
     * @param requestMap
     * @return
     */
    List<String> flowPathCompany(Map<String, Object> requestMap);

    void updateFlowPathByDelegation(AgentRequest request);
}
