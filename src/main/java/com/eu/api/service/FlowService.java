package com.eu.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.condition.GetFlowCondition;
import com.eu.api.domain.condition.GetStepCondition;
import com.eu.api.domain.dao.FlowDAO;
import com.eu.api.domain.dao.StepDAO;
import com.eu.api.domain.dto.FlowByListDTO;
import com.eu.api.domain.dto.FlowDTO;
import com.eu.api.domain.dto.StepInfoDTO;
import com.eu.api.domain.entity.FlowEntity;
import com.eu.api.domain.ms.MsFlowAddRequest;
import com.eu.api.domain.ms.MsFlowUpdateRequest;
import com.eu.api.domain.ms.MsStepListRequest;

import java.util.List;
import java.util.Map;

/**
 * 审批接口
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 */
public interface FlowService extends IService<FlowEntity> {

    /**
     * 创建流程
     *
     * @param request
     * @return
     */
    public FlowDAO addFlow(MsFlowAddRequest request);

    public FlowDAO addFlowByStep(MsFlowAddRequest request);

//    public FlowDAO addFlowByStep(FlowDAO flowDAO);

    public FlowDAO updateFlowByStep(MsFlowUpdateRequest request);

    /**
     * 修改流程
     *
     * @param request
     * @return
     */
    public FlowDAO updateFlow(MsFlowUpdateRequest request);

    /**
     * 修改审批流
     * @param flowDAO
     * @return
     */
    public FlowDAO addFlowByStep(FlowDAO flowDAO);

    /**
     * 获取审批流详情
     *
     * @param condition
     * @return
     */
    public FlowDTO getFlow(GetFlowCondition condition);


    /**
     * 获取审批流详情v2
     * @param condition
     * @return
     */
    public FlowDTO getFlowStep(GetFlowCondition condition);
    /**
     * 获取对应流程所有的节点信息
     *
     * @param flowId
     * @return
     */
    public StepInfoDTO getStepTree(String flowId);

    public StepInfoDTO getStepTree(List<StepDAO> stepDAOList);

    public StepInfoDTO createTreeDfs(Map<Integer, StepInfoDTO> stepInfoDTOMap, Map<Integer, String> passConsMap, Integer stepId);

    public List<StepInfoDTO> createStepInfo(String flowId, Map<String, Object> tempDataMap);

    public List<StepInfoDTO> createStepList(String flowId, Map<String, Object> tempDataMap, List<StepInfoDTO> stepInfoList, Map<String, Boolean> verifyResult);

    public List<StepInfoDTO> recursionStepInfo(List<StepInfoDTO> stepInfoDTOList, Map<String, Object> tempDataMap, String srcId, Map<String, Boolean> verifyResult);

//    /**
//     * 审批 数据推进方法
//     * @param id            审批流程ID
//     * @param action        审批操作类型
//     * @param uuid          操作人身份标识
//     * @param showName      操作人名称
//     * @param reason        操作备注
//     * @param appChannelId  操作客户端渠道ID
//     * @param dataChannelId 操作数据源ID
//     * @param title         操作标题
//     * @return
//     */
//    public boolean goFlow(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title);

    public Map<String, Object> createNotificationData(String flowId, int flowPathId, String uuid, String action, String event, String tempShowData, String title, String extraContent, String tempData, String stepInfoList, String flowPathExtraContent, Integer flowPathStatus);

    public Integer getFlowCount(GetFlowCondition condition);

    public List<FlowDTO> getFlowList(GetFlowCondition condition);

    public List<FlowDTO> getFlowList(GetFlowCondition condition, int start, int limit);
    public List<FlowByListDTO> getFlowListByList(GetFlowCondition condition, int start, int limit);

    public List<StepDAO> getStepList(MsStepListRequest request);

    public List<StepDAO> getStepList(GetStepCondition condition);

    public Map<String, List<StepDAO>> getStepListByFlowIds(List<String> flowIds);
}
