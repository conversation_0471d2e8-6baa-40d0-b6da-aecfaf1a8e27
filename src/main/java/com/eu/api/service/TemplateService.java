package com.eu.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.eu.api.domain.dto.TemplateDTO;
import com.eu.api.domain.entity.TemplateEntity;
import com.eu.api.domain.ms.MsTemplateAddRequest;
import com.eu.api.domain.ms.MsTemplateInfoRequest;
import com.eu.api.domain.ms.MsTemplateListRequest;
import com.eu.api.domain.ms.MsTemplateUpdateRequest;

import java.util.List;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface TemplateService extends IService<TemplateEntity> {

    public List<TemplateDTO> getTemplateList(MsTemplateListRequest request);

    public TemplateDTO addTemplate(MsTemplateAddRequest request);

    public TemplateDTO updateTemplate(MsTemplateUpdateRequest request);

    public TemplateDTO getTemplate(MsTemplateInfoRequest request);

}
