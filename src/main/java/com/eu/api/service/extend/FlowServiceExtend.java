package com.eu.api.service.extend;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eu.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * &#064;DATE: 2024/11/11
 * &#064;AUTHOR: XSL
 *
 */
@Component
public class FlowServiceExtend {

    private final Lock lock = new ReentrantLock();

    private JSONObject jsonObject = null;

    public JSONObject getJsonObject(JSONObject jsonObject) {
        lock.lock();
        try {
            this.jsonObject = new JSONObject();
            Set<String> keySet = jsonObject.keySet();
            for (String key : keySet) {
                String str = jsonObject.getStr(key);
                if (JSONUtil.isTypeJSONArray(str)) {
                    if (str.contains("field") && str.contains("value")) {
                        recursionLabel(str);
                    } else if (str.contains("value")) {
                        JSONArray jsonArray = JSONUtil.parseArray(str);
                        int size = jsonArray.size();
                        List<String> valueList = new ArrayList<>();
                        for (int i = 0; i < size; i++) {
                            String value = jsonArray.getStr(i);
                            if (StringUtil.isNotEmpty(value)) {
                                if (value.contains("value")) {
                                    if (JSONUtil.isTypeJSONObject(value)) {
                                        JSONObject jsonObject1 = JSONUtil.parseObj(value);
                                        valueList.add(jsonObject1.getStr("value"));
                                    } else {
                                        valueList.add(value);
                                    }
                                }
                            }
                        }
                        this.jsonObject.set(key, valueList);
                    } else {
                        this.jsonObject.set(key, str);
                    }
                } else if (JSONUtil.isTypeJSONObject(str)) {
                    JSONObject entries = JSONUtil.parseObj(str);
                    String str1 = entries.getStr("value");
                    if (StrUtil.isEmpty(str1)) {
                        str1 = entries.getStr("id");
                    }
                    this.jsonObject.set(key, str1);
                } else {
                    this.jsonObject.set(key, str);
                }
            }
            JSONObject returnJson = new JSONObject();
            returnJson.putAll(this.jsonObject);
            this.jsonObject = null;
            return returnJson;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 递归集合
     * @param jsonObjectSTR
     * @return
     */
    private void recursionLabel(String jsonObjectSTR) {
        if (StrUtil.isEmpty(jsonObjectSTR)) {
            return;
        }
        if (JSONUtil.isTypeJSONArray(jsonObjectSTR)) {
            JSONArray jsonArray = JSONUtil.parseArray(jsonObjectSTR);
            int size = jsonArray.size();    //数组个数
            for (int i = 0; i < size; i++) {
                recursionLabel(jsonArray.getStr(i));
            }
        } else if (JSONUtil.isTypeJSONObject(jsonObjectSTR)) {  //判断是否是对象
            JSONObject jsonObject = JSONUtil.parseObj(jsonObjectSTR);
            String field = jsonObject.getStr("field");
            String value = jsonObject.getStr("value");
            if (StringUtil.isNotEmpty(field) && StrUtil.isNotEmpty(value)) {
                if (JSONUtil.isTypeJSONArray(value)) {
                    recursionLabel(value);
                } else if (JSONUtil.isTypeJSONObject(value)) {
                    JSONObject jsonObject1 = JSONUtil.parseObj(value);
                    if (jsonObject1.containsKey("field") && jsonObject1.containsKey("value")) {
                        this.jsonObject.set(jsonObject1.getStr(field), jsonObject1.get("value")); //将值放到最终的JSON中
                    } else {
                        this.jsonObject.set(field, value);
                    }
                } else {
                    this.jsonObject.set(field, value); //将值放到最终的JSON中
                }
            }
        }
    }

}
