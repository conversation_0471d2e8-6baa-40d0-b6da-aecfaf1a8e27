package com.eu.api.service.extend;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eu.api.domain.dao.StepDAO;
import com.eu.api.domain.dto.ConditionDTO;
import com.eu.common.util.StringUtil;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * &#064;DATE: 2024/11/29
 * &#064;AUTHOR: XSL
 *
 */
@Component
public class StepServiceExtend {

    private final Lock lock = new ReentrantLock();
    List<StepDAO> stepList = null;

    /**
     * 判断出分支走哪条件 TODO
     * @param stepList
     * @param tempData
     * @return
     */
    public StepDAO conditionStepByJudge(List<StepDAO> stepList, Map<String, Object> tempData) {
        List<StepDAO> conditionStep = new ArrayList<>();
        List<StepDAO> conditionStepElse = new ArrayList<>();
        for (StepDAO stepDAO : stepList) {
            String conditions = stepDAO.getConditions();
            if (StrUtil.isEmpty(conditions)) {
                continue;
            }
            JSONArray conditionsJSONArray = JSONUtil.parseArray(conditions);
            if (!conditionsJSONArray.isEmpty() && conditionsJSONArray.getStr(0).length() > 2) {
                conditionStep.add(stepDAO);
            } else {
                conditionStepElse.add(stepDAO);
            }
        }

        conditionStep.sort(Comparator.comparingInt(StepDAO::getId));
        for (StepDAO stepDAO : conditionStep) {
            JSONArray jsonArray = JSONUtil.parseArray(stepDAO.getConditions()); //条件组
            int size = jsonArray.size();

            List<Boolean> flagList = new ArrayList<>();

            /*
             * 遍历条件组
             */
            for (int i = 0; i < size; i++) {
                JSONObject jsonObjectConditions = jsonArray.getJSONObject(i);
                Set<String> keySet = jsonObjectConditions.keySet();
                boolean flag = false;
                for (String key : keySet) {
                    ConditionDTO conditionDTO = jsonObjectConditions.getBean(key, ConditionDTO.class);
                    flag = judgeOne(key + conditionDTO.getType() + conditionDTO.getValue(), tempData);
                    if (!flag) {    //不满足
                        break;
                    }
                }
                flagList.add(flag);
            }
            if (flagList.stream().anyMatch(Boolean::booleanValue)) {
                return stepDAO;
            }
        }
        if (!conditionStepElse.isEmpty()) {
            return conditionStepElse.get(0);
        }
        return stepList.get(0);
    }

    /*
     * 递归获取所有之后的节点
     */
    public List<StepDAO> getStepList(List<StepDAO> stepList, List<String> stepIdList, boolean conditionFlag) {
        lock.lock();
        try {
            this.stepList = new ArrayList<>();
            for (String stepId : stepIdList) {
                recursionStep(stepList, stepId, conditionFlag);
            }
            List<StepDAO> returnStepDAOList = this.stepList.stream().filter(item -> Objects.isNull(item.getConditions())).distinct().collect(Collectors.toList());
            this.stepList = null;
            return returnStepDAOList;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 递归获取之后的节点
     * @param stepList
     * @param stepId
     * @param conditionFlag
     */
    private void recursionStep(List<StepDAO> stepList, String stepId, boolean conditionFlag) {
        if (Objects.isNull(stepList) || stepList.isEmpty()) {
            return;
        }
        for (StepDAO stepDAO : stepList) {
            String srcId = stepDAO.getSrcId();
            if (StrUtil.isEmpty(srcId)) {
                continue;
            }
            if (srcId.contains(stepId)) {
                this.stepList.add(stepDAO);
                String conditions = stepDAO.getConditions();
                if (StrUtil.isNotEmpty(conditions) && !conditionFlag) {   //条件节点
                    break;
                }
                recursionStep(stepList, stepDAO.getStepId(), conditionFlag);
            }
        }
    }

    /**
     * 判断条件分支
     * @param condition
     * @param tempDataMap
     * @return
     */
    public boolean judgeOne(String condition, Map<String, Object> tempDataMap) {
        JSONObject tempDataJSON = JSONUtil.parseObj(tempDataMap);
        String key = "";
        String value = "";
        if (condition.contains(">=")) {
            String[] conditionArr = condition.split(">=");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains("<=")) {
            String[] conditionArr = condition.split("<=");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains("===")) {
            String[] conditionArr = condition.split("===");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains(">")) {
            String[] conditionArr = condition.split(">");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains("<")) {
            String[] conditionArr = condition.split("<");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains("=")) {
            String[] conditionArr = condition.split("=");
            key = conditionArr[0];
            value = conditionArr[1];
        } else if (condition.contains("≠")) {
            String[] conditionArr = condition.split("≠");
            key = conditionArr[0];
            value = conditionArr[1];
        }

        if (StrUtil.isEmpty(tempDataJSON.getStr(key))) {
            return false;
        }

        //判定
        if (condition.contains(">=") && StringUtil.isDouble(value)) { //大于等于
            return Double.parseDouble(StringUtil.isEmpty(tempDataMap.get(key).toString()) ? "0.00" : tempDataMap.get(key).toString()) >= Double.parseDouble(value);
        } else if (condition.contains("<=")) {  //小于等于 包含
            if (StringUtil.isDouble(value)) {
                return Double.parseDouble(StringUtil.isEmpty(tempDataMap.get(key).toString()) ? "0.00" : tempDataMap.get(key).toString()) <= Double.parseDouble(value);
            }
            if (JSONUtil.isTypeJSONArray(value)) {
                List<String> valueStringList = JSONUtil.toList(value, String.class);
                String tempDataMapValue = tempDataMap.get(key).toString();
                if (tempDataMapValue.contains("[")) {
                    List<String> tempDataMapValueStringList = JSONUtil.toList(tempDataMapValue, String.class);
                    List<String> containValueStringList = valueStringList.stream().filter(tempDataMapValueStringList::contains).collect(Collectors.toList());
                    return !containValueStringList.isEmpty();
                } else return valueStringList.contains(tempDataMapValue);
            } else {
                String tempData = tempDataMap.get(key).toString();
                if (tempData.contains(",")) {
                    if (JSONUtil.isTypeJSONArray(tempData)) {
                        List<String> valueStringList = JSONUtil.toList(tempData, String.class);
                        for (String item : valueStringList) {
                            boolean contains = value.contains(item);
                            if (contains) {
                                return true;
                            }
                        }
                        return false;
                    }
                }
                return value.contains(tempData);
            }
        } else if (condition.contains("===")) { //
            String tempDataValue = tempDataMap.get(key).toString();
            if (StringUtil.isDouble(value)) {
                return Double.parseDouble(StringUtil.isEmpty(tempDataValue) ? "0.00" : tempDataValue) == Double.parseDouble(value);
            } else {
                if (value.startsWith("[") && tempDataValue.startsWith("[")) {
                    List<String> valueStringList = JSONUtil.toList(value, String.class);
                    List<String> tempDataMapValueStringList = JSONUtil.toList(tempDataValue, String.class);
                    return valueStringList.equals(tempDataMapValueStringList);
                }
            }
        } else if (condition.contains(">") && StringUtil.isDouble(value)) {   //大于
            return Double.parseDouble(StringUtil.isEmpty(tempDataMap.get(key).toString()) ? "0.00" : tempDataMap.get(key).toString()) > Double.parseDouble(value);
        } else if (condition.contains("<") && StringUtil.isDouble(value)) {   //小于
            return Double.parseDouble(StringUtil.isEmpty(tempDataMap.get(key).toString()) ? "0.00" : tempDataMap.get(key).toString()) < Double.parseDouble(value);
        } else if (condition.contains("=")) {   //等于
            String tempDataValueStr = "";
            List<String> tempDataValueList = new ArrayList<>();
            if (tempDataMap.get(key) instanceof List) {
                tempDataValueList = JSONUtil.toList(JSONUtil.toJsonStr(tempDataMap.get(key)), String.class);
                tempDataValueList.sort(Comparator.naturalOrder());
            } else {
                String tempDataObjStr = tempDataMap.get(key).toString();
                if (tempDataObjStr.startsWith("\"")) {
                    tempDataValueStr = tempDataObjStr.substring(1, tempDataObjStr.length() - 2);
                } else {
                    tempDataValueStr = tempDataObjStr;
                }
            }
            if (value.contains("|")) {
                List<String> valueList = new ArrayList<>(Arrays.asList(value.split("\\|")));
                if (!tempDataValueList.isEmpty()) {
                    for (String v : valueList) {
                        if (tempDataValueList.contains(v)) {
                            return true;
                        }
                    }
                } else {
                    return valueList.contains(tempDataValueStr);
                }
            } else if (value.contains(",")) {
                if (JSONUtil.isTypeJSONArray(value)) {  //判断值是否[]
                    JSONArray jsonArray = JSONUtil.parseArray(value);
                    String[] arraySTR = {};
                    String[] array = jsonArray.toArray(arraySTR);
                    List<String> list = Arrays.asList(array);
                    return list.contains(tempDataValueStr);
                } else {
                    List<String> valueList = new ArrayList<>(Arrays.asList(value.split(",")));
                    valueList.sort(Comparator.naturalOrder());
                    if (!tempDataValueList.isEmpty()) {
                        return tempDataValueList.equals(valueList);
                    }
                }
            } else {
                if (!tempDataValueList.isEmpty()) {
                    if (tempDataValueList.size() > 1) {
                        return false;
                    }
                    tempDataValueStr = tempDataValueList.get(0);
                }
                if (JSONUtil.isTypeJSONArray(value)) {  //判断值是否[]
                    JSONArray jsonArray = JSONUtil.parseArray(value);
                    String[] arraySTR = {};
                    String[] array = jsonArray.toArray(arraySTR);
                    List<String> list = Arrays.asList(array);
                    return list.contains(tempDataValueStr);
                } else {
                    if (StringUtil.isDouble(value) && StringUtil.isDouble(tempDataValueStr)) {  //如果两个都是数字
                        return Double.parseDouble(value) == Double.parseDouble(tempDataValueStr);
                    } else {
                        return tempDataValueStr.equals(value);
                    }
                }
            }
        } else if (condition.contains("≠")) {    //不属于
            String tempDataValueStr = tempDataMap.get(key).toString();
            if (JSONUtil.isTypeJSONArray(tempDataValueStr)) {
                JSONArray jsonArray = JSONUtil.parseArray(tempDataValueStr);
                if (JSONUtil.isTypeJSON(value)) {
                    boolean flag = true;
                    int size = jsonArray.size();
                    for (int i = 0; i < size; i++) {
                        String str = jsonArray.getStr(i);
                        flag = !value.contains(str);
                        if (!flag) {
                            break;
                        }
                    }
                    return flag;
                } else {
                    return !jsonArray.contains(value);
                }
            } else {
                if (JSONUtil.isTypeJSONArray(value)) {
                    JSONArray jsonArray = JSONUtil.parseArray(value);
                    List<String> list = jsonArray.toList(String.class);
                    return !list.contains(tempDataValueStr);
                } else {
                    return !value.contains(tempDataValueStr);
                }
            }
        }
        return false;
    }

}


