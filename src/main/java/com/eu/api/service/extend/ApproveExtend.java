package com.eu.api.service.extend;

import com.eu.api.constant.step.StepConstant;
import com.eu.api.domain.dao.StepDAO;
import com.eu.common.exception.BsException;
import com.eu.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * &#064;DATE: 2025/1/24 14:28
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class ApproveExtend {

    @Autowired
    private StepServiceExtend stepServiceExtend;

    /**
     * 条件判断节点走向,来组成完整路线
     */
    public List<StepDAO> conditionJudgeStep(List<StepDAO> stepList, Map<String, Object> tempData) {
        if (Objects.isNull(stepList) || stepList.isEmpty()) {
            return stepList;
        }
        Optional<StepDAO> first = stepList.stream().filter(item -> "start".equals(item.getStepId()) || (Objects.nonNull(item.getType()) && item.getType() == StepConstant.Type.STATER.getCode())).findFirst();
        StepDAO stepStater;
        if (first.isPresent()) {
            stepStater = first.get();
        } else {
            stepList.sort(Comparator.comparingInt(StepDAO::getId));
            stepStater = stepList.get(0);
        }
        if (stepStater == null) {
            throw new BsException("流程节点不存在发起节点");
        }

        /*
         * 分组,便于查询 Map<srcId,StepTreeDTOList>
         */
        Map<String, List<StepDAO>> stepListBySrcIdMap = stepList.stream().filter(item -> StringUtil.isNotEmpty(item.getSrcId())).collect(Collectors.groupingBy(StepDAO::getSrcId));

        Map<String, List<StepDAO>> newStepListBySrcIdmap = new HashMap<>();
        Set<String> keySet = stepListBySrcIdMap.keySet();
        for (String key : keySet) {
            List<StepDAO> stepDAOS = stepListBySrcIdMap.get(key);
            if (key.contains(",")) {
                String[] split = key.split(",");
                for (String s : split) {
                    newStepListBySrcIdmap.put(s, stepDAOS);
                }
            } else {
                newStepListBySrcIdmap.put(key, stepDAOS);
            }
        }


        List<StepDAO> returnList = new ArrayList<>();
//        returnList.add(stepStater);


        String stepId = stepStater.getStepId();   //当前节点的ID 用来查找下一节点用

        while (true) {
            List<StepDAO> stepItemList = newStepListBySrcIdmap.get(stepId);
            if (Objects.isNull(stepItemList)) { //未找到下一节点 结束
                break;
            }

            /*
             * 下一节点处理
             */
            StepDAO step = stepServiceExtend.conditionStepByJudge(stepItemList, tempData);
            stepId = step.getStepId();
//            if (step.getType() == StepConstant.Type.CONDITION.getCode()) {
//                continue;
//            }
            returnList.add(step);
        }

        return returnList;
    }


}
