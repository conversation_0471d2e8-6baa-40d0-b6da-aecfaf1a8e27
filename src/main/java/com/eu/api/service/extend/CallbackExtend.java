package com.eu.api.service.extend;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.eu.common.holder.ApplicationHolder;
import com.eu.common.ms.log.LogMs;
import com.eu.common.ms.log.pojo.request.LogRequest;
import com.eu.common.ms.mq.MQMs;
import com.eu.common.param.CallbackMQTopic;
import com.eu.common.pojo.LogRecordRequest;
import com.eu.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * &#064;DATE: 2025/1/21 17:30
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class CallbackExtend {

//    @Autowired
//    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private CallbackMQTopic callbackMQTopic;
    @Autowired
    private CallbackMQRelationExtend callbackMQRelationExtend;

    /**
     * 回调通知
     *
     * @param url  回调地址
     * @param data 回调参数
     */
    public void callback(String url, Map<String, Object> data) {
//        CompletableFuture.runAsync(() -> {
//                    String jsonStr = JSONUtil.toJsonStr(data);
//                    String post = HttpClient.post(url, jsonStr);
//                    log.info("callback url: {} reponse: {}", url, post);
//                    LogMs.logAdd(
//                            LogRequest.builder().content(
//                                    Collections.singletonList(
//                                            JSONUtil.toJsonStr(LogRecordRequest.builder()
//                                                    .name("回调通知")
//                                                    .module("回调通知")
//                                                    .type(1)
//                                                    .requestIP("127.0.0.1")
//                                                    .requestAppChannelId(ApplicationHolder.getAppChannelId())
//                                                    .requestDataChannelId(ApplicationHolder.getDataChannelId())
//                                                    .traceId("")
//                                                    .elapsedTime(0L)
//                                                    .method(0)
//                                                    .requestBody(jsonStr)
//                                                    .responseBody(post)
//                                                    .requestURI(url)
//                                                    .success("true")
//                                                    .exception("")
//                                                    .createTime(DateUtil.now())
//                                                    .build())
//                                    )
//                            ).build()
//                    );
//                }
//                , threadPoolExecutor);

        String topicId = callbackMQRelationExtend.getTopIc(url);
//        String topicId = getTopicId(url);
        if (StringUtil.isEmpty(topicId)) {
            return;
        }

        if (Objects.isNull(data.get("uuid")) && Objects.isNull(data.get("userIdList"))) {
            return;
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("url", url);
        dataMap.put("action", data.get("action"));
        dataMap.put("event", data.get("event"));
        dataMap.put("title", data.get("title"));
        dataMap.put("uuid", data.get("uuid"));
        dataMap.put("flowId", data.get("flowId"));
        dataMap.put("flowPathId", data.get("flowPathId"));
        dataMap.put("extraContent", data.get("extraContent"));
        dataMap.put("userIdList", data.get("userIdList"));

        callback(topicId, dataMap, 0);
    }

    /**
     * 推送到MQ
     * @param topicNo
     * @param dataMap
     * @param delay
     */
    public void callback(String topicNo, Map<String, Object> dataMap, int delay) {
        String jsonStr = JSONUtil.toJsonStr(dataMap);
        try {
            MQMs.push(topicNo, jsonStr, delay);
            CompletableFuture.runAsync(() -> LogMs.logAdd(
                    LogRequest.builder().content(
                            Collections.singletonList(
                                    JSONUtil.toJsonStr(LogRecordRequest.builder()
                                            .name("回调通知")
                                            .module("回调通知")
                                            .type(1)
                                            .requestIP("127.0.0.1")
                                            .requestAppChannelId(ApplicationHolder.getAppChannelId())
                                            .requestDataChannelId(ApplicationHolder.getDataChannelId())
                                            .traceId("")
                                            .elapsedTime(0L)
                                            .method(0)
                                            .requestBody(jsonStr)
                                            .requestURI(dataMap.get("url").toString())
                                            .success("true")
                                            .exception("")
                                            .createTime(DateUtil.now())
                                            .build())
                            )
                    ).build()
            ));
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("url:{} callback topicNo: {} dataMap: {}", dataMap.get("url").toString(), topicNo, dataMap);
            try {
                CompletableFuture.runAsync(() -> LogMs.logAdd(
                                LogRequest.builder().content(
                                        Collections.singletonList(
                                                JSONUtil.toJsonStr(LogRecordRequest.builder()
                                                        .name("回调通知")
                                                        .module("回调通知")
                                                        .type(1)
                                                        .requestIP("127.0.0.1")
                                                        .requestAppChannelId(ApplicationHolder.getAppChannelId())
                                                        .requestDataChannelId(ApplicationHolder.getDataChannelId())
                                                        .traceId("")
                                                        .elapsedTime(0L)
                                                        .method(0)
                                                        .requestBody(jsonStr)
                                                        .requestURI(dataMap.get("url").toString())
                                                        .success("false")
                                                        .exception("")
                                                        .createTime(DateUtil.now())
                                                        .build())
                                        )
                                ).build()
                        )
                );
            } catch (Exception exception) {
                log.error(exception.getMessage());
            }
        }
    }

    /**
     * 生成入参对象
     * @param flowId
     * @param flowPathId
     * @param uuid
     * @param action
     * @param event
     * @param tempShowData
     * @param title
     * @param extraContent
     * @param tempData
     * @param stepInfoList
     * @param flowPathExtraContent
     * @param flowPathStatus
     * @return
     */
    public Map<String, Object> createNotificationData(String flowId, int flowPathId, String uuid, String action, String event, String tempShowData, String title, String extraContent, String tempData, String stepInfoList, String flowPathExtraContent, Integer flowPathStatus) {
        Map<String, Object> notificationData = new HashMap<>();
        notificationData.put("action", action);
        notificationData.put("event", event);
        notificationData.put("title", title);
        notificationData.put("uuid", uuid);
        notificationData.put("flowId", flowId);
        notificationData.put("flowPathId", flowPathId);
        notificationData.put("tempData", tempData);
        notificationData.put("tempShowData", tempShowData);
        notificationData.put("extraContent", extraContent);
        notificationData.put("stepInfoList", stepInfoList);
        notificationData.put("flowPathExtraContent", flowPathExtraContent);
        notificationData.put("flowPathStatus", flowPathStatus);
        return notificationData;
    }

    /**
     * 匹配对应的 MQ主题ID
     * @param url
     * @return
     */
    public String getTopicId(String url) {
        String topicId = "";
        if (url == null || url.isEmpty()) {
            return topicId;
        } else if (url.endsWith("apis/invoice/card/approveCallback")) {
            topicId = callbackMQTopic.getInvoiceCardApproveCallback();
        } else if (url.endsWith("apis/approval/callback/pass")) {
            topicId = callbackMQTopic.getApprovalCallbackPass();
        } else if (url.endsWith("api/approvePass/approval/approval")) {
            topicId = callbackMQTopic.getApprovePassApprovalApproval();
        } else if (url.endsWith("apis/approval/callback/reject")) {
            topicId = callbackMQTopic.getApprovalCallbackReject();
        } else if (url.endsWith("api/approveReject/approval/approval")) {
            topicId = callbackMQTopic.getApproveRejectApprovalApproval();
        } else if (url.endsWith("apis/approval/callback/notify")) {
            topicId = callbackMQTopic.getApprovalCallbackNotify();
        } else if (url.endsWith("api/approveNotify/approval/approval")) {
            topicId = callbackMQTopic.getApproveNotifyApprovalApproval();
        } else if (url.endsWith("apis/template/callback")) {
            topicId = callbackMQTopic.getTemplateCallback();
        } else if (url.endsWith("apis/template/customerCallback")) {
            topicId = callbackMQTopic.getTemplateCustomerCallback();
        } else if (url.endsWith("api/MsApproval/pass")) {
            topicId = callbackMQTopic.getMsApprovalPass();
        } else if (url.endsWith("api/MsApproval/refuse")) {
            topicId = callbackMQTopic.getMsApprovalRefuse();
        }
        return topicId;
    }


}
