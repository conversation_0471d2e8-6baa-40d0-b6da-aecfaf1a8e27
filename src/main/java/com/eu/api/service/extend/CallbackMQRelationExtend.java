package com.eu.api.service.extend;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eu.common.ms.config.ConfigMs;
import com.eu.common.ms.config.pojo.request.ConfigRequest;
import com.eu.common.util.PropertiesUtil;
import com.eu.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * &#064;DATE: 2025/5/12 18:56
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@Service
public class CallbackMQRelationExtend {

    private static Map<String, String> map;

    /**
     * 获取对应的Topic
     * @param url
     * @return
     */
    public String getTopIc(String url) {
        if (StringUtil.isEmpty(url)) {
            return "";
        }
        if (map == null) {
            map = new HashMap<>();
            Properties properties = PropertiesUtil.getProperties();
            String appChannelId = properties.get("server.appChannelId").toString();
            Map<String, Object> config = ConfigMs.config(ConfigRequest.builder().appChannelId(appChannelId).build());
            if (Objects.isNull(config)) {
                log.error("获取回调失败");
            }
            JSONObject entries = JSONUtil.parseObj(config);
            JSONObject configJSON = entries.getJSONObject("config");
            if (Objects.isNull(configJSON)) {
                log.error("获取回调失败");
            }
            JSONObject callbackMq = configJSON.getJSONObject("callbackMq");    //获取MQ对应关系
            Set<String> keySet = callbackMq.keySet();
            for (String key : keySet) {
                map.put(key.trim(), callbackMq.getStr(key, "").trim());
            }
        }
        JSONObject entriesMap = JSONUtil.parseObj(map);
        return entriesMap.getStr(url.trim(), "");
    }

    /**
     * 热更新MQ对应的配置
     */
    public void updateMap() {
        map = new HashMap<>();
        Properties properties = PropertiesUtil.getProperties();
        String appChannelId = properties.get("server.appChannelId").toString();
        Map<String, Object> config = ConfigMs.config(ConfigRequest.builder().appChannelId(appChannelId).build());
        if (Objects.isNull(config)) {
            log.error("获取回调失败");
        }
        JSONObject entries = JSONUtil.parseObj(config);
        JSONObject configJSON = entries.getJSONObject("config");
        if (Objects.isNull(configJSON)) {
            log.error("获取回调失败");
        }
        JSONObject callbackMq = configJSON.getJSONObject("callbackMq");    //获取MQ对应关系
        Set<String> keySet = callbackMq.keySet();
        for (String key : keySet) {
            map.put(key.trim(), callbackMq.getStr(key, "").trim());
        }
    }

}
