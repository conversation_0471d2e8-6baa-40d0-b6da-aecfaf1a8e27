package com.eu.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.constant.FlowConstant;
import com.eu.api.domain.condition.*;
import com.eu.api.domain.dao.*;
import com.eu.api.domain.dto.*;
import com.eu.api.domain.entity.FlowEntity;
import com.eu.api.domain.entity.FlowPathListEntity;
import com.eu.api.domain.entity.FlowPathStepEntity;
import com.eu.api.domain.entity.TemplateEntity;
import com.eu.api.domain.ms.*;
import com.eu.api.mapper.*;
import com.eu.api.service.*;
import com.eu.api.service.extend.ApproveExtend;
import com.eu.api.service.extend.CallbackExtend;
import com.eu.api.service.extend.FlowServiceExtend;
import com.eu.api.service.extend.StepServiceExtend;
import com.eu.common.context.ServletContext;
import com.eu.common.exception.BsException;
import com.eu.common.holder.ApplicationHolder;
import com.eu.common.util.StringUtil;
import com.eu.common.util.TimeUtil;
import com.eu.common.util.TitleConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 审批业务操作实现
 * &#064;DATE: 2025/1/22 10:48
 * &#064;AUTHOR: XSL
 */
@Service
@Slf4j
public class ApprovealServiceImpl implements ApprovealService {

    @Autowired
    private FlowMapper flowMapper;
    @Autowired
    private FlowPathMapper flowPathMapper;
    @Autowired
    private FlowPathStepMapper flowPathStepMapper;
    @Autowired
    private FlowPathStepNotificationMapper flowPathStepNotificationMapper;
    @Autowired
    private FlowServiceExtend flowServiceExtend;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private FlowService flowService;
    @Autowired
    private FlowPathStepService flowPathStepService;
    @Autowired
    private FlowPathStepNotificationService flowPathStepNotificationService;
    @Autowired
    private StepServiceExtend stepServiceExtend;
    @Autowired
    private CallbackExtend callbackExtend;
    @Autowired
    private FlowPathService flowPathService;
    @Autowired
    private FlowPathHistoryMapper flowPathHistoryMapper;
    @Autowired
    private FlowPathListMapper flowPathListMapper;
    @Autowired
    private ApproveExtend approveExtend;
    @Autowired
    private GoFlowService goFlowService;


    /**
     * 审批发起 新
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer initiateApproval(MsFlowPathCreateRequest request) {
        if (request.getFlowId() == null) {
            throw new BsException("流程ID不能为空");
        }

        // 获取流程模板
        FlowDAO flowDAO = flowMapper.getFlow(GetFlowCondition.builder().flowId(request.getFlowId()).build());
        if (flowDAO == null) {
            throw new BsException("没有符合条件的审批模板");
        }
        if (Objects.isNull(flowDAO.getStatus()) || flowDAO.getStatus() == 0) {
            throw new BsException("流程已停用");
        }

        // 获取流程步骤列表
        List<StepDAO> stepList = flowService.getStepList(GetStepCondition.builder().flowId(flowDAO.getId()).build());

        // 获取模板信息
        TemplateDAO templateDAO = templateMapper.getTemplate(GetTemplateCondition.builder().id(flowDAO.getTempId()).build());
        String tempInfo = JSONUtil.toJsonStr(request.getTempInfo());
        if (templateDAO != null) {
            tempInfo = Optional.ofNullable(templateDAO.getTempInfo()).orElse("");
        }

        /*
         * 组装流程 flowPath
         */
        FlowPathDAO flowPathDAO = FlowPathDAO.builder()
                .flowId(flowDAO.getId())
                .flowTitle(flowDAO.getTitle())
                .title(StringUtil.isEmpty(request.getTitle()) ? flowDAO.getTitle() : request.getTitle())
                .uuid(request.getUuid())
                .showName(request.getShowName())
                .avatar(request.getAvatar())
                .tempInfo(tempInfo)
                .tempData(JSONUtil.toJsonStr(request.getTempData()))
                .tempShowData(JSONUtil.toJsonStr(request.getTempShowData()))
                .refuseUrl(StringUtil.isEmpty(request.getRefuseUrl()) ? flowDAO.getRefuseUrl() : request.getRefuseUrl())
                .passUrl(StringUtil.isEmpty(request.getPassUrl()) ? flowDAO.getPassUrl() : request.getPassUrl())
                .approverUrl(StringUtil.isEmpty(request.getApproverUrl()) ? flowDAO.getApproverUrl() : request.getApproverUrl())
                .ccCreate(request.getCcCreate())
                .withdraw(flowDAO.getWithdraw())
                .rejectMustAlert(flowDAO.getRejectMustAlert())
                .freedom(flowDAO.getFreedom())
                .countersign(flowDAO.getCountersign())
                .returnBack(flowDAO.getReturnBack())
                .autoappr(flowDAO.getAutoappr())
                .extraContent(request.getExtraContent() == null ? (StringUtil.isEmpty(flowDAO.getExtraContent()) ? "{}" : flowDAO.getExtraContent()) : JSONUtil.toJsonStr(request.getExtraContent()))
                .appChannelId(StringUtil.isEmpty(request.getAppChannelId()) ? ServletContext.getAppChannelId() : request.getAppChannelId())
                .dataChannelId(StringUtil.isEmpty(request.getDataChannelId()) ? ServletContext.getDataChannelId() : request.getDataChannelId())
                .isRejectNotice(flowDAO.getIsRejectNotice())
                .isRevokeNotice(flowDAO.getIsRevokeNotice())
                .isReadApprove(flowDAO.getIsReadApprove())
                .build();

        // 处理流程步骤
        List<StepInfoDTO> freeStepInfo = request.getFreeStepInfo();
        List<StepInfoDTO> stepInfoList = new ArrayList<>();
        long emptyStepIdCount = stepList.stream().filter(item -> StringUtil.isEmpty(item.getStepId())).count();
        if (stepList.size() == emptyStepIdCount || stepList.stream().anyMatch(item -> "start".equals(item.getStepId()))) {
            stepInfoList.addAll(freeStepInfo);
        } else {

            /*
             * 判断流程节点
             */
            List<StepDAO> stepDAOList = approveExtend.conditionJudgeStep(stepList, flowServiceExtend.getJsonObject(JSONUtil.parseObj(request.getTempData())));
            Map<String, List<StepInfoDTO>> stepInfoDTOListByStepIdMap = freeStepInfo.stream().collect(Collectors.groupingBy(StepInfoDTO::getStepId));
            for (StepDAO stepDAO : stepDAOList) {
                String stepId = stepDAO.getStepId();
                List<StepInfoDTO> stepInfoDTOS = stepInfoDTOListByStepIdMap.get(stepId);
                if (stepInfoDTOS == null) {
                    continue;
                }

                StepInfoDTO stepInfoDTO = StepInfoDTO.create(stepDAO);
                StepInfoDTO stepInfoDTOCopy = stepInfoDTOS.get(0);

                /*
                 * 补充数据 以自身数据为先 传递数据作为补充
                 */
                stepInfoDTO.setNotification(stepInfoDTOCopy.getNotification());
                stepInfoDTO.setNotificationList(stepInfoDTOCopy.getNotification());
                stepInfoDTO.setCcNotification(stepInfoDTOCopy.getCcNotification());
                stepInfoDTO.setCcNotificationList(stepInfoDTOCopy.getCcNotification());
                if (stepInfoDTO.getExtraContent() != null && stepInfoDTOCopy.getExtraContent() != null) {
                    stepInfoDTO.getExtraContent().putAll(stepInfoDTOCopy.getExtraContent());
                }
                stepInfoList.add(stepInfoDTO);
            }
            if (stepInfoList.isEmpty()) {
                Set<String> freeStepIds = freeStepInfo.stream().map(StepInfoDTO::getStepId).collect(Collectors.toSet());
                List<String> stepIds = stepList.stream()
                        .filter(item -> StringUtil.isNotEmpty(item.getStepId()))
                        .map(StepDAO::getStepId)
                        .collect(Collectors.toList());
                if (Collections.disjoint(freeStepIds, stepIds)) {
                    throw new BsException("当前审批模板有变更，请重新进入");
                }
            }
        }


        //没有任何步骤是不允许的，至少要有一个审批步骤
        if (stepInfoList.isEmpty()) {
            throw new BsException("审批提交失败: 当前审批没有任何有效审批人。");
        }

        flowPathDAO.setStepInfo(JSONUtil.toJsonStr(stepInfoList));
        if (flowPathMapper.addFlowPath(flowPathDAO) <= 0) {
            throw new BsException("添加审批流失败");
        }

        //存储flow_path_step表
        int stepNo = 1;
        int preId = 0;
        for (StepInfoDTO stepInfoDTO : stepInfoList) {
            FlowPathStepDAO flowPathStepDAO = flowPathStepService.addFlowPathStep(
                    flowPathDAO.getId(),
                    stepNo,
                    stepInfoDTO.getTitle(),
                    stepInfoDTO.getPassCons(),
                    stepInfoDTO.getNotification(),
                    stepInfoDTO.getCcNotification(),
                    stepInfoDTO.getPassAll(),
                    stepInfoDTO.getWithdraw(),
                    stepInfoDTO.getGoback(),
                    ApproveConstant.flowPathStatus.WAITING.getCode(),
                    ApproveConstant.appiont.NO.getCode(),
                    ApproveConstant.returnBack.NO.getCode(),
                    stepInfoDTO.getExtraContent(),
                    0,
                    preId,
                    stepInfoDTO.getStepType()
            );
            preId = flowPathStepDAO.getId();
            stepNo++;
        }
        return flowPathDAO.getId();
    }

    @Override
    public FlowPathDTO create(MsFlowPathCreateRequest request) {
        FlowDAO flowDAO = flowMapper.getFlow(GetFlowCondition.builder().flowId(request.getFlowId()).build());
        if (flowDAO == null) {
            throw new BsException("没有符合条件的审批模板");
        }
        TemplateDAO templateDAO = templateMapper.getTemplate(GetTemplateCondition.builder().id(flowDAO.getTempId()).build());
        String tempInfo = JSONUtil.toJsonStr(request.getTempInfo());
        if (templateDAO != null) {
            tempInfo = Optional.ofNullable(templateDAO.getTempInfo()).orElse("");
        }
        FlowPathDAO flowPathDAO = FlowPathDAO.builder()
                .flowId(flowDAO.getId())
                .flowTitle(flowDAO.getTitle())
                .title(StringUtil.isEmpty(request.getTitle()) ? flowDAO.getTitle() : request.getTitle())
                .uuid(request.getUuid())
                .showName(request.getShowName())
                .avatar(request.getAvatar())
                .tempInfo(tempInfo)
                .tempData(JSONUtil.toJsonStr(request.getTempData()))
                .tempShowData(JSONUtil.toJsonStr(request.getTempShowData()))
                .refuseUrl(StringUtil.isEmpty(request.getRefuseUrl()) ? flowDAO.getRefuseUrl() : request.getRefuseUrl())
                .passUrl(StringUtil.isEmpty(request.getPassUrl()) ? flowDAO.getPassUrl() : request.getPassUrl())
                .approverUrl(StringUtil.isEmpty(request.getApproverUrl()) ? flowDAO.getApproverUrl() : request.getApproverUrl())
                .ccCreate(request.getCcCreate())
                .withdraw(flowDAO.getWithdraw())
                .rejectMustAlert(flowDAO.getRejectMustAlert())
                .freedom(flowDAO.getFreedom())
                .countersign(flowDAO.getCountersign())
                .returnBack(flowDAO.getReturnBack())
                .autoappr(flowDAO.getAutoappr())
                .extraContent(request.getExtraContent() == null ? (StringUtil.isEmpty(flowDAO.getExtraContent()) ? "{}" : flowDAO.getExtraContent()) : JSONUtil.toJsonStr(request.getExtraContent()))
                .appChannelId(request.getAppChannelId())
                .dataChannelId(request.getDataChannelId())
                .build();
        List<StepInfoDTO> stepInfoList = new ArrayList<>();

        List<StepDAO> stepList = flowService.getStepList(GetStepCondition.builder().flowId(flowPathDAO.getFlowId()).build());

        if (flowDAO.getFreedom() > 0) {
            //自由审批
            if (request.getFreeStepInfo() != null && !request.getFreeStepInfo().isEmpty()) {
                int startStepId = 1;
                for (StepInfoDTO freeStep : request.getFreeStepInfo()) {
                    if (!freeStep.getNotification().isEmpty()) {
                        List<StepInfoNotificationDTO> freeStepNotificationList = freeStep.getNotification();
                        //子节点必须包含uuid/userId和showName
                        if (freeStepNotificationList.get(0).getUserId() != null
                                || freeStepNotificationList.get(0).getUuid() != null
                                || freeStepNotificationList.get(0).getShowName() != null) {
                            List<StepInfoNotificationDTO> availableNotificationList = new ArrayList<>();
                            //uuid和userId兼容，冗余茁壮//无有效信息的一律抛弃
                            for (StepInfoNotificationDTO freeStepNotification : freeStepNotificationList) {
                                if (!StringUtil.isEmpty(freeStepNotification.getUuid())) {
                                    freeStepNotification.setUserId(freeStepNotification.getUuid());
                                } else if (!StringUtil.isEmpty(freeStepNotification.getUserId())) {
                                    freeStepNotification.setUuid(freeStepNotification.getUserId());
                                } else {
                                    continue;
                                }
                                if (StringUtil.isEmpty(freeStepNotification.getShowName())) continue;
                                availableNotificationList.add(freeStepNotification);
                            }
                            freeStep.setNotificationList(availableNotificationList);
                            if (!freeStep.getNotificationList().isEmpty()) {
                                //至此，数据格式全部合法，和文档一致
                                //为所有步骤赋予id
                                freeStep.setId(startStepId++);
                                freeStep.setNotification(freeStep.getNotificationList());
//                                freeStep.setNotification(StringUtil.jsonEncode(freeStep.getNotificationList()));// = json_encode($freeStep['notification'], JSON_UNESCAPED_UNICODE);
                            }
                        }

                    }
                    //和notification检查数据一致，暂不封装
                    if (freeStep.getCcNotification() != null && !freeStep.getCcNotification().isEmpty()) {
                        List<StepInfoNotificationDTO> freeStepCcNotificationList = freeStep.getCcNotification();
                        if (freeStepCcNotificationList.get(0).getUserId() != null
                                || freeStepCcNotificationList.get(0).getUuid() != null
                                || freeStepCcNotificationList.get(0).getShowName() != null) {
                            List<StepInfoNotificationDTO> availableCcNotificationList = new ArrayList<>();
                            //uuid和userId兼容，冗余茁壮//无有效信息的一律抛弃
                            for (StepInfoNotificationDTO freeStepNotification : freeStepCcNotificationList) {
                                if (!StringUtil.isEmpty(freeStepNotification.getUuid())) {
                                    freeStepNotification.setUserId(freeStepNotification.getUuid());
                                } else if (!StringUtil.isEmpty(freeStepNotification.getUserId())) {
                                    freeStepNotification.setUuid(freeStepNotification.getUserId());
                                } else {
                                    continue;
                                }
                                if (StringUtil.isEmpty(freeStepNotification.getShowName())) continue;
                                availableCcNotificationList.add(freeStepNotification);
                            }
                            freeStep.setCcNotification(availableCcNotificationList);
                            freeStep.setCcNotificationList(availableCcNotificationList);
                        }
                    }
                    stepInfoList.add(freeStep);
                }
            }
        } else {
            //根据tempData创建步骤分支数据
            Optional<StepDAO> stepDAOOptional = stepList.stream().findFirst();
            if (!stepDAOOptional.isPresent()) {
                throw new BsException("审批提交失败: 当前审批没有任何有效审批人。");
            }
            StepDAO stepDAO = stepDAOOptional.get();
            if (StringUtil.isEmpty(stepDAO.getStepId())) {
                stepInfoList = flowService.createStepInfo(flowDAO.getId(), request.getTempData());
            } else {
                List<StepInfoDTO> freeStepInfo = request.getFreeStepInfo();
                Map<String, Boolean> verifyResult = request.getVerifyResult();
                stepInfoList = flowService.createStepList(flowDAO.getId(), request.getTempData(), freeStepInfo, verifyResult);
            }
        }
        //没有任何步骤是不允许的，至少要有一个审批步骤
        if (stepInfoList.isEmpty()) {
            throw new BsException("审批提交失败: 当前审批没有任何有效审批人。");
        }
        //存储基本数据
        flowPathDAO.setStepInfo(JSONUtil.toJsonStr(stepInfoList));

//        /*
//         * 判断是否是存在后续节点
//         */
//        StepInfoDTO stepInfo = stepInfoList.get(stepInfoList.size() - 1);    //获取最后一个节点
//        if (StrUtil.isNotEmpty(stepInfo.getStepId())) {
//            Map<String, List<StepDAO>> stepDAOListBySrcId = stepList.stream().collect(Collectors.groupingBy(StepDAO::getSrcId));
//            if (stepDAOListBySrcId.get(stepInfo.getStepId()) != null) {
//                String extraContent = flowPathDAO.getExtraContent();
//                JSONObject jsonObject = JSONUtil.parseObj(extraContent);
//                jsonObject.set("isEnd", false);
//                flowPathDAO.setExtraContent(jsonObject.toString());
//            }
//        }

        if (flowPathMapper.addFlowPath(flowPathDAO) <= 0) {
            throw new BsException("添加审批流失败");
        }
        //存储flow_path_step表
        int stepNo = 1;
        int preId = 0;
        for (StepInfoDTO stepInfoDTO : stepInfoList) {
            FlowPathStepDAO flowPathStepDAO = flowPathStepService.addFlowPathStep(
                    flowPathDAO.getId(),
                    stepNo,
                    stepInfoDTO.getTitle(),
                    stepInfoDTO.getPassCons(),
                    stepInfoDTO.getNotification(),
                    stepInfoDTO.getCcNotification(),
                    stepInfoDTO.getPassAll(),
                    stepInfoDTO.getWithdraw(),
                    stepInfoDTO.getGoback(),
                    ApproveConstant.flowPathStatus.WAITING.getCode(),
                    ApproveConstant.appiont.NO.getCode(),
                    ApproveConstant.returnBack.NO.getCode(),
                    stepInfoDTO.getExtraContent(),
                    0,
                    preId,
                    stepInfoDTO.getStepType()
            );
            preId = flowPathStepDAO.getId();
            stepNo++;
        }
        return flowPathService.getFlowPathDetail(flowPathDAO.getId());
    }


    @Override
    public FlowPathDTO withdraw(MsFlowPathWithdrawRequest request) {
        goFlowService.goFlowV2(request.getFlowPathId(), 3, request.getUuid(), request.getShowName(), request.getReason(), request.getAppChannelId(), request.getDataChannelId(), "", false);
        return flowPathService.getFlowPathDetail(request.getFlowPathId());
    }

    /**
     * 审批根据条件拼接后续节点
     *
     * @param request
     */
    @Override
    public void flowPathAddByCondition(MsFlowPathConditionRequest request) {
        Integer flowPathId = request.getFlowPathId();

        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(flowPathId);

        /*
         * 审批状态判断
         */
        if (flowPathDetail.getStatus() != ApproveConstant.flowPathStatus.WAITING.getCode()) {
            throw new BsException("审批流程已结束");
        }

        /*
         * 获取流程节点信息
         */
        List<StepDAO> stepList = flowService.getStepList(GetStepCondition.builder().flowId(flowPathDetail.getFlowId()).build());
        if (Objects.isNull(stepList) || stepList.isEmpty()) {
            throw new BsException("流程已删除");
        }


        List<FlowPathStepDTO> stepInfoList = flowPathDetail.getStepInfoList();  //审批流程
        List<FlowPathStepDTO> stepDTOList = stepInfoList.stream().filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());

        FlowPathStepDTO flowPathStepDTO = stepDTOList.get(stepDTOList.size() - 1);  //获取最后一个节点
        String stepId = JSONUtil.parseObj(flowPathStepDTO.getExtraContent()).getStr("stepId");
        if (StrUtil.isEmpty(stepId)) {
            return;
        }

        /*
         * 获取需要拼接点的节点
         */
        List<StepDAO> stepDAOList = stepList.stream().filter(step -> step.getSrcId().equals(stepId)).collect(Collectors.toList()); //获取条件节点
        Map<String, List<StepDAO>> StepBySrcIdMap = stepServiceExtend.getStepList(stepList, stepDAOList.stream().map(StepDAO::getStepId).collect(Collectors.toList()), false)
                .stream().collect(Collectors.groupingBy(StepDAO::getSrcId));


        List<NodeAgentDTO> nodeAgentDTOList = request.getNodeAgentDTOList();    //所有节点的办理人
        Map<Integer, List<NodeAgentDTO>> nodeAgentDTOByIdMap = nodeAgentDTOList.stream().collect(Collectors.groupingBy(NodeAgentDTO::getId));

        /*
         * 组装流程
         */
        List<FlowPathStepAddDTO> addFlowPathStepDTOList = new ArrayList<>();   //需要添加的节点
        String whileStepId = stepServiceExtend.conditionStepByJudge(stepDAOList, flowServiceExtend.getJsonObject(JSONUtil.parseObj(request.getTempData()))).getStepId();
        int stepNo = flowPathStepDTO.getStepNo();
        while (StrUtil.isNotEmpty(whileStepId)) {
            List<StepDAO> stepDAOListItem = StepBySrcIdMap.get(whileStepId); //获取下一节点ID
            if (Objects.isNull(stepDAOListItem)) {
                break;
            }
            for (StepDAO stepDAO : stepDAOListItem) {   //循环当前的所有的分支
                String conditions = stepDAO.getConditions();
                if (StrUtil.isEmpty(conditions)) {  //普通节点
                    List<NodeAgentDTO> nodeAgentDTOS = nodeAgentDTOByIdMap.get(stepDAO.getId());
                    if (Objects.isNull(nodeAgentDTOS)) {
                        whileStepId = stepDAO.getStepId();
                        continue;
                    }
                    NodeAgentDTO nodeAgentDTO = nodeAgentDTOS.get(0);
                    List<StepInfoNotificationDTO> notificationList = nodeAgentDTO.getNotificationList();
                    List<StepInfoNotificationDTO> ccNotificationList = nodeAgentDTO.getCcNotificationList();

                    if ((Objects.isNull(notificationList) || notificationList.isEmpty()) && (Objects.isNull(ccNotificationList) || ccNotificationList.isEmpty())) {
                        whileStepId = stepDAO.getStepId();
                        continue;
                    }

                    JSONObject map = JSONUtil.parseObj(JSONUtil.parseObj(stepDAO.getExtraContent()));

                    Map<String, Object> extraContent = nodeAgentDTO.getExtraContent();
                    if (Objects.nonNull(extraContent)) {
                        map.putAll(extraContent);
                    }

                    addFlowPathStepDTOList.add(FlowPathStepAddDTO.builder()
                            .flowPathId(flowPathId)
                            .stepNo(++stepNo)
                            .title(stepDAO.getTitle())
                            .passCons(stepDAO.getPassCons())
                            .passAll(stepDAO.getPassAll())
                            .withdraw(stepDAO.getWithdraw())
                            .goback(stepDAO.getGoback())
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .isReturnBack(ApproveConstant.returnBack.NO.getCode())
                            .isCountersign(ApproveConstant.appiont.NO.getCode())
                            .extraContent(map)
                            .stepType(stepDAO.getStepType())
                            .notificationList(nodeAgentDTO.getNotificationList())
                            .ccNotificationList(nodeAgentDTO.getCcNotificationList())
                            .build());
                    whileStepId = stepDAO.getStepId();
                }
            }
        }

        /*
         * 将节点添加到数据库
         */
        int id = flowPathStepDTO.getId();
        for (FlowPathStepAddDTO item : addFlowPathStepDTOList) {
            item.setPreId(id);
            FlowPathStepDAO flowPathStepDAO = flowPathStepService.addFlowPathStep(item);
            id = flowPathStepDAO.getId();
        }

        /*
         * 更新模版内容
         */
        FlowPathDAO flowPath = new FlowPathDAO();
        flowPath.setId(flowPathId);
        flowPath.setTempData(JSONUtil.toJsonStr(request.getTempData()));
        flowPathMapper.updateFlowPath(flowPath);

    }


    /**
     * 分支变动
     *
     * @param request
     */
    @Override
    public void embranchmentUpdate(EmbranchmentUpdateRequest request) {
        Integer flowPathId = request.getFlowPathId();
        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(flowPathId);
        if (Objects.isNull(flowPathDetail)) {
            throw new BsException("审批流程不存在");
        }

        /*
         * 审批状态判断
         */
        if (flowPathDetail.getStatus() != ApproveConstant.flowPathStatus.WAITING.getCode()) {
            throw new BsException("审批流程已结束");
        }

        /*
         * 获取流程节点信息
         */
        List<StepDAO> stepList = flowService.getStepList(GetStepCondition.builder().flowId(flowPathDetail.getFlowId()).build());
        if (Objects.isNull(stepList) || stepList.isEmpty()) {
            throw new BsException("流程已删除");
        }

        /*
         * 判断审批是否完结
         */
        List<FlowPathStepDTO> stepInfoList = flowPathDetail.getStepInfoList();  //审批流程
        List<FlowPathStepDTO> stepDTOList = stepInfoList.stream().filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());
        if (stepDTOList.isEmpty()) {
            throw new BsException("未查询到待审批节点");
        }

        /*
         * 更新表单数据最新内容
         */
        FlowPathDAO flowPath = new FlowPathDAO();
        flowPath.setId(flowPathId);
        flowPath.setTempShowData(JSONUtil.toJsonStr(request.getTempShowData()));
        flowPath.setTempData(JSONUtil.toJsonStr(request.getTempData()));
        flowPathMapper.updateFlowPath(flowPath);

        FlowPathStepDTO flowPathStepDTO = stepDTOList.get(0);  //获取第一个节点

        /*
         * 判断分支节点 并 组装流程
         */
        List<StepDAO> stepDAOList = approveExtend.conditionJudgeStep(stepList, flowServiceExtend.getJsonObject(JSONUtil.parseObj(request.getTempData())));

        Map<String, Object> extraContent = flowPathStepDTO.getExtraContent();
        if (Objects.isNull(extraContent)) {
            throw new BsException("未查询到对应节点");
        }

        String stepId = (String) extraContent.getOrDefault("stepId", "");
        if (StringUtil.isEmpty(stepId)) {
            throw new BsException("未查询到对应节点");
        }

        /*
         * 过滤出第一个待审批对应的节点
         */
        List<StepDAO> waitStepList = new ArrayList<>();
        boolean flag = false;
        for (StepDAO stepDAO : stepDAOList) {
            if (flag) {
                waitStepList.add(stepDAO);
            }
            if (stepId.equals(stepDAO.getStepId())) {
                flag = true;
            }
        }


        List<NodeAgentDTO> nodeAgentDTOList = request.getNodeAgentDTOList();    //所有节点的办理人
        Map<String, List<NodeAgentDTO>> nodeAgentDTOByStepIdMap = nodeAgentDTOList.stream().filter(item -> StringUtil.isNotEmpty(item.getStepId())).collect(Collectors.groupingBy(NodeAgentDTO::getStepId));

        /*
         * 组装流程
         */
        List<FlowPathStepAddDTO> addFlowPathStepDTOList = new ArrayList<>();   //需要添加的节点
        int stepNo = flowPathStepDTO.getStepNo();
        for (StepDAO stepDAO : waitStepList) {
            List<NodeAgentDTO> nodeAgentDTOS = nodeAgentDTOByStepIdMap.get(stepDAO.getStepId());
            if (Objects.isNull(nodeAgentDTOS)) {
                continue;
            }
            NodeAgentDTO nodeAgentDTO = nodeAgentDTOS.get(0);
            List<StepInfoNotificationDTO> notificationList = nodeAgentDTO.getNotificationList();
            List<StepInfoNotificationDTO> ccNotificationList = nodeAgentDTO.getCcNotificationList();

            if ((Objects.isNull(notificationList) || notificationList.isEmpty()) && (Objects.isNull(ccNotificationList) || ccNotificationList.isEmpty())) {
                continue;
            }

            JSONObject jsonObject = JSONUtil.parseObj(JSONUtil.parseObj(stepDAO.getExtraContent()));

            Map<String, Object> extraContentItem = nodeAgentDTO.getExtraContent();
            // 复制的节点不需要暴力描述和附件信息
            extraContentItem.remove("description"); // 清空description
            List<StepInfoNotificationDTO> notification = nodeAgentDTO.getNotificationList();
            if (notification != null && !notification.isEmpty()) {
                StepInfoNotificationDTO dto = notification.get(0);
                Map<String, Object> extra = dto.getExtraContent();
                if (Objects.nonNull(extra)) {
                    extra.remove("transferFiles");
                }
            }
            if (Objects.nonNull(extraContentItem)) {
                jsonObject.putAll(extraContentItem);
            }
            jsonObject.set("stepId", nodeAgentDTO.getStepId());

            addFlowPathStepDTOList.add(FlowPathStepAddDTO.builder()
                    .flowPathId(flowPathId)
                    .stepNo(++stepNo)
                    .title(stepDAO.getTitle())
                    .passCons(stepDAO.getPassCons())
                    .passAll(stepDAO.getPassAll())
                    .withdraw(stepDAO.getWithdraw())
                    .goback(stepDAO.getGoback())
                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                    .isReturnBack(ApproveConstant.returnBack.NO.getCode())
                    .isCountersign(ApproveConstant.appiont.NO.getCode())
                    .extraContent(jsonObject)
                    .stepType(stepDAO.getStepType())
                    .notificationList(nodeAgentDTO.getNotificationList())
                    .ccNotificationList(nodeAgentDTO.getCcNotificationList())
                    .build());
        }

        if (addFlowPathStepDTOList.isEmpty()) {
            return;
        }

        /*
         * 删除掉旧的审批节点及办理人
         */
        LambdaQueryWrapper<FlowPathStepEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FlowPathStepEntity::getFlowPathId, flowPathId);
        queryWrapper.gt(FlowPathStepEntity::getStepNo, flowPathStepDTO.getStepNo());
        int delete = flowPathStepMapper.delete(queryWrapper);
        if (delete > 0) {
            flowPathStepNotificationMapper.deleteByFlowPathIdAndStepNoScope(flowPathId, flowPathStepDTO.getStepNo(), Collections.emptyList());
        }

        /*
         * 将节点添加到数据库
         */
        int id = flowPathStepDTO.getId();
        for (FlowPathStepAddDTO item : addFlowPathStepDTOList) {
            item.setPreId(id);
            FlowPathStepDAO flowPathStepDAO = flowPathStepService.addFlowPathStep(item);
            id = flowPathStepDAO.getId();
        }


    }

    @Override
    public void batchEmbranchmentUpdate(List<EmbranchmentUpdateRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return;
        }
        // 1. 收集所有 flowPathId
        List<Integer> flowPathIds = requests.stream()
                .map(EmbranchmentUpdateRequest::getFlowPathId)
                .distinct()
                .collect(Collectors.toList());

        // 2. 批量拉取流程详情
        Map<Integer, FlowPathDTO> flowPathDetailMap = flowPathService
                .getFlowPathDetails(flowPathIds)
                .stream()
                .collect(Collectors.toMap(FlowPathDTO::getId, Function.identity()));

        // 3. 校验所有流程
        for (Integer id : flowPathIds) {
            FlowPathDTO dto = flowPathDetailMap.get(id);
            if (dto == null) {
                throw new BsException("审批流程不存在，flowPathId=" + id);
            }
            if (dto.getStatus() != ApproveConstant.flowPathStatus.WAITING.getCode()) {
                throw new BsException("审批流程已结束，flowPathId=" + id);
            }
        }


//        Map<Integer, List<StepDAO>> stepListMap = flowService
//                .getStepListByFlowIds();
        // 4. 批量拉取所有涉及的 stepList（按 flowId 分组）
        Map<String, List<StepDAO>> stepListMap = flowService.getStepListByFlowIds(flowPathDetailMap.values().stream()
                .map(FlowPathDTO::getFlowId).distinct().collect(Collectors.toList()));


        // 5. 对每个 request 进行处理
        for (EmbranchmentUpdateRequest req : requests) {
            Integer flowPathId = req.getFlowPathId();
            FlowPathDTO flowPathDetail = flowPathDetailMap.get(flowPathId);
            List<StepDAO> stepList = stepListMap.get(flowPathDetail.getFlowId());
            if (stepList == null || stepList.isEmpty()) {
                throw new BsException("流程已删除，flowPathId=" + flowPathId);
            }

            // 5.1 找到第一个待审批节点
            List<FlowPathStepDTO> waitingSteps = flowPathDetail.getStepInfoList().stream()
                    .filter(s -> s.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode())
                    .collect(Collectors.toList());
            if (waitingSteps.isEmpty()) {
                throw new BsException("未查询到待审批节点，flowPathId=" + flowPathId);
            }
            FlowPathStepDTO currentStep = waitingSteps.get(0);

            // 5.2 调用 conditionJudgeStep & getJsonObject（保持单条调用）
            List<StepDAO> branchSteps = approveExtend.conditionJudgeStep(
                    stepList,
                    flowServiceExtend.getJsonObject(JSONUtil.parseObj(req.getTempData()))
            );

            // 5.3 从 currentStep.extraContent 拿到 nextStepId，然后筛出 waitStepList
            Map<String, Object> extra = currentStep.getExtraContent();
            String stepId = Objects.isNull(extra) ? "" : (String) extra.getOrDefault("stepId", "");
            if (StringUtil.isEmpty(stepId)) {
                throw new BsException("未查询到对应节点，flowPathId=" + flowPathId);
            }
            List<StepDAO> waitStepList = new ArrayList<>();
            boolean found = false;
            for (StepDAO s : branchSteps) {
                if (found) waitStepList.add(s);
                if (stepId.equals(s.getStepId())) {
                    found = true;
                }
            }

            // 5.4 构建所有新增节点 DTO
            Map<String, List<NodeAgentDTO>> agentMap = req.getNodeAgentDTOList().stream()
                    .filter(a -> StringUtil.isNotEmpty(a.getStepId()))
                    .collect(Collectors.groupingBy(NodeAgentDTO::getStepId));

            List<FlowPathStepAddDTO> toAdd = new ArrayList<>();
            int stepNo = currentStep.getStepNo();
            for (StepDAO s : waitStepList) {
                List<NodeAgentDTO> agents = agentMap.get(s.getStepId());
                if (agents == null || agents.isEmpty()) continue;
                NodeAgentDTO agent = agents.get(0);
                if ((agent.getNotificationList() == null || agent.getNotificationList().isEmpty())
                        && (agent.getCcNotificationList() == null || agent.getCcNotificationList().isEmpty())) {
                    continue;
                }
                JSONObject cfg = JSONUtil.parseObj(s.getExtraContent());
                if (agent.getExtraContent() != null) {
                    cfg.putAll(agent.getExtraContent());
                }
                cfg.set("stepId", agent.getStepId());

                toAdd.add(FlowPathStepAddDTO.builder()
                        .flowPathId(flowPathId)
                        .stepNo(++stepNo)
                        .title(s.getTitle())
                        .passCons(s.getPassCons())
                        .passAll(s.getPassAll())
                        .withdraw(s.getWithdraw())
                        .goback(s.getGoback())
                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                        .isReturnBack(ApproveConstant.returnBack.NO.getCode())
                        .isCountersign(ApproveConstant.appiont.NO.getCode())
                        .extraContent(cfg)
                        .stepType(s.getStepType())
                        .notificationList(agent.getNotificationList())
                        .ccNotificationList(agent.getCcNotificationList())
                        .build());
            }

            if (!toAdd.isEmpty()) {
                // 删除旧节点
                LambdaQueryWrapper<FlowPathStepEntity> delWrapper = new LambdaQueryWrapper<>();
                delWrapper.eq(FlowPathStepEntity::getFlowPathId, flowPathId)
                        .gt(FlowPathStepEntity::getStepNo, currentStep.getStepNo());
                int deleted = flowPathStepMapper.delete(delWrapper);
                if (deleted > 0) {
                    flowPathStepNotificationMapper.deleteByFlowPathIdAndStepNoScope(
                            flowPathId, currentStep.getStepNo(), Collections.emptyList());
                }
                //批量插入新节点
                int preId = currentStep.getId();
                for (FlowPathStepAddDTO addDto : toAdd) {
                    addDto.setPreId(preId);
                    FlowPathStepDAO dao = flowPathStepService.addFlowPathStep(addDto);
                    preId = dao.getId();
                }
            }

            //更新表单数据
            FlowPathDAO upd = new FlowPathDAO();
            upd.setId(flowPathId);
            upd.setTempShowData(JSONUtil.toJsonStr(req.getTempShowData()));
            upd.setTempData(JSONUtil.toJsonStr(req.getTempData()));
            flowPathMapper.updateFlowPath(upd);
        }
    }

    @Override
    public void addFlowPathListAndNotify(UpdateStepNotificationRequest request, Map<String, List<String>> changeUuidMap) {
        // 如果没有变更审核人，直接过
        if (Objects.isNull(changeUuidMap) || (CollectionUtil.isEmpty(changeUuidMap.get("addUuidList")) && CollectionUtil.isEmpty(changeUuidMap.get("delUuidList")))) {
            return;
        }
        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(request.getFlowPathId()).build());
        if (Objects.isNull(flowPathDAO)) {
            return;
        }
        List<String> addUuidList = changeUuidMap.get("addUuidList");
        List<String> delUuidList = changeUuidMap.get("delUuidList");
        Map<String, StepInfoNotificationDTO> uuidDtoMap = Optional.of(request)
                .map(UpdateStepNotificationRequest::getStepInfoNotificationList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(dto -> dto.getExtraContent() != null
                        && dto.getExtraContent().containsKey("delegationId")
                        && dto.getExtraContent().get("delegationId") instanceof String)
                .collect(Collectors.toMap(
                        StepInfoNotificationDTO::getUuid,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
        if (CollectionUtil.isNotEmpty(delUuidList)) {
            // 删除用户我的审批列表待审数据
            flowPathListMapper.delete(GetFlowPathListDAOListCondition.builder()
                    .flowPathId(request.getFlowPathId())
                    .type(ApproveConstant.flowPathListType.WAIT.getCode())
                    .uuidList(delUuidList).build());
        }
        if (CollectionUtil.isNotEmpty(addUuidList)) {
            String flowTitle = Optional.of(flowPathDAO.getTitle()).orElse(flowPathDAO.getFlowTitle());
            String title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
            title = configTitle(title, flowPathDAO);
            String flowPathExtraContent = Optional.of(flowPathDAO.getExtraContent()).orElse("{}");
            // 添加
            List<FlowPathListDAO> insertList = new ArrayList<>();
            String appChannelId = ServletContext.getAppChannelId();
            String dataChannelId = ServletContext.getDataChannelId();
            for (String addUuid : addUuidList) {
                String extraContent = flowPathExtraContent;
                LocalDateTime agentExpirationTime = null;
                if (uuidDtoMap.containsKey(addUuid)) {
                    try {
                        JSONObject jsonObject = JSONUtil.parseObj(extraContent);
                        StepInfoNotificationDTO dto = uuidDtoMap.get(addUuid);
                        if(Objects.nonNull(dto) && dto.getExtraContent().containsKey("delegationId")) {
                            String delegationId = (String) dto.getExtraContent().get("delegationId");
                            jsonObject.put("delegationId", delegationId);
                        }
                        agentExpirationTime = TimeUtil.parseLocalDateTime(dto.getAgentExpirationTime());
                        extraContent = jsonObject.toString();
                    } catch (Exception ignored) {
                    }
                }
                insertList.add(
                        FlowPathListDAO
                                .builder()
                                .title(title)
                                .flowPathId(request.getFlowPathId())
                                .uuid(addUuid)
                                .type(ApproveConstant.flowPathListType.WAIT.getCode())
                                .appChannelId(appChannelId)
                                .dataChannelId(dataChannelId)
                                .extraContent(extraContent)
                                .agentExpirationTime(agentExpirationTime)
                                .build()
                );
            }
            if (!insertList.isEmpty()) {
                flowPathListMapper.batchAddLists(insertList);
            }
            // 消息通知
            Map<String, Object> callbackRequestMap = new HashMap<>();
            callbackRequestMap.put("flowId", flowPathDAO.getFlowId());
            callbackRequestMap.put("flowPathId", flowPathDAO.getId());
            callbackRequestMap.put("userIdList", addUuidList);
            callbackRequestMap.put("action", "approve");
            callbackRequestMap.put("event", "wait");
            callbackRequestMap.put("title", title);
            callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackRequestMap);
        }
    }

    /**
     * 审批 数据推进方法
     *
     * @param id            审批流程ID
     * @param action        审批操作类型
     * @param uuid          操作人身份标识
     * @param showName      操作人名称
     * @param reason        操作备注
     * @param appChannelId  操作客户端渠道ID
     * @param dataChannelId 操作数据源ID
     * @param title         操作标题
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean goFlow(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title) {
        // 1、获取 当前操作 审批流程数据
        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(id).build());
        if (flowPathDAO == null) {
            throw new BsException("审批流程不存在");
        }
        if (ApproveConstant.flowPathStatus.WAITING.getCode() != flowPathDAO.getStatus()) {
            throw new BsException("审批流程已结束");
        }
        if (ApproveConstant.flowPathIsInterrupt.YES.getCode() == flowPathDAO.getIsInterrupt()) {
            throw new BsException("审批流程已被暂停");
        }

        /*
         * 判读是否需要驳回
         */
        boolean flag = approveReject(flowPathDAO.getId(), flowPathDAO, showName, title, appChannelId, dataChannelId);
        if (flag) { //已驳回
            return true;
        }

        // 2、读取 该审批流程 预设审批步骤
        List<FlowPathStepDTO> stepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(id)
                .build());

        // 3、整理操作所需的变量
        String flowTitle = Optional.of(flowPathDAO.getTitle()).orElse(flowPathDAO.getFlowTitle());
        String flowPathExtraContent = Optional.of(flowPathDAO.getExtraContent()).orElse("{}");  //审批流flowpathList扩展字段
        //标记待审
        FlowPathStepDTO apprNotification = null;
        List<String> approvalUuidList = new ArrayList<>();
        int thisStepCompleted = 0;
        int stepCompleted = 0;
        //根据设定流转
        List<FlowPathStepNotificationDTO> ccNotificationList = new ArrayList<>();
        FlowPathHistoryDAO flowPathHistoryDAO = null;

        // 4、开始流转
        // 创建审批流程
        if (action == ApproveConstant.flowPathStatus.WAITING.getCode()) {
            // 创建审批实例 不需要验证权限
            // 创建审批流时，直接下一步:相当于通过
            title = StringUtil.isEmpty(title) ? (showName + " 创建了 " + flowTitle + "申请") : title;

            // 存储操作历史
            flowPathHistoryDAO = FlowPathHistoryDAO
                    .builder()
                    .title(title)
                    .flowPathId(id)
                    .uuid(uuid)
                    .stepId(0)
                    .nextStepId(stepInfoDTOList.get(0).getId())
                    .action(action)
                    .reason(reason)
                    .build();
            flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
            flowTitle = configTitle(flowTitle, flowPathDAO); //合并代码

            // 存储到 "我的" 审批列表
            flowPathListMapper.addFlowPathList(
                    FlowPathListDAO
                            .builder()
                            .title(flowTitle)
                            .flowPathId(id)
                            .uuid(uuid)
                            .type(1)
                            .appChannelId(appChannelId)
                            .dataChannelId(dataChannelId)
                            .extraContent(flowPathExtraContent)
                            .build()
            );
            apprNotification = stepInfoDTOList.get(0);  //第一个节点
            List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
            if (Objects.isNull(notificationList) || notificationList.isEmpty()) {
                thisStepCompleted = 1;
            }
        }
        //创建END
        //已经通过的审批人//有序
        Map<Integer, List<String>> passStepUuidMap = new HashMap<>();
        for (FlowPathStepDTO flowPathStepDTO : stepInfoDTOList) {
            List<String> passStepUuidList = passStepUuidMap.getOrDefault(flowPathStepDTO.getId(), new ArrayList<>());
            List<FlowPathStepNotificationDTO> notificationList = flowPathStepDTO.getNotificationList();
            if (Objects.nonNull(notificationList) && !notificationList.isEmpty()) {
                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : notificationList) {
                    if (flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()) {
                        passStepUuidList.add(flowPathStepNotificationDTO.getUuid());
                    }
                }
            }
            passStepUuidMap.put(flowPathStepDTO.getId(), passStepUuidList);
        }
        if (action > ApproveConstant.flowPathAction.CREATE.getCode()) {
            //读取当前步骤&上一步&下一步
            FlowPathStepDTO thisStepInfo = null, nextStepInfo = null, lastStepInfo = null;
            List<FlowPathStepDTO> passStepList = new ArrayList<>();
            int size = stepInfoDTOList.size();
            for (int i = 0; i < size; i++) {
                //取出第一个待审核的步骤，为当前步骤
                if (stepInfoDTOList.get(i).getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {

                    /*
                     * 判断空节点 执行自动通过
                     */
                    FlowPathStepDTO flowPathStepDTO = stepInfoDTOList.get(i);
                    List<FlowPathStepNotificationDTO> notification = flowPathStepDTO.getNotification();
                    List<FlowPathStepNotificationDTO> ccNotification = flowPathStepDTO.getCcNotification();
                    if ((Objects.isNull(notification) || notification.isEmpty()) && (Objects.isNull(ccNotification) || ccNotification.isEmpty())) { //如果节点办理人或抄送人为空  就是空节点
                        flowPathStepService.update(
                                FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                                GetFlowPathStepCondition.builder().flowPathId(flowPathStepDTO.getFlowPathId()).stepNo(flowPathStepDTO.getStepNo()).build()
                        );
                        continue;
                    }

                    thisStepInfo = stepInfoDTOList.get(i);
                    if ((i + 1) < stepInfoDTOList.size()) {
                        nextStepInfo = stepInfoDTOList.get(i + 1);
                    }
                    if (i > 0) {
                        lastStepInfo = stepInfoDTOList.get(i - 1);
                    }
                    break;
                } else if (stepInfoDTOList.get(i).getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()) {
                    passStepList.add(stepInfoDTOList.get(i));
                }
            }
            if (thisStepInfo == null) {
                throw new BsException("审批流程已结束");
            }

            int thisStepHisPathId = thisStepInfo.getId();
            //业务通知和抄送通知数据
            ccNotificationList = thisStepInfo.getCcNotificationList();
            //权限验证
            //审批通过或拒绝//1拒绝2通过
            if (action == ApproveConstant.flowPathAction.REFUSE.getCode() || action == ApproveConstant.flowPathAction.PASS.getCode()) {
                //查看设定：全员、任一、依次
                int passAll = thisStepInfo.getPassAll();
                int isNextPerson = 0;
                if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
                    //依次审批查验是否是当前步骤下一个待审
                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                        if (notifyOne.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
                            //如果当前步骤待审批人是此人
                            if (notifyOne.getUuid().equals(uuid)) {
                                isNextPerson = 1;
                            }
                            break;
                        }
                    }
                } else {
                    //全员审批
                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                        //如果当前步骤其中一个审批人是此人，验证前两个是否和历史记录相符
                        //全员审批和任一审批的判断依据都是是否在当前步骤下
                        if (notifyOne.getUuid().equals(uuid)
                                && notifyOne.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
                            isNextPerson = 1;
                            break;
                        }
                    }
                }
                if (isNextPerson == 0) {
                    throw new BsException("对不起，当前用户权限不符合当前规则");
                }
                //===== 分割线 =====
                //权限验证完毕，审核环节
                if (action == ApproveConstant.flowPathAction.REFUSE.getCode()) {
                    //拒绝操作
                    //更新flow_path表状态
                    flowPathMapper.updateFlowPath(
                            FlowPathDAO.builder()
                                    .id(id)
                                    .status(ApproveConstant.flowPathStatus.REFUSED.getCode())
                                    .updateTime(new Date())
                                    .build()
                    );
                    //更新flow_path_step表
                    flowPathStepService.update(
                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.REFUSED.getCode()).build(),
                            GetFlowPathStepCondition.builder().flowPathId(thisStepInfo.getFlowPathId()).stepNo(thisStepInfo.getStepNo()).build()
                    );
                    //更新flow_path_step_notification表
                    flowPathStepNotificationService.update(
                            FlowPathStepNotificationDAO.builder()
                                    .reason(reason)
                                    .status(ApproveConstant.flowPathStatus.REFUSED.getCode())
                                    .updateTime(new Date())
                                    .build(),
                            GetFlowPathStepNotificationCondition.builder()
                                    .flowPathId(thisStepInfo.getFlowPathId())
                                    .stepNo(thisStepInfo.getStepNo())
                                    .uuid(uuid)
                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                                    .build()
                    );
                    //历史
                    title = StringUtil.isEmpty(title) ? (showName + " 驳回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).action(action).reason(reason).build();
                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
                    //更新 待我 处理的审批到 已处理//当前实例一起更新
                    flowPathListMapper.update(
                            FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                            GetFlowPathListDAOListCondition.builder().flowPathId(id).type(ApproveConstant.flowPathListType.WAIT.getCode()).build()
                    );
                    title = showName + "驳回了你的" + flowTitle + "申请";

                    //拒绝回调前，先进行通知回调
                    callbackExtend.callback(
                            flowPathDAO.getApproverUrl(),
                            callbackExtend.createNotificationData(
                                    flowPathDAO.getFlowId(),
                                    id,
                                    flowPathDAO.getUuid(),
                                    "approve",
                                    "refuse",
                                    flowPathDAO.getTempShowData(),
                                    title,
                                    "",
                                    flowPathDAO.getTempData(),
                                    JSONUtil.toJsonStr(stepInfoDTOList),
                                    flowPathDAO.getExtraContent(),
                                    flowPathDAO.getStatus()
                            )
                    );

                    //拒绝回调
                    callbackExtend.callback(flowPathDAO.getRefuseUrl(),
                            callbackExtend.createNotificationData(
                                    flowPathDAO.getFlowId(),
                                    id,
                                    flowPathDAO.getUuid(),
                                    "approve",
                                    "refuse",
                                    flowPathDAO.getTempShowData(),
                                    title,
                                    "",
                                    flowPathDAO.getTempData(),
                                    JSONUtil.toJsonStr(stepInfoDTOList),
                                    flowPathDAO.getExtraContent(),
                                    ApproveConstant.flowPathStatus.REFUSED.getCode() // flowPathDAO.getStatus()
                            )
                    );

                    /*
                     *  当设置了驳回后通知已审核人，那么就对已经审核通过的人进行通知
                     */
                    if (flowPathDAO.getIsRejectNotice() == 1) { // TODO 1111

                        /*
                         * 查询出驳回时已经审核过的人
                         */
                        Set<String> userIdList = new HashSet<>();
                        for (FlowPathStepDTO flowPathStepDTO : stepInfoDTOList) {
                            List<FlowPathStepNotificationDTO> notification = flowPathStepDTO.getNotification();
                            if (notification != null && !notification.isEmpty()) {
                                userIdList.addAll(notification.stream()
                                        .filter(flowPathStepNotificationDTO -> flowPathStepNotificationDTO.getStatus()
                                                == ApproveConstant.flowPathStatus.PASSED.getCode()).map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toList()));
                            }
                        }
                        if (!CollectionUtils.isEmpty(userIdList)) {
                            title = showName + " 驳回了 " + flowPathDAO.getShowName() + "的" + flowTitle;
//                            for (String uuidPass : userIdList) {
//                                //进行通知回调
//                                callbackExtend.callback(
//                                        flowPathDAO.getApproverUrl(),
//                                        callbackExtend.createNotificationData(
//                                                flowPathDAO.getFlowId(),
//                                                id,
//                                                uuidPass,
//                                                "approve",
//                                                "refuse",
//                                                flowPathDAO.getTempShowData(),
//                                                title,
//                                                "",
//                                                flowPathDAO.getTempData(),
//                                                JSONUtil.toJsonStr(stepInfoDTOList),
//                                                flowPathDAO.getExtraContent(),
//                                                flowPathDAO.getStatus()
//                                        )
//                                );
//                            }
                            Map<String, Object> callbackRequestMap = new HashMap<>();
                            callbackRequestMap.put("flowId", flowPathDAO.getFlowId());
                            callbackRequestMap.put("flowPathId", flowPathDAO.getId());
                            callbackRequestMap.put("userIdList", userIdList);
                            callbackRequestMap.put("action", "approve");
                            callbackRequestMap.put("event", "refuse");
                            callbackRequestMap.put("title", title);
                            callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackRequestMap);   //发起通知
                        }
                    }

                    if (flowPathDAO.getWithdrawPathId() != 0) {
                        // 恢复 撤回目标流程 使其继续执行
                        proceedApproval(flowPathDAO.getWithdrawPathId());
                    }

                    //拒绝实例操作完成
                    stepCompleted = 1;
                }

                /*
                 * 通过操作
                 */
                else {
                    //通过操作 审批通过
                    FlowPathStepNotificationDTO nextNotification = null;//下一个审批人
                    int toNext = 1;
                    ArrayList<String> notificationUuidList = new ArrayList<>();
                    /*
                     * 检查流程状态是全员通过还是依次通过
                     */
                    if (passAll == ApproveConstant.flowPassAll.ALL.getCode() || passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
                        //全员通过和依次通过都是验证是否全部通过
                        for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                            //过滤掉转审状态的审核人或加签状态的审批人
                            if (notifyOne.getStatus() == ApproveConstant.flowPathStatus.TRANSFERRED.getCode()) {
                                continue;
                            }
                            notificationUuidList.add(notifyOne.getUuid());
                            //如果当前步骤其中一个审批人是此人，验证前两个是否和历史记录相符
                            //全员审批和任一审批的判断依据都是是否在当前步骤下
                            if (!passStepUuidMap.containsKey(thisStepHisPathId)) {
                                passStepUuidMap.put(thisStepHisPathId, new ArrayList<>());
                            }
                            List<String> uuidList = passStepUuidMap.get(thisStepHisPathId);
                            uuidList.add(uuid);
                            passStepUuidMap.put(thisStepHisPathId, uuidList);
                            if (!uuidList.contains(notifyOne.getUuid())) {
                                //依次通过模式只通知下一个审批人，先记录下一个审批人uuid，后面触发通知
                                if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() && nextNotification == null) {
                                    nextNotification = notifyOne;
                                }
                                toNext = 0;
                            }
                        }
                    } else {
                        //当前审核人加入已通过的uuid
                        if (!passStepUuidMap.containsKey(thisStepHisPathId)) {
                            passStepUuidMap.put(thisStepHisPathId, new ArrayList<>());
                        }
                        List<String> uuidList = passStepUuidMap.get(thisStepHisPathId);
                        uuidList.add(uuid);
                        passStepUuidMap.put(thisStepHisPathId, uuidList);
                    }
                    //历史
                    title = StringUtil.isEmpty(title) ? (showName + " 通过了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
                    if (toNext == 0) {
                        //如果不去下一步，那么下一步还是这一步，等待全员审核后才能下一步
                        nextStepInfo = thisStepInfo;
                        //更新 待我 处理的审批到 已处理//当前实例一起更新
                        flowPathListMapper.update(
                                FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                                GetFlowPathListDAOListCondition.builder().flowPathId(id).uuid(uuid).type(ApproveConstant.flowPathListType.WAIT.getCode()).build()
                        );
                        //依次通过模式通知下一个审批人
                        if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()
                                && nextNotification != null) {
                            apprNotification = nextStepInfo;
//                            callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackExtend.createNotificationData(flowPathDAO.getFlowId(), id, nextNotification.getUuid(), "approve", "normal", flowPathDAO.getTempShowData(), flowTitle, JSONUtil.toJsonStr(nextNotification), ""));
                            approvalUuidList.add(nextNotification.getUuid());
                        }
                    }
                    else {
                        Map<Integer, List<FlowPathStepDTO>> stepInfoDTOListByStepNoMap = stepInfoDTOList.stream().collect(Collectors.groupingBy(FlowPathStepDTO::getStepNo));

                        /*
                         * 处理空节点  执行自动通过  ----------TODO
                         */
                        if (Objects.nonNull(nextStepInfo)) {
                            Integer stepNo = nextStepInfo.getStepNo();
                            while (true) {
                                List<FlowPathStepDTO> flowPathStepDTOS = stepInfoDTOListByStepNoMap.get(stepNo);
                                if (Objects.isNull(flowPathStepDTOS)) {
                                    break;
                                }
                                nextStepInfo = flowPathStepDTOS.get(0);
                                stepNo = stepNo + 1;
                                List<FlowPathStepNotificationDTO> notification = nextStepInfo.getNotification();
                                List<FlowPathStepNotificationDTO> ccNotification = nextStepInfo.getCcNotification();
                                if ((Objects.isNull(notification) || notification.isEmpty()) && (Objects.isNull(ccNotification) || ccNotification.isEmpty())) { //如果节点办理人或抄送人为空  就是空节点
                                    flowPathStepService.update(
                                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                                            GetFlowPathStepCondition.builder().flowPathId(nextStepInfo.getFlowPathId()).stepNo(nextStepInfo.getStepNo()).build()
                                    );
                                } else {
                                    break;
                                }
                            }
                        }

                        // --------------------------------------TODO

                        //判断下一步是否存在//如果没有下一步，那么审批结束，全流程通过
                        if (nextStepInfo == null) {
                            //更新状态
                            flowPathMapper.updateFlowPath(
                                    FlowPathDAO.builder()
                                            .id(id)
                                            .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                                            .updateTime(new Date())
                                            .build()
                            );
                            //通过实例操作完成
                            stepCompleted = 1;
                            thisStepCompleted = 1;

                            //通过回调前，通知回调
                            callbackExtend.callback(
                                    flowPathDAO.getApproverUrl(),
                                    callbackExtend.createNotificationData(
                                            flowPathDAO.getFlowId(),
                                            id,
                                            flowPathDAO.getUuid(),
                                            "approve",
                                            "pass",
                                            flowPathDAO.getTempShowData(),
                                            title,
                                            "",
                                            flowPathDAO.getTempData(),
                                            JSONUtil.toJsonStr(stepInfoDTOList),
                                            flowPathDAO.getExtraContent(),
                                            flowPathDAO.getStatus()
                                    )
                            );

                            //通过回调
                            callbackExtend.callback(
                                    flowPathDAO.getPassUrl(),
                                    callbackExtend.createNotificationData(
                                            flowPathDAO.getFlowId(),
                                            id,
                                            flowPathDAO.getUuid(),
                                            "approve",
                                            "pass",
                                            flowPathDAO.getTempShowData(),
                                            title,
                                            "",
                                            flowPathDAO.getTempData(),
                                            JSONUtil.toJsonStr(stepInfoDTOList),
                                            flowPathDAO.getExtraContent(),
                                            ApproveConstant.flowPathAction.PASS.getCode()
                                            // flowPathDAO.getStatus()
                                    )
                            );

                            if (flowPathDAO.getWithdrawPathId() != 0) {
                                // 正常撤回，直接撤回成功
                                withdrawnPermit(flowPathDAO.getWithdrawPathId());
                            }
                        }
                        else {
                            //跳步抄送
                            thisStepCompleted = 1;
                            //标记待审
                            apprNotification = nextStepInfo;
                        }
                        //更新 待我 处理的审批到 已处理//当前实例一起更新的当前步骤
                        GetFlowPathListDAOListCondition condition = GetFlowPathListDAOListCondition.builder()
                                .flowPathId(id)
                                .type(ApproveConstant.flowPathListType.WAIT.getCode())
                                .build();
                        if (!notificationUuidList.isEmpty()) {
                            condition.setUuidList(notificationUuidList);
                        }
                        flowPathListMapper.update(FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(), condition);
                        //更新flow_path_step表
                        flowPathStepService.update(
                                FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                                GetFlowPathStepCondition.builder().flowPathId(thisStepInfo.getFlowPathId()).stepNo(thisStepInfo.getStepNo()).build()
                        );
                    }
                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).nextStepId(nextStepInfo == null ? 0 : nextStepInfo.getId()).action(action).reason(reason).build();
                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
                    //更新flow_path_step_notification表
                    flowPathStepNotificationService.update(
                            FlowPathStepNotificationDAO.builder()
                                    .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                                    .updateTime(new Date())
                                    .build(),
                            GetFlowPathStepNotificationCondition.builder()
                                    .flowPathId(thisStepInfo.getFlowPathId())
                                    .stepNo(thisStepInfo.getStepNo())
                                    .uuid(uuid)
                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                                    .build()
                    );
                }
            }
            else if (action == ApproveConstant.flowPathAction.WITHDRAW.getCode()) {      //撤回
                if (!flowPathDAO.getUuid().equals(uuid)) {
                    throw new BsException("对不起，当前用户权限不符合当前规则");
                }
                if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.PROHIBIT.getCode()) {
                    throw new BsException("对不起，当前流程不支持撤销操作");
                } else if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.PERMIT.getCode()) {
                    // 正常撤回，直接撤回成功
                    withdrawnPermit(id);
                    stepCompleted = 1;
                } else if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.CONFIRMATION.getCode()) {
                    // 如果当前步骤内有已审核通过的人，则将该步骤也放入循环中，当作新审批内的最后一步
                    List<FlowPathStepNotificationDTO> thisStepPassApprover = thisStepInfo.getNotification().stream().filter(flowPathStepNotificationDTO -> flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()).collect(Collectors.toList());
                    if (!thisStepPassApprover.isEmpty()) {
                        passStepList.add(thisStepInfo);
                    }
                    if (passStepList.isEmpty()) {
                        withdrawnPermit(id);
                    } else {
                        withdrawnConfirmation(flowPathDAO, passStepList);
                    }
                }
                /*
                 * 当设置了撤销后通知已审核人，那么就对已经审核通过的人进行通知
                 */

                if (flowPathDAO.getIsRevokeNotice() == 1) {
                    Set<String> uuidList = new HashSet<>();
                    /*
                     * 查询出驳回时已经审核过的人
                     */
                    for (FlowPathStepDTO flowPathStepDTO : stepInfoDTOList) {
                        List<FlowPathStepNotificationDTO> notification = flowPathStepDTO.getNotification();
                        if (notification != null && !notification.isEmpty()) {
                            uuidList.addAll(notification.stream()
                                    .filter(flowPathStepNotificationDTO -> flowPathStepNotificationDTO.getStatus()
                                            == ApproveConstant.flowPathStatus.PASSED.getCode()).map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toList()));
                        }
                    }
                    if (!CollectionUtils.isEmpty(uuidList)) {
                        title = flowPathDAO.getShowName() + " 撤销了 " + flowTitle;
//                        for (String uuidPass : uuidList) {
//                            //进行通知回调
//                            callbackExtend.callback(
//                                    flowPathDAO.getApproverUrl(),
//                                    callbackExtend.createNotificationData(
//                                            flowPathDAO.getFlowId(),
//                                            id,
//                                            uuidPass,
//                                            "approve",
//                                            "revoke",
//                                            flowPathDAO.getTempShowData(),
//                                            title,
//                                            "",
//                                            flowPathDAO.getTempData(),
//                                            JSONUtil.toJsonStr(stepInfoDTOList),
//                                            flowPathDAO.getExtraContent(),
//                                            flowPathDAO.getStatus()
//                                    )
//                            );
//                        }
                        Map<String, Object> callbackRequestMap = new HashMap<>();
                        callbackRequestMap.put("flowId", flowPathDAO.getFlowId());
                        callbackRequestMap.put("flowPathId", flowPathDAO.getId());
                        callbackRequestMap.put("userIdList", uuidList);
                        callbackRequestMap.put("action", "approve");
                        callbackRequestMap.put("event", "wait");
                        callbackRequestMap.put("title", title);
                        callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackRequestMap);   //发起通知
                    }
                }
                //撤回实例操作完成
            }
            else if (action == ApproveConstant.flowPathAction.BACK.getCode()) {
                //权限验证//当前uuid必须在当前stepInfo的uuid中
                int canGoBack = 0;
                for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                    if (notifyOne.getUuid().equals(uuid)) {
                        canGoBack = 1;
                        break;
                    }
                }
                if (canGoBack == 0) {
                    throw new BsException("对不起，当前用户权限不符合当前规则");
                }
                //退回上一步操作
                if (lastStepInfo != null) {
                    //如果有上一步，退回到上一步，状态不变
                    nextStepInfo = thisStepInfo;
                    thisStepInfo = lastStepInfo;
                    //历史
                    title = StringUtil.isEmpty(title) ? (showName + " 退回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).nextStepId(nextStepInfo.getId()).action(action).reason(reason).build();
                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
                    //提取uuid
                    ArrayList<String> lastNotificationUuidList = new ArrayList<>();
                    List<FlowPathStepNotificationDTO> lastStepInfoNotificationDTOList = lastStepInfo.getNotificationList();
                    //标记待审
                    apprNotification = nextStepInfo;
                    for (FlowPathStepNotificationDTO notifyOne : lastStepInfoNotificationDTOList) {
                        lastNotificationUuidList.add(notifyOne.getUuid());
                    }
                    ArrayList<String> notificationUuidList = new ArrayList<>();
                    //提取uuid
                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                        if (!lastNotificationUuidList.contains(notifyOne.getUuid())) {
                            notificationUuidList.add(notifyOne.getUuid());
                        }
                    }
                    if (!notificationUuidList.isEmpty()) {
                        //删除当前 待我 处理的审批
                        flowPathListMapper.delete(GetFlowPathListDAOListCondition.builder().flowPathId(id).uuidList(notificationUuidList).typeList(new ArrayList<Integer>() {{
                            add(2);
                            add(3);
                        }}).build());
                    }
                    //上一步 已 处理的审批，变更为待处理
                    flowPathListMapper.update(
                            FlowPathListDAO.builder().type(3).build(),
                            GetFlowPathListDAOListCondition.builder().flowPathId(id).uuidList(lastNotificationUuidList).type(2).build()
                    );
                    //更新上一步flow_path_step表的状态为待审核
                    flowPathStepService.update(
                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.WAITING.getCode()).updateTime(new Date()).build(),
                            GetFlowPathStepCondition.builder().flowPathId(lastStepInfo.getFlowPathId()).stepNo(lastStepInfo.getStepNo()).build()
                    );
                    //更新上一步flow_path_step_notification表的状态为待审核
                    flowPathStepNotificationService.update(
                            FlowPathStepNotificationDAO.builder()
                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                                    .updateTime(new Date())
                                    .build(),
                            GetFlowPathStepNotificationCondition.builder()
                                    .flowPathId(lastStepInfo.getFlowPathId())
                                    .stepNo(lastStepInfo.getStepNo())
                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                                    .build()
                    );
                    //更新此步骤flow_path_step_notification表的状态为待审核
                    flowPathStepNotificationService.update(
                            FlowPathStepNotificationDAO.builder()
                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                                    .updateTime(new Date())
                                    .build(),
                            GetFlowPathStepNotificationCondition.builder()
                                    .flowPathId(thisStepInfo.getFlowPathId())
                                    .stepNo(thisStepInfo.getStepNo())
                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                                    .build()
                    );
                } else {
                    //如果没有上一步，视为撤回，状态为5
                    //更新状态
                    flowPathMapper.updateFlowPath(FlowPathDAO.builder().id(id).status(ApproveConstant.flowPathStatus.BACK.getCode()).updateTime(new Date()).build());
                    //历史
                    title = StringUtil.isEmpty(title) ? (showName + " 退回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId())//已经流转过的步骤id//0表示创建
                            .nextStepId(0)//正在进行的/下一个流转步骤id
                            .action(action).reason(reason).build();
                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
                    //更新 待我 处理的审批到 已处理//当前实例一起更新
                    flowPathListMapper.update(FlowPathListDAO.builder().type(3).build(), GetFlowPathListDAOListCondition.builder().flowPathId(id).type(2).build());
                    stepCompleted = 1;
                    //退回实例操作完成
                    //更新此步骤flow_path_step_notification表的状态为待审核
                    flowPathStepNotificationService.update(
                            FlowPathStepNotificationDAO.builder()
                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                                    .updateTime(new Date())
                                    .build(),
                            GetFlowPathStepNotificationCondition.builder()
                                    .flowPathId(thisStepInfo.getFlowPathId())
                                    .stepNo(thisStepInfo.getStepNo())
                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                                    .build()
                    );
                }
            }
        }

        /*
         *  如果不是发起操作 ,该对象为下一节点
         */
        if (apprNotification != null) {

            /*
             * 查询剩下的待审节点
             */
            List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(
                    GetFlowPathStepCondition.builder().
                            flowPathId(id).
                            status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .build());

            Iterator<FlowPathStepDTO> iterator = newStepInfoDTOList.iterator(); //使用迭代器.来判断是否时最后的节点

            while (iterator.hasNext()) {
                apprNotification = iterator.next();

                List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
                if (Objects.nonNull(notificationList) && !notificationList.isEmpty()) {  //审批节点不为空
                    break; //跳出循环
                }

                List<FlowPathStepNotificationDTO> ccNotificationDTOList = apprNotification.getCcNotificationList(); //抄送数据
                if (Objects.isNull(ccNotificationDTOList) || ccNotificationDTOList.isEmpty()) {
                    continue;
                }

                FlowPathStepDAO flowPathStepDAO = new FlowPathStepDAO();
                flowPathStepDAO.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
                GetFlowPathStepCondition condition = new GetFlowPathStepCondition();
                condition.setId(apprNotification.getId());
                flowPathStepService.update(flowPathStepDAO, condition);

                ccNotificationList.addAll(ccNotificationDTOList);
            }
            List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
            if (!iterator.hasNext() && (Objects.isNull(notificationList) || notificationList.isEmpty())) {    //判断是否还有节点,如果没有,说明最后的节点是抄送节点,已经被上边的循环消化掉了 || 判断最后一个节点,是否是审批节点,如果是
                flowPathMapper.updateFlowPath(
                        FlowPathDAO.builder()
                                .id(id)
                                .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                                .updateTime(new Date())
                                .build()
                );
                //通过回调前，通知回调
                callbackExtend.callback(
                        flowPathDAO.getApproverUrl(),
                        callbackExtend.createNotificationData(
                                flowPathDAO.getFlowId(),
                                id,
                                flowPathDAO.getUuid(),
                                "approve",
                                "pass",
                                flowPathDAO.getTempShowData(),
                                title,
                                "",
                                flowPathDAO.getTempData(),
                                JSONUtil.toJsonStr(stepInfoDTOList),
                                flowPathDAO.getExtraContent(),
                                flowPathDAO.getStatus()
                        )
                );

                //通过回调
                callbackExtend.callback(
                        flowPathDAO.getPassUrl(),
                        callbackExtend.createNotificationData(
                                flowPathDAO.getFlowId(),
                                id,
                                flowPathDAO.getUuid(),
                                "approve",
                                "pass",
                                flowPathDAO.getTempShowData(),
                                title,
                                "",
                                flowPathDAO.getTempData(),
                                JSONUtil.toJsonStr(stepInfoDTOList),
                                flowPathDAO.getExtraContent(),
                                ApproveConstant.flowPathAction.PASS.getCode()
                                // flowPathDAO.getStatus()
                        )
                );
            }

            List<FlowPathStepNotificationDTO> newNotificationList = apprNotification.getNotificationList();
            if (Objects.nonNull(newNotificationList) && !newNotificationList.isEmpty()) {
                //待审
                if (apprNotification.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
                    //依次审批只通知下一个待审
                    int isNextPerson = 0;
                    for (FlowPathStepNotificationDTO notifyOne : newNotificationList) {
                        if (isNextPerson > 0 || action == 0 || thisStepCompleted > 0) {
                            //存储待我处理的审批
                            title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
                            title = configTitle(title, flowPathDAO); //合并代码
                            approvalUuidList.add(notifyOne.getUuid());
                            flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(id).uuid(notifyOne.getUuid()).type(2).appChannelId(flowPathDAO.getAppChannelId()).dataChannelId(flowPathDAO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
                            Map<String, Object> notificationData = new HashMap<>();
                            notificationData.put("action", "approve");
                            notificationData.put("event", "wait");
                            notificationData.put("title", title);
                            notificationData.put("uuid", notifyOne.getUuid());
                            notificationData.put("flowPathId", id);
                            notificationData.put("flowId", flowPathDAO.getFlowId());
                            notificationData.put("tempShowData", flowPathDAO.getTempShowData());
                            notificationData.put("extraContent", JSONUtil.toJsonStr(notifyOne));
                            callbackExtend.callback(flowPathDAO.getApproverUrl(), notificationData);
                            break;
                        }
                        if (notifyOne.getUuid().equals(uuid)) {
                            isNextPerson = 1;
                        }
                    }
                } else {
                    //其他审批通知所有待审
//                    for (FlowPathStepNotificationDTO notifyOne : apprNotification.getNotificationList()) {
//                        //存储待我处理的审批
//                        title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
//                        flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(id).uuid(notifyOne.getUuid()).type(2).appChannelId(flowPathDAO.getAppChannelId()).dataChannelId(flowPathDAO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
//                        approvalUuidList.add(notifyOne.getUuid());
//                        callbackExtend.callback(flowPathDAO.getApproverUrl(),
//                                callbackExtend.createNotificationData(
//                                        flowPathDAO.getFlowId(),
//                                        id,
//                                        notifyOne.getUuid(),
//                                        "approve",
//                                        "wait",
//                                        flowPathDAO.getTempShowData(),
//                                        title, JSONUtil.toJsonStr(notifyOne),
//                                        "", JSONUtil.toJsonStr(stepInfoDTOList), flowPathDAO.getExtraContent(), flowPathDAO.getStatus()));
//                    }

                    List<String> userIdList = new ArrayList<>();
                    title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
                    title = configTitle(title, flowPathDAO);
                    for (FlowPathStepNotificationDTO notifyOne : apprNotification.getNotificationList()) {
                        String userId = notifyOne.getUuid();
                        //存储待我处理的审批
                        flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(id).uuid(userId).type(2).appChannelId(flowPathDAO.getAppChannelId()).dataChannelId(flowPathDAO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
                        approvalUuidList.add(userId);
                        userIdList.add(userId);
                    }
                    Map<String, Object> callbackRequestMap = new HashMap<>();
                    callbackRequestMap.put("flowId", flowPathDAO.getFlowId());
                    callbackRequestMap.put("flowPathId", id);
                    callbackRequestMap.put("userIdList", userIdList);
                    callbackRequestMap.put("action", "approve");
                    callbackRequestMap.put("event", "wait");
                    callbackRequestMap.put("title", title);
                    callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackRequestMap);
                }
            }
        }

        //抄送
        if (thisStepCompleted > 0 && !ccNotificationList.isEmpty()) {
            List<String> userIdList = new ArrayList<>();
            title = flowPathDAO.getShowName() + "抄送了" + configTitle(flowTitle, flowPathDAO) + "申请给你"; //合并代码
            //抄送通知
            for (FlowPathStepNotificationDTO notifyOne : ccNotificationList) {
                String userId = notifyOne.getUuid();
                userIdList.add(userId);
                flowPathListMapper.addFlowPathList(
                        FlowPathListDAO.builder()
                                .title(title)
                                .flowPathId(id)
                                .uuid(userId)
                                .type(ApproveConstant.flowPathListType.CC.getCode())
                                .appChannelId(flowPathDAO.getAppChannelId())
                                .dataChannelId(flowPathDAO.getDataChannelId())
                                .extraContent(JSONUtil.toJsonStr(notifyOne))
                                .build());
//                callbackExtend.callback(
//                        flowPathDAO.getApproverUrl(),
//                        callbackExtend.createNotificationData(
//                                flowPathDAO.getFlowId(),
//                                id,
//                                userId,
//                                "approve",
//                                "normal",
//                                flowPathDAO.getTempShowData(),
//                                title,
//                                JSONUtil.toJsonStr(notifyOne),
//                                "",
//                                JSONUtil.toJsonStr(stepInfoDTOList),
//                                flowPathDAO.getExtraContent(),
//                                flowPathDAO.getStatus()
//                        )
//                );
            }
            Map<String, Object> callbackRequestMap = new HashMap<>();
            callbackRequestMap.put("flowId", flowPathDAO.getFlowId());
            callbackRequestMap.put("flowPathId", id);
            callbackRequestMap.put("userIdList", userIdList);
            callbackRequestMap.put("action", "approve");
            callbackRequestMap.put("event", "normal");
            callbackRequestMap.put("title", title);
            callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackRequestMap);
        }

        //抄送本人
        if (stepCompleted > 0 && flowPathDAO.getCcCreate() > 0) {
            //存储抄送我的审批
            //title = "你的" + flowTitle + "申请";
            title = "你的" + configTitle(flowTitle, flowPathDAO) + "申请"; //合并代码
            flowPathListMapper.addFlowPathList(
                    FlowPathListDAO.builder()
                            .title(title)
                            .flowPathId(id)
                            .uuid(flowPathDAO.getUuid())
                            .type(ApproveConstant.flowPathListType.CC.getCode())
                            .appChannelId(flowPathDAO.getAppChannelId())
                            .dataChannelId(flowPathDAO.getDataChannelId())
                            .extraContent(flowPathExtraContent)
                            .build()
            );
            callbackExtend.callback(
                    flowPathDAO.getApproverUrl(),
                    callbackExtend.createNotificationData(
                            flowPathDAO.getFlowId(),
                            id,
                            flowPathDAO.getUuid(),
                            "approve",
                            "normal",
                            flowPathDAO.getTempShowData(),
                            title,
                            flowPathExtraContent,
                            "",
                            JSONUtil.toJsonStr(stepInfoDTOList),
                            flowPathDAO.getExtraContent(),
                            flowPathDAO.getStatus()
                    )
            );
        }
        //异步尝试自动通过
//        this.autoApproval(id, showName, uuid, reason, appChannelId, dataChannelId,
//                title, flowTitle, passStepUuidMap, approvalUuidList, flowPathHistoryDAO, flowPathDAO);


        //尝试自动通过
        //自动审批加入当前审核人
        // approvalUuidList.add(uuid);
        if (!approvalUuidList.isEmpty() && flowPathHistoryDAO != null &&
                (flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.CREATE.getCode() || flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.PASS.getCode())) {


            /*
             * 匹配uuid和人名字 处理自动通过名字不对的问题
             */
            Map<String, String> showNameByUuidMap = new HashMap<>();
            for (FlowPathStepDTO flowPathStepDTO : stepInfoDTOList) {
                List<FlowPathStepNotificationDTO> notification = flowPathStepDTO.getNotification();
                if (Objects.isNull(notification) || notification.isEmpty()) {
                    continue;
                }
                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : notification) {
                    showNameByUuidMap.put(flowPathStepNotificationDTO.getUuid(), flowPathStepNotificationDTO.getShowName());
                }
            }

            //相同ID自动通过:[1:流转过的相同节点自动通过;2:连续相同节点自动通过;3:不自动]
            if (flowPathDAO.getAutoappr() == 2) {
                //连续id自动通过
                for (String apprUuid : approvalUuidList) {
                    if (apprUuid.equals(uuid)) {
                        String name = showNameByUuidMap.get(uuid);
                        if (StringUtil.isNotEmpty(name)) {
                            showName = name;
                        }
                        title = showName + " 自动通过了 " + flowTitle;
                        goFlow(id, 2, uuid, showName, reason, appChannelId, dataChannelId, title);
                        break;
                    }
                }
            } else if (flowPathDAO.getAutoappr() == 1) {
                Set<String> passUuidSet = new HashSet<>();//已通过的uuid
                for (Map.Entry<Integer, List<String>> entry : passStepUuidMap.entrySet()) {
                    passUuidSet.addAll(new HashSet<>(entry.getValue()));
                }
                passUuidSet.add(flowPathDAO.getUuid());//创建人也当成已通过
                for (String apprUuid : approvalUuidList) {
                    if (passUuidSet.contains(apprUuid)) {
                        String name = showNameByUuidMap.get(uuid);
                        if (StringUtil.isNotEmpty(name)) {
                            showName = name;
                        }
                        title = showName + " 自动通过了 " + flowPathDAO.getShowName() + "的" + flowTitle;
                        goFlow(id, 2, apprUuid, "", reason, appChannelId, dataChannelId, title);
                        break;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 生成配置标题
     *
     * @param originalTitle
     * @param flowPathDAO
     * @return
     */
    private String configTitle(String originalTitle, FlowPathDAO flowPathDAO) {
        if (Objects.isNull(flowPathDAO) || StringUtil.isEmpty(flowPathDAO.getTempShowData())) {
            return originalTitle;
        }

        //合并代码
        FlowEntity flowDAO = flowMapper.selectById(flowPathDAO.getFlowId());
        if (Objects.isNull(flowDAO)) {
            return originalTitle;
        }

        TemplateEntity templateEntity = templateMapper.selectById(flowDAO.getTempId());
        if (Objects.isNull(templateEntity) || StringUtil.isEmpty(templateEntity.getTitleConfig())) {
            return originalTitle;
        }

        List<Map<String, Object>> titleConfig = StringUtil.jsonDecode(templateEntity.getTitleConfig(), List.class);
        //String title = TitleConfigUtil.getTitleStr(new JSONArray(tempShowData),titleConfig);


        //合并代码
        Map<String, String> tempValueMap = TitleConfigUtil.getTempShowDataMap(new JSONArray(flowPathDAO.getTempShowData()));
        log.info("configTitle ->tempValueMap:{}", tempValueMap);
        StringBuilder title = new StringBuilder();
        for (Map<String, Object> config : titleConfig) {
            if (Objects.equals(config.get("type"), "2")) {
                String value = tempValueMap.getOrDefault(config.getOrDefault("val", ""), "");
                title.append(value);
            } else if (Objects.equals(config.get("type"), "3")) {
                title.append(config.getOrDefault("val", ""));
            } else if (Objects.equals(config.get("type"), "1")) {
                String val = String.valueOf(config.getOrDefault("val", ""));
                if (val.equals("1")) {
                    title.append(flowPathDAO.getShowName());
                } else if (val.equals("2")) {
                    String createData = DateUtil.formatDate(flowPathDAO.getCreateTime());
                    title.append(createData);
                } else if (val.equals("3")) {
                    String createTime = DateUtil.formatTime(flowPathDAO.getCreateTime());
                    title.append(createTime);
                } else if (val.equals("4")) {
                    if (StringUtil.isNotEmpty(flowPathDAO.getExtraContent())) {
                        JSONObject extraContent = JSONUtil.parseObj(flowPathDAO.getExtraContent());
                        title.append(extraContent.getStr("companyName", ""));
                    }
                } else if (val.equals("5")) {
                    title.append(flowPathDAO.getTitle());
                }
            }
        }

        if (StringUtil.isNotEmpty(title.toString())) {
            return title.toString();
        }


        return originalTitle;
    }

    public void autoApproval(int id, String showName, String uuid, String reason, String appChannelId, String dataChannelId, String title, String flowTitle,
                             Map<Integer, List<String>> passStepUuidMap, List<String> approvalUuidList,
                             FlowPathHistoryDAO flowPathHistoryDAO, FlowPathDAO flowPathDAO) {

        //自动审批加入当前审核人
        //approvalUuidList.add(uuid);

        CompletableFuture.supplyAsync(() -> {
            if (!approvalUuidList.isEmpty() && flowPathHistoryDAO != null &&
                    (flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.CREATE.getCode() || flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.PASS.getCode())) {
                //相同ID自动通过:[1:流转过的相同节点自动通过;2:连续相同节点自动通过;3:不自动]
                if (flowPathDAO.getAutoappr() == 2) {
                    //连续id自动通过
                    for (String apprUuid : approvalUuidList) {
                        if (apprUuid.equals(uuid)) {
                            String titleFile = showName + " 自动通过了 " + flowTitle;
                            goFlowService.goFlowV2(id, 2, uuid, showName, reason, appChannelId, dataChannelId, titleFile, false);
                            break;
                        }
                    }
                } else if (flowPathDAO.getAutoappr() == 1) {
                    Set<String> passUuidSet = new HashSet<>();//已通过的uuid
                    for (Map.Entry<Integer, List<String>> entry : passStepUuidMap.entrySet()) {
                        passUuidSet.addAll(new HashSet<>(entry.getValue()));
                    }
                    passUuidSet.add(flowPathDAO.getUuid());//创建人也当成已通过
                    for (String apprUuid : approvalUuidList) {
                        if (passUuidSet.contains(apprUuid)) {
                            String titleFile = showName + " 自动通过了 " + flowPathDAO.getShowName() + "的" + flowTitle;
                            goFlowService.goFlowV2(id, 2, apprUuid, "", reason, appChannelId, dataChannelId, titleFile, false);
                            break;
                        }
                    }
                }
            }

            return null;
        });
    }


    /**
     * 恢复流程，并发送回调
     *
     * @param flowPathId
     */
    private void proceedApproval(Integer flowPathId) {
        // 1、更新撤回目标流程的暂停状态为 恢复
        flowPathMapper.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathId)
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
                        .build()
        );
        // 2、获取审批流程数据进行回调
        // 获取该 撤回目标流程
        FlowPathDAO withdrawFlowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
        // 获取 撤回目标流程 步骤
        List<FlowPathStepDTO> withdrawStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(flowPathId)
                .build());

        // 3、回调业务层-将原审批恢复
        callbackExtend.callback(
                withdrawFlowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        withdrawFlowPathDAO.getFlowId(),
                        withdrawFlowPathDAO.getId(),
                        withdrawFlowPathDAO.getUuid(),
                        "approve",
                        "proceed",
                        withdrawFlowPathDAO.getTempShowData(),
                        withdrawFlowPathDAO.getTitle() + "已恢复",
                        JSONUtil.toJsonStr(withdrawFlowPathDAO.getExtraContent()),
                        withdrawFlowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
                        withdrawFlowPathDAO.getExtraContent(),
                        withdrawFlowPathDAO.getStatus()
                )
        );
    }

    /**
     * 正常撤回，直接撤回成功
     *
     * @param flowPathId
     */
    private void withdrawnPermit(int flowPathId) {
        // 1、获取 流程信息和步骤信息
        // 获取该 撤回目标流程
        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
        // 获取 撤回目标流程 步骤
        List<FlowPathStepDTO> withdrawStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(flowPathId)
                .build());

        // 2、判断该撤回申请是否有关联 撤销目标审批流程 ，如果有，需要把目标流程状态设置为 进行中
        if (flowPathDAO.getWithdrawPathId() != 0) {
            proceedApproval(flowPathDAO.getWithdrawPathId());
        }

        // 3、获取当前审批步骤
        Optional<FlowPathStepDTO> flowPathStepDTO = withdrawStepInfoDTOList.stream().filter(withdrawStepInfoDTO -> withdrawStepInfoDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
        if (!flowPathStepDTO.isPresent()) {
            throw new BsException("撤销目标流程失败");
        }
        FlowPathStepDTO withdrawFlowPathStepDTO = flowPathStepDTO.get();

        // 4、更新流程状态
        flowPathMapper.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathId)
                        .updateTime(new Date())
                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
                        .build()
        );

        // 5、更新flow_path_step表
        flowPathStepService.update(
                FlowPathStepDAO.builder()
                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
                        .build(),
                GetFlowPathStepCondition.builder()
                        .flowPathId(flowPathId)
                        .stepNo(withdrawFlowPathStepDTO.getStepNo())
                        .build()
        );

        // 6、添加历史数据（意义不大）
        String title = StringUtil.isEmpty(flowPathDAO.getTitle()) ? (flowPathDAO.getFlowTitle() + "已撤回") : flowPathDAO.getTitle();
        FlowPathHistoryDAO flowPathHistoryDAO = FlowPathHistoryDAO.builder()
                .title(title)
                .flowPathId(flowPathId)
                .uuid(flowPathDAO.getUuid())
                .stepId(withdrawFlowPathStepDTO.getId())
                .nextStepId(0)
                .action(ApproveConstant.flowPathAction.WITHDRAW.getCode())
                .reason("")
                .build();
        flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);

        // 7、更新 待我 处理的审批到 已处理
        // 当前实例一起更新
        flowPathListMapper.update(
                FlowPathListDAO.builder()
                        .type(ApproveConstant.flowPathListType.PROCESSED.getCode())
                        .build(),
                GetFlowPathListDAOListCondition.builder()
                        .flowPathId(flowPathId)
                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
                        .build()
        );

        // 8、更新flow_path_step_notification表
//        flowPathStepNotificationService.update(
//                FlowPathStepNotificationDAO.builder()
//                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
//                        .updateTime(new Date())
//                        .build(),
//                GetFlowPathStepNotificationCondition.builder()
//                        .flowPathId(withdrawFlowPathStepDTO.getFlowPathId())
//                        .stepNo(withdrawFlowPathStepDTO.getStepNo())
//                        .uuid(flowPathDAO.getUuid())
//                        .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                        .build()
//        );

        /*
         * 通知回调
         */
        callbackExtend.callback(
                flowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDAO.getFlowId(),
                        flowPathId,
                        flowPathDAO.getUuid(),
                        "approve",
                        "revocation",
                        flowPathDAO.getTempShowData(),
                        title,
                        "",
                        flowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
                        flowPathDAO.getExtraContent(),
                        flowPathDAO.getStatus()
                )
        );

        /*
         * 拒绝地址回调
         */
        Map<String, Object> notificationData = callbackExtend.createNotificationData(
                flowPathDAO.getFlowId(),
                flowPathId,
                flowPathDAO.getUuid(),
                "approve",
                "revocation",
                flowPathDAO.getTempShowData(),
                title,
                "",
                flowPathDAO.getTempData(),
                JSONUtil.toJsonStr(withdrawStepInfoDTOList),
                flowPathDAO.getExtraContent(),
                ApproveConstant.flowPathStatus.WITHDRAWN.getCode()
        );
        callbackExtend.callback(flowPathDAO.getRefuseUrl(), notificationData);

        /*
         * 撤回地址回调
         */
//        callbackExtend.callback(
//                callbackExtend.createNotificationData(
//                        flowPathDAO.getFlowId(),
//                        flowPathId,
//                        flowPathDAO.getUuid(),
//                        "approve",
//                        "revocation",
//                        flowPathDAO.getTempShowData(),
//                        title,
//                        "",
//                        flowPathDAO.getTempData(),
//                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
//                        flowPathDAO.getExtraContent(),
//                        flowPathDAO.getStatus()
//                )
//        );

    }

    /**
     * 撤回，需要已审核人员确认
     *
     * @param flowPathDAO  审批信息
     * @param passStepList 包含已通过的人员节点
     */
    private void withdrawnConfirmation(FlowPathDAO flowPathDAO, List<FlowPathStepDTO> passStepList) {
        // 首先验证是否已存在 该条流程的 撤销确认申请
        List<FlowPathDAO> flowPathList = flowPathMapper.getFlowPathList(
                GetFlowPathCondition
                        .builder()
                        .withdrawPathId(flowPathDAO.getId())
                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                        .build(),
                0,
                0
        );
        if (!flowPathList.isEmpty()) {
            return;
        }

        // 1、循环已经通过的步骤
//        for (FlowPathStepDTO stepInfo : passStepNotNullList) {
//            // 2、剔除步骤内未审核通过的人
//            List<FlowPathStepNotificationDTO> notificationList = stepInfo.getNotification().stream()
//                    .filter(flowPathStepNotificationDTO ->
//                            flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode() ||
//                                    flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.RETURN_BACL.getCode()
//                    ).collect(Collectors.toList());
//            stepInfo.setNotification(notificationList);
//        }
        for (FlowPathStepDTO stepInfo : passStepList) {
            // 2、剔除步骤内未审核通过的人
            List<FlowPathStepNotificationDTO> notifications = stepInfo.getNotification();
            // 判空
            if (CollectionUtils.isEmpty(notifications)) {
                continue;
            }
            // 过滤状态并设置回去
            List<FlowPathStepNotificationDTO> notificationList = notifications.stream()
                    .filter(notification -> {
                        // 对 notification判空
                        if (notification == null) {
                            return false;
                        }
                        int status = notification.getStatus();
                        //  status 的判空
                        return status == ApproveConstant.flowPathStatus.PASSED.getCode() ||
                                status == ApproveConstant.flowPathStatus.RETURN_BACL.getCode();
                    })
                    .collect(Collectors.toList());
            stepInfo.setNotification(notificationList);
        }

        // 3、将当前审批设置为 暂停状态
        interruptApproval(flowPathDAO.getId());

        // 4、生成新的 DAO
        flowPathDAO.setTitle(flowPathDAO.getTitle() + "的撤销");
        flowPathDAO.setStatus(ApproveConstant.flowPathStatus.WAITING.getCode());
        flowPathDAO.setCreateTime(null);
        flowPathDAO.setUpdateTime(null);
        flowPathDAO.setStepInfo(JSONUtil.toJsonStr(passStepList));
        flowPathDAO.setWithdrawPathId(flowPathDAO.getId());

        // 5、保存新的审批流程
        if (flowPathMapper.addFlowPath(flowPathDAO) <= 0) {
            throw new BsException("创建审批流失败");
        }

        // 6、存储flow_path_step表
//        List<FlowPathStepDTO> newStepInfoDTOList = new ArrayList<>();
        for (FlowPathStepDTO flowPathStepDTO : passStepList) {
//            List<StepInfoNotificationDTO> stepNotificationList = flowPathStepDTO.getNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
//            List<StepInfoNotificationDTO> stepCcNotificationList = flowPathStepDTO.getCcNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
            // 如果没有通知人，则使用空列表
            List<StepInfoNotificationDTO> stepNotificationList = Optional.ofNullable(flowPathStepDTO.getNotification())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(StepInfoNotificationDTO::create)
                    .collect(Collectors.toList());
            // 如果没有抄送人，则使用空列表
            List<StepInfoNotificationDTO> stepCcNotificationList = Optional.ofNullable(flowPathStepDTO.getCcNotification())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(StepInfoNotificationDTO::create)
                    .collect(Collectors.toList());

            flowPathStepService.addFlowPathStep(
                    flowPathDAO.getId(),
                    flowPathStepDTO.getStepNo(),
                    flowPathStepDTO.getTitle(),
                    flowPathStepDTO.getPassCons(),
                    stepNotificationList,
                    stepCcNotificationList,
                    flowPathStepDTO.getPassAll(),
                    flowPathStepDTO.getWithdraw(),
                    flowPathStepDTO.getGoback(),
                    ApproveConstant.flowPathStatus.WAITING.getCode(),
                    ApproveConstant.appiont.NO.getCode(),
                    ApproveConstant.returnBack.NO.getCode(),
                    flowPathStepDTO.getExtraContent(),
                    0,
                    flowPathStepDTO.getPreId(),
                    flowPathStepDTO.getStepType()
            );
//            newStepInfoDTOList.add(FlowPathStepDTO.create(flowPathStepDAO));
        }

        // 7、获取stepInfoList
        List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(flowPathDAO.getId())
                .build());

        // 8、存储发起人到 flowPathList
        // 存储操作历史
        flowPathHistoryMapper.addFlowPathHistory(
                FlowPathHistoryDAO
                        .builder()
                        .title(flowPathDAO.getTitle())
                        .flowPathId(flowPathDAO.getId())
                        .uuid(flowPathDAO.getUuid())
                        .stepId(0)
                        .nextStepId(newStepInfoDTOList.get(0).getId())
                        .action(ApproveConstant.flowPathAction.CREATE.getCode())
                        .reason("")
                        .build()
        );
        // 存储到 "我的" 审批列表
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO
                        .builder()
                        .title(flowPathDAO.getTitle())
                        .flowPathId(flowPathDAO.getId())
                        .uuid(flowPathDAO.getUuid())
                        .type(ApproveConstant.flowPathListType.MY.getCode())
                        .appChannelId(flowPathDAO.getAppChannelId())
                        .dataChannelId(flowPathDAO.getDataChannelId())
                        .extraContent(flowPathDAO.getExtraContent())
                        .build()
        );

        // 9、获取当前待审核步骤
        Optional<FlowPathStepDTO> firstWaitingStepInfo = newStepInfoDTOList.stream().filter(newStepInfoDTO -> newStepInfoDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
        if (!firstWaitingStepInfo.isPresent()) {
            throw new BsException("创建审批流失败");
        }
        FlowPathStepDTO flowPathStepDTO = firstWaitingStepInfo.get();

        // 10、依据当前步骤规则设置待审批人员
        if (flowPathStepDTO.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
            Optional<FlowPathStepNotificationDTO> firstNotificationInfo = flowPathStepDTO.getNotification().stream().filter(notification -> notification.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
            if (!firstNotificationInfo.isPresent()) {
                throw new BsException("创建审批流失败");
            }
            FlowPathStepNotificationDTO flowPathStepNotificationDTO = firstNotificationInfo.get();

            setWaitApprove(flowPathDAO, newStepInfoDTOList, flowPathStepNotificationDTO);
        } else {
            for (FlowPathStepNotificationDTO notificationInfo : flowPathStepDTO.getNotification()) {
                setWaitApprove(flowPathDAO, newStepInfoDTOList, notificationInfo);
            }
        }
    }

    /**
     * 设置待审人，并发送通知
     *
     * @param newFlowPathDAO
     * @param newStepInfoDTOList
     * @param flowPathStepNotificationDTO
     */
    private void setWaitApprove(FlowPathDAO newFlowPathDAO, List<FlowPathStepDTO> newStepInfoDTOList, FlowPathStepNotificationDTO flowPathStepNotificationDTO) {
        // 1、未审批人添加待审
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO.builder()
                        .title(newFlowPathDAO.getShowName() + "的" + newFlowPathDAO.getTitle() + "申请")
                        .flowPathId(newFlowPathDAO.getId())
                        .uuid(flowPathStepNotificationDTO.getUuid())
                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
                        .appChannelId(newFlowPathDAO.getAppChannelId())
                        .dataChannelId(newFlowPathDAO.getDataChannelId())
                        .extraContent(JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()))
                        .build()
        );

        // 2、回调业务层-新审批开始流转
        callbackExtend.callback(
                newFlowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        newFlowPathDAO.getFlowId(),
                        newFlowPathDAO.getId(),
                        flowPathStepNotificationDTO.getUuid(),
                        "approve",
                        "normal",
                        newFlowPathDAO.getTempShowData(),
                        newFlowPathDAO.getTitle(),
                        JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()),
                        newFlowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(newStepInfoDTOList),
                        newFlowPathDAO.getExtraContent(),
                        newFlowPathDAO.getStatus()
                )
        );
    }

    /**
     * 暂停流程，并发送回调
     *
     * @param flowPathId
     */
    private void interruptApproval(Integer flowPathId) {
        // 1、更新现有审批流程状态为暂停
        flowPathMapper.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathId)
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.YES.getCode())
                        .build()
        );

        // 2、获取审批流程数据进行回调
        // 获取该 撤回目标流程
        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
        // 获取 撤回目标流程 步骤
        List<FlowPathStepDTO> stepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(flowPathId)
                .build());

        // 3、回调业务层-将原审批暂停
        callbackExtend.callback(
                flowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDAO.getFlowId(),
                        flowPathDAO.getId(),
                        flowPathDAO.getUuid(),
                        "approve",
                        "interrupt",
                        flowPathDAO.getTempShowData(),
                        flowPathDAO.getTitle() + "已暂停",
                        JSONUtil.toJsonStr(flowPathDAO.getExtraContent()),
                        flowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(stepInfoDTOList),
                        flowPathDAO.getExtraContent(),
                        flowPathDAO.getStatus()
                )
        );
    }

    /**
     * 节点强制变动
     *
     * @param flowPathStepDTO
     */
    @Override
    public void flowPathStep(FlowPathStepDTO flowPathStepDTO) {
        Integer flowPathId = flowPathStepDTO.getFlowPathId();
        FlowPathDTO flowPathDTO = flowPathService.getFlowPathDetail(flowPathId);    //获取审批详情
        if (flowPathDTO.getId() == null) {
            throw new BsException("审批流程不存在");
        }
        if (flowPathDTO.getStatus() != ApproveConstant.flowPathStatus.WAITING.getCode()) {//流程状态
            throw new BsException("审批流程已结束");
        }

        /*
         * 更新审批节点
         */
        FlowPathStepDAO flowPathStepDAO = FlowPathStepDAO.create(flowPathStepDTO);
        flowPathStepDAO.setFlowPathId(null);
        flowPathStepService.update(flowPathStepDAO, GetFlowPathStepCondition.builder().id(flowPathStepDAO.getId()).build());    //更新节点状态

        Integer status = flowPathStepDAO.getStatus();   //节点状态
        if (Objects.isNull(status)) {
            return;
        }

        List<FlowPathStepDTO> stepInfoList = flowPathDTO.getStepInfoList();

        if (status.equals(ApproveConstant.flowPathStatus.REFUSED.getCode())) {  //驳回  通知发起人

            /*
             * 更新审批流状态
             */
            FlowPathDAO flowPathDAO = new FlowPathDAO();
            flowPathDAO.setId(flowPathId);
            flowPathDAO.setStatus(ApproveConstant.flowPathStatus.REFUSED.getCode());
            flowPathService.updateFlowPath(flowPathDAO);

            flowPathListMapper.deleteByFlowPathIdAndType(RemoveFlowPathListCondition.builder().flowPathId(flowPathId).type(ApproveConstant.flowPathListType.WAIT.getCode()).build());

            /*
             * 发起通知回调 通知发起人
             */
            //拒绝回调前，先进行通知回调
            callbackExtend.callback(
                    flowPathDTO.getApproverUrl(),
                    callbackExtend.createNotificationData(
                            flowPathDTO.getFlowId(),
                            flowPathDTO.getId(),
                            flowPathDTO.getUuid(),
                            "approve",
                            "refuse",
                            JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                            "系统驳回",
                            "",
                            JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                            JSONUtil.toJsonStr(stepInfoList),
                            JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                            flowPathDTO.getStatus()
                    )
            );

            //拒绝回调
            callbackExtend.callback(flowPathDTO.getRefuseUrl(),
                    callbackExtend.createNotificationData(
                            flowPathDTO.getFlowId(),
                            flowPathDTO.getId(),
                            flowPathDTO.getUuid(),
                            "approve",
                            "refuse",
                            JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                            "系统驳回",
                            "",
                            JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                            JSONUtil.toJsonStr(stepInfoList),
                            JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                            ApproveConstant.flowPathStatus.REFUSED.getCode() // flowPathDTO.getStatus()
                    )
            );

        }
        else if (status.equals(ApproveConstant.flowPathStatus.PASSED.getCode())) {    //通过 通知下一个办理人
            List<FlowPathStepDTO> stepInfoListWAITING = stepInfoList.stream().filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());    //获取节点代办理人

            String title = flowPathDTO.getShowName() + "的" + flowPathDTO.getTitle() + "申请";

            int passAll = 0; //当前节点的通过状态
            List<FlowPathStepNotificationDTO> notificationList = null; //当前节点未办理人
            for (FlowPathStepDTO item : stepInfoListWAITING) {
                notificationList = item.getNotificationList();
                passAll = item.getPassAll();
                if (!notificationList.isEmpty()) {
                    break;
                }
            }

            if (Objects.isNull(notificationList) || notificationList.isEmpty()) {
                return;
            }

            List<FlowPathStepNotificationDTO> notificationDatalist = new ArrayList<>();
            if (passAll == ApproveConstant.flowPassAll.ONE.getCode() || passAll == ApproveConstant.flowPassAll.ALL.getCode()) {    //单人通过 或 全部通过   通知该节点所有人
                notificationDatalist.addAll(notificationList);
            } else if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {   //依次通过
                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : notificationList) {
                    if (flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
                        notificationDatalist.add(flowPathStepNotificationDTO);
                        break;
                    }
                }
            }

            /*
             * 去重处理
             */
            Map<String, FlowPathStepNotificationDTO> itemMap = new HashMap<>();
            for (FlowPathStepNotificationDTO item : notificationDatalist) {
                itemMap.put(item.getUuid(), item);
            }
            List<String> userIdList = new ArrayList<>();
            for (FlowPathStepNotificationDTO item : itemMap.values()) {
                String userId = item.getUuid();
                userIdList.add(userId);

                /*
                 * 添加审批记录
                 */
                flowPathListMapper.addFlowPathList(
                        FlowPathListDAO.builder()
                                .title(title)
                                .flowPathId(flowPathId)
                                .uuid(item.getUuid())
                                .type(2)
                                .appChannelId(flowPathDTO.getAppChannelId())
                                .dataChannelId(flowPathDTO.getDataChannelId())
                                .extraContent(JSONUtil.toJsonStr(item))
                                .build()
                );
            }

            Map<String, Object> callbackRequestMap = new HashMap<>();
            callbackRequestMap.put("flowId", flowPathDTO.getFlowId());
            callbackRequestMap.put("flowPathId", flowPathDTO.getId());
            callbackRequestMap.put("userIdList", userIdList);
            callbackRequestMap.put("action", "approve");
            callbackRequestMap.put("event", "refuse");
            callbackRequestMap.put("title", title);
            callbackExtend.callback(flowPathDTO.getRefuseUrl(), callbackRequestMap);   //发起通知


            flowPathDTO = flowPathService.getFlowPathDetail(flowPathId);
            stepInfoListWAITING = flowPathDTO.getStepInfoList().stream().filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());//获取节点代办理人

            if (stepInfoListWAITING.isEmpty()) {    //没有待审批节点 将整个流程处理成已完结状态

                /*
                 * 更新审批流状态
                 */
                FlowPathDAO flowPathDAO = new FlowPathDAO();
                flowPathDAO.setId(flowPathId);
                flowPathDAO.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
                flowPathService.updateFlowPath(flowPathDAO);


                //通过回调前，通知回调
                callbackExtend.callback(flowPathDTO.getApproverUrl(), callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "pass",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(stepInfoList),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        ApproveConstant.flowPathStatus.PASSED.getCode()
                ));

                //通过回调
                callbackExtend.callback(flowPathDTO.getPassUrl(), callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "pass",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(stepInfoList),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        ApproveConstant.flowPathStatus.PASSED.getCode()
                ));
            }

        }

    }


    /**
     * 判断是否自动驳回
     *
     * @param flowPathId
     */
    public boolean approveReject(Integer flowPathId, FlowPathDAO flowPathDAO, String showName, String title, String appChannelId, String dataChannelId) {
        FlowPathDTO flowPathDetail = flowPathService.getFlowPathDetail(flowPathId); //获取审批详情
        if (Objects.isNull(flowPathDetail)) {
            return false;
        }
        List<FlowPathStepDTO> stepInfoList = flowPathDetail.getStepInfoList();  //获取节点集合

        List<FlowPathStepDTO> flowPathStepDTOList = stepInfoList.stream().filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());

        if (flowPathStepDTOList.isEmpty()) {
            return false;
        }

        FlowPathStepDTO flowPathStepDTO = flowPathStepDTOList.get(0);   //获取第一个待审批的节点
        Map<String, Object> extraContent = flowPathStepDTO.getExtraContent();   //第一个待审批节点的扩展信息
        JSONObject extraContentJSON = JSONUtil.parseObj(extraContent);
        Integer isAutoReject = extraContentJSON.getInt("isAutoReject");
        if (Objects.nonNull(isAutoReject) && isAutoReject == 1) {   //判断自动驳回
            FlowPathStepDTO newFlowPathStepDTO = new FlowPathStepDTO();
            newFlowPathStepDTO.setId(flowPathStepDTO.getId());
            newFlowPathStepDTO.setFlowPathId(flowPathId);
            newFlowPathStepDTO.setStatus(ApproveConstant.flowPathStatus.REFUSED.getCode());
            flowPathStep(newFlowPathStepDTO);


            String flowTitle = Optional.of(flowPathDAO.getTitle()).orElse(flowPathDAO.getFlowTitle());
            /*
             * 添加发起人审批流程数据
             */
            String uuid = flowPathDetail.getUuid();
            title = StringUtil.isEmpty(title) ? (showName + " 创建了 " + flowTitle + "申请") : title;

            // 存储操作历史
            flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO
                    .builder()
                    .title(title)
                    .flowPathId(flowPathDetail.getId())
                    .uuid(uuid)
                    .stepId(0)
                    .nextStepId(flowPathStepDTOList.get(0).getId())
                    .action(1)
                    .reason("系统分支配置自动驳回")
                    .build());

            String flowPathExtraContent = Optional.of(flowPathDAO.getExtraContent()).orElse("{}");  //审批流flowpathList扩展字段

            // 存储到 "我的" 审批列表
            flowPathListMapper.addFlowPathList(
                    FlowPathListDAO
                            .builder()
                            .title(flowTitle)
                            .flowPathId(flowPathDetail.getId())
                            .uuid(uuid)
                            .type(1)
                            .appChannelId(appChannelId)
                            .dataChannelId(dataChannelId)
                            .extraContent(flowPathExtraContent)
                            .build()
            );

            return true;
        }
        return false;
    }


    /**
     * 深拷贝步骤列表
     */
    private List<FlowPathStepDTO> deepCopyStepList(List<FlowPathStepDTO> originalList) {
        if (originalList == null) return null;
        try {
            // 推荐使用 JSON 序列化库进行深拷贝，确保 DTO 可序列化
            String json = JSONUtil.toJsonStr(originalList);
            return JSONUtil.toList(json, FlowPathStepDTO.class);
        } catch (Exception e) {
            log.error("深拷贝 FlowPathStepDTO 列表失败: {}", e.getMessage(), e);
            // 备选：手动拷贝（如果 DTO 结构简单且固定）
            // 或者抛出异常，因为无法安全处理
            throw new RuntimeException("无法深拷贝步骤列表", e);
        }
    }
}
