package com.eu.api.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.constant.FlowConstant;
import com.eu.api.domain.condition.*;
import com.eu.api.domain.dao.*;
import com.eu.api.domain.dto.*;
import com.eu.api.domain.entity.FlowEntity;
import com.eu.api.domain.entity.FlowPathEntity;
import com.eu.api.domain.entity.FlowPathListEntity;
import com.eu.api.domain.entity.TemplateEntity;
import com.eu.api.mapper.*;
import com.eu.api.service.FlowPathService;
import com.eu.api.service.FlowPathStepNotificationService;
import com.eu.api.service.FlowPathStepService;
import com.eu.api.service.GoFlowService;
import com.eu.api.service.extend.CallbackExtend;
import com.eu.common.client.RedisCache;
import com.eu.common.exception.BsException;
import com.eu.common.util.StringUtil;
import com.eu.common.util.TimeUtil;
import com.eu.common.util.TitleConfigUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 进行审批接口
 *
 * <AUTHOR>
 * @date 2025/5/19 15:16
 **/
@Service
public class GoFlowServiceImpl implements GoFlowService {
    private static final Logger log = LoggerFactory.getLogger(GoFlowServiceImpl.class);
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private FlowPathService flowPathService;
    @Autowired
    private FlowPathStepService flowPathStepService;
    @Autowired
    private FlowPathListMapper flowPathListMapper;
    @Autowired
    private CallbackExtend callbackExtend;
    @Autowired
    private FlowPathHistoryMapper flowPathHistoryMapper;
    @Autowired
    private FlowMapper flowMapper;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private FlowPathStepNotificationService flowPathStepNotificationService;
    @Autowired
    private FlowPathMapper flowPathMapper;


    /**
     * 是否开启抄送  类型：Boolean
     */
    private final static String IS_ENABLE_CC = "isEnableCc";
    /**
     * 是否审批完成，如果等于true，代表该审批流已执行完  类型：Boolean
     */
    private final static String IS_APPROVE_COMPLETED = "isApproveCompleted";
    /**
     * 各节点已经通过的用户集合，用来处理一个人有多个审批节点时根据配置审批一次  类型： Map<Integer, List<String>>
     */
    private final static String PASS_STEP_UUID_MAP = "passStepUuidMap";
    /**
     * 下一个待执行的节点  类型：FlowPathStepDTO
     */
    private final static String NEXT_WAIT_STEP_INFO = "nextWaitStepInfo";
    /**
     * 通过的审批人列表  类型：List<String>
     */
    private final static String PASS_APPROVAL_UUID_LIST = "passApprovalUuidList";
    /**
     * 抄送节点通知列表  类型：List<FlowPathStepNotificationDTO>
     */
    private final static String CC_NOTIFICATION_LIST = "ccNotificationList";
    /**
     * 审批对象
     */
    private final static String FLOW_INFO = "flowInfo";
    /**
     * 当前执行节点
     */
    private final static String THIS_STEP_INFO = "thisStepInfo";
    /**
     * 上一个执行节点
     */
    private final static String LAST_STEP_INFO = "lastStepInfo";
    /**
     * 下一个执行节点
     */
    private final static String NEXT_STEP_INFO = "nextStepInfo";

    /**
     * 审批 数据推进方法
     *
     * @param id            审批流程ID
     * @param action        审批操作类型
     * @param uuid          操作人身份标识
     * @param showName      操作人名称
     * @param reason        操作备注
     * @param appChannelId  操作客户端渠道ID
     * @param dataChannelId 操作数据源ID
     * @param title         操作标题
     * @param isSkip        是否跳过加锁， true 跳过   false 不跳过
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean goFlowV2(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title, Boolean isSkip) {
        String redisKey = String.format(ApproveConstant.APPROVE_LOCK_PREFIX, id);
        long redisValue = System.currentTimeMillis();
        if (Objects.isNull(isSkip) || !isSkip) {
            boolean getLockResult = redisCache.setIfAbsent(redisKey, redisValue, ApproveConstant.APPROVE_LOCK_EXPIRE_TIME);
            if (!getLockResult) {
                throw new BsException("该审批正在处理中，请稍后重试");
            }
        }
        try {
            // 1.获取审批详情
            FlowPathDTO flowPathDTO = flowPathService.getFlowPathDetail(id);
            checkFlowPath(flowPathDTO);
            // 2. 判断节点是否设置了自动驳回
            boolean autoRejectResult = approveAutoReject(flowPathDTO, uuid);
            if (autoRejectResult) {
                return Boolean.TRUE;
            }
            Map<String, Object> paramMap = getParamMap();
            // 3. 审核开始执行
            if (ApproveConstant.flowPathAction.CREATE.getCode() == action) {
                FlowPathStepDTO nextWaitStepInfo = flowPathDTO.getStepInfoList().get(0);
                paramMap.put(NEXT_WAIT_STEP_INFO, nextWaitStepInfo);
                approveCreate(flowPathDTO, showName, uuid, nextWaitStepInfo.getId(), reason, title, appChannelId, dataChannelId);
                // 第一个节点通知人为空，直接抄送（设置开启抄送）
                if (Objects.isNull(nextWaitStepInfo) || CollectionUtil.isEmpty(nextWaitStepInfo.getNotificationList())) {
                    paramMap.put(IS_ENABLE_CC, true);
                }
            } else {
                paramMap = emptyStepHandle(flowPathDTO, paramMap);
                FlowPathStepDTO thisStepInfo = Objects.nonNull(paramMap.get(THIS_STEP_INFO)) ? (FlowPathStepDTO) paramMap.get(THIS_STEP_INFO) : null;
                FlowPathStepDTO lastStepInfo = Objects.nonNull(paramMap.get(LAST_STEP_INFO)) ? (FlowPathStepDTO) paramMap.get(LAST_STEP_INFO) : null;
                FlowPathStepDTO nextStepInfo = Objects.nonNull(paramMap.get(NEXT_STEP_INFO)) ? (FlowPathStepDTO) paramMap.get(NEXT_STEP_INFO) : null;
                paramMap.put(CC_NOTIFICATION_LIST, thisStepInfo.getCcNotificationList());
                if (ApproveConstant.flowPathAction.REFUSE.getCode() == action) {
                    approvePermissionVerification(thisStepInfo, uuid);
                    approveReject(thisStepInfo, flowPathDTO, showName, uuid, reason, false);
                    paramMap.put(IS_APPROVE_COMPLETED, true);
                } else if (ApproveConstant.flowPathAction.PASS.getCode() == action) {
                    Map<Integer, List<String>> passStepUuidMap = getPassStepUuidMap(flowPathDTO.getStepInfoList());
                    approvePermissionVerification(thisStepInfo, uuid);
                    paramMap = approvePass(flowPathDTO, thisStepInfo, nextStepInfo, passStepUuidMap, uuid, title, showName, reason, paramMap);
                } else if (ApproveConstant.flowPathAction.WITHDRAW.getCode() == action) {
                    paramMap = approveWithdraw(flowPathDTO, uuid, paramMap);
                } else if (ApproveConstant.flowPathAction.BACK.getCode() == action) {
                    approvePermissionVerification(thisStepInfo, uuid);
                    paramMap = approveBack(flowPathDTO, lastStepInfo, nextStepInfo, thisStepInfo, title, showName, uuid, reason, paramMap);
                }
            }
            paramMap = preprocessNextStep(flowPathDTO, title, action, uuid, paramMap, showName);
            ccHandle(flowPathDTO, paramMap);
            automaticallyPass(flowPathDTO, action, uuid, title, reason, appChannelId, dataChannelId, showName, paramMap);
        } finally {
            redisCache.deleteIfExists(redisKey, redisValue);
        }
        return Boolean.TRUE;
    }

    /**
     * 预设标识
     *
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/5/21 16:38
     **/
    @NotNull
    private static Map<String, Object> getParamMap() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(IS_ENABLE_CC, false);
        paramMap.put(IS_APPROVE_COMPLETED, false);
        paramMap.put(PASS_STEP_UUID_MAP, new HashMap<Integer, List<String>>());
        paramMap.put(PASS_APPROVAL_UUID_LIST, new ArrayList<String>());
        paramMap.put(CC_NOTIFICATION_LIST, new ArrayList<FlowPathStepNotificationDTO>());
        return paramMap;
    }

    /**
     * 验证审批状态
     *
     * @param flowPathDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/19 16:04
     **/
    private static void checkFlowPath(FlowPathDTO flowPathDTO) {
        if (Objects.isNull(flowPathDTO)) {
            throw new BsException("审批流程不存在");
        }
        if (CollectionUtil.isEmpty(flowPathDTO.getStepInfoList())) {
            throw new BsException("审批流程步骤不存在");
        }
        if (ApproveConstant.flowPathStatus.WAITING.getCode() != flowPathDTO.getStatus()) {
            throw new BsException("审批流程已结束");
        }
        if (ApproveConstant.flowPathIsInterrupt.YES.getCode() == flowPathDTO.getIsInterrupt()) {
            throw new BsException("审批流程已被暂停");
        }
    }

    /**
     * 尝试自动通过
     *
     * @param flowPathDTO
     * @param action
     * @param uuid
     * @param title
     * @param reason
     * @param appChannelId
     * @param dataChannelId
     * @param showName
     * @param paramMap
     * @return void
     * <AUTHOR>
     * @date 2025/5/27 9:55
     **/
    private void automaticallyPass(FlowPathDTO flowPathDTO, Integer action, String uuid, String title, String reason
            , String appChannelId, String dataChannelId, String showName, Map<String, Object> paramMap) {
        List<String> passApprovalUuidList = (List<String>) paramMap.get(PASS_APPROVAL_UUID_LIST);
        Map<Integer, List<String>> passStepUuidMap = (Map<Integer, List<String>>) paramMap.get(PASS_STEP_UUID_MAP);
        String flowTitle = getFlowTitle(flowPathDTO);
        if (CollectionUtil.isNotEmpty(passApprovalUuidList) && (ApproveConstant.flowPathAction.CREATE.getCode() == action
                || ApproveConstant.flowPathAction.PASS.getCode() == action)) {
            // 匹配uuid和人名字 处理自动通过名字不对的问题
            Map<String, String> showNameByUuidMap = new HashMap<>();
            for (FlowPathStepDTO flowPathStepDTO : flowPathDTO.getStepInfoList()) {
                List<FlowPathStepNotificationDTO> notification = flowPathStepDTO.getNotification();
                if (CollectionUtil.isEmpty(notification)) {
                    continue;
                }
                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : notification) {
                    JSONObject extraContent = flowPathStepNotificationDTO.getExtraContent();
                    if (Objects.nonNull(extraContent)) {
                        showNameByUuidMap.put(flowPathStepNotificationDTO.getUuid(), extraContent.getStr("showName", ""));
                    }
                }
            }
            //相同ID自动通过: [1:流转过的相同节点自动通过; 2:连续相同节点自动通过; 3:不自动]
            if (flowPathDTO.getAutoappr() == 2) {
                //连续id自动通过
                for (String apprUuid : passApprovalUuidList) {
                    if (apprUuid.equals(uuid)) {
                        String name = showNameByUuidMap.get(uuid);
                        if (StringUtil.isNotEmpty(name)) {
                            showName = name;
                        }
                        title = showName + " 自动通过了 " + flowTitle;
                        goFlowV2(flowPathDTO.getId(), 2, uuid, flowPathDTO.getShowName(), reason, appChannelId, dataChannelId, title, true);
                        break;
                    }
                }
            } else if (flowPathDTO.getAutoappr() == 1) {
                Set<String> passUuidSet = new HashSet<>();//已通过的uuid
                for (Map.Entry<Integer, List<String>> entry : passStepUuidMap.entrySet()) {
                    passUuidSet.addAll(new HashSet<>(entry.getValue()));
                }
                passUuidSet.add(flowPathDTO.getUuid());//创建人也当成已通过
                for (String apprUuid : passApprovalUuidList) {
                    if (passUuidSet.contains(apprUuid)) {
                        String name = showNameByUuidMap.get(uuid);
                        if (StringUtil.isNotEmpty(name)) {
                            showName = name;
                        }
                        title = showName + " 自动通过了 " + flowPathDTO.getShowName() + "的" + flowTitle;
                        goFlowV2(flowPathDTO.getId(), 2, apprUuid, showName, reason, appChannelId, dataChannelId, title, true);
                        break;
                    }
                }
            }
        }
    }

    /**
     * 处理抄送
     *
     * @param flowPathDTO
     * @param paramMap
     * @return void
     * <AUTHOR>
     * @date 2025/5/19 14:35
     **/
    private void ccHandle(FlowPathDTO flowPathDTO, Map<String, Object> paramMap) {
        // 1. 审批完成，发送抄送人
        String flowTitle = getFlowTitle(flowPathDTO);
        if ((Boolean) paramMap.get(IS_ENABLE_CC) && CollectionUtil.isNotEmpty((List<FlowPathStepNotificationDTO>) paramMap.get(CC_NOTIFICATION_LIST))) {
            List<String> userIdList = new ArrayList<>();
            List<FlowPathListEntity> flowPathListList = new ArrayList<>();
            String title = flowPathDTO.getShowName() + "抄送了"
                    + configTitle(flowTitle, JSONUtil.toJsonStr(flowPathDTO.getTempShowData()), flowPathDTO.getFlowId(), flowPathDTO.getShowName(), JSONUtil.toJsonStr(flowPathDTO.getExtraContent()), flowPathDTO.getTitle(), flowPathDTO.getCreateTime())
                    + "申请给你";
            for (FlowPathStepNotificationDTO notifyOne : (List<FlowPathStepNotificationDTO>) paramMap.get(CC_NOTIFICATION_LIST)) {
                String userId = notifyOne.getUuid();
                userIdList.add(userId);
                FlowPathListEntity flowPathListEntity = FlowPathListEntity.builder()
                        .title(title)
                        .flowPathId(flowPathDTO.getId())
                        .uuid(userId)
                        .type(ApproveConstant.flowPathListType.CC.getCode())
                        .appChannelId(flowPathDTO.getAppChannelId())
                        .dataChannelId(flowPathDTO.getDataChannelId())
                        .extraContent(JSONUtil.toJsonStr(notifyOne))
                        .build();
                flowPathListList.add(flowPathListEntity);
            }
            flowPathListMapper.insert(flowPathListList);
            Map<String, Object> callbackRequestMap = new HashMap<>();
            callbackRequestMap.put("flowId", flowPathDTO.getFlowId());
            callbackRequestMap.put("flowPathId", flowPathDTO.getId());
            callbackRequestMap.put("userIdList", userIdList);
            callbackRequestMap.put("action", "approve");
            callbackRequestMap.put("event", "normal");
            callbackRequestMap.put("title", title);
            callbackExtend.callback(flowPathDTO.getApproverUrl(), callbackRequestMap);
        }

        // 2.抄送本人
        if ((Boolean) paramMap.get(IS_APPROVE_COMPLETED) && flowPathDTO.getCcCreate() > 0) {
            String title = "你的" + configTitle(flowTitle, JSONUtil.toJsonStr(flowPathDTO.getTempShowData()), flowPathDTO.getFlowId(), flowPathDTO.getShowName(), JSONUtil.toJsonStr(flowPathDTO.getExtraContent()), flowPathDTO.getTitle(), flowPathDTO.getCreateTime()) + "申请";
            // 我的审批  抄送我 列表增加数据
            flowPathListMapper.addFlowPathList(
                    FlowPathListDAO.builder()
                            .title(title)
                            .flowPathId(flowPathDTO.getId())
                            .uuid(flowPathDTO.getUuid())
                            .type(ApproveConstant.flowPathListType.CC.getCode())
                            .appChannelId(flowPathDTO.getAppChannelId())
                            .dataChannelId(flowPathDTO.getDataChannelId())
                            .extraContent(JSONUtil.toJsonStr(flowPathDTO.getExtraContent()))
                            .build()
            );
            // 通知回调
            callbackExtend.callback(
                    flowPathDTO.getApproverUrl(),
                    callbackExtend.createNotificationData(
                            flowPathDTO.getFlowId(),
                            flowPathDTO.getId(),
                            flowPathDTO.getUuid(),
                            "approve",
                            "normal",
                            JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                            title,
                            JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                            "",
                            JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                            JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                            flowPathDTO.getStatus()
                    )
            );
        }
    }

    /**
     * 预处理下一个节点
     *
     * @param flowPathDTO
     * @param title
     * @param action
     * @param uuid
     * @param paramMap
     * @param showName
     * @return void
     * <AUTHOR>
     * @date 2025/5/19 10:33
     **/
    private Map<String, Object> preprocessNextStep(FlowPathDTO flowPathDTO, String title, Integer action, String uuid, Map<String, Object> paramMap, String showName) {
        if (Objects.nonNull(paramMap.get(NEXT_WAIT_STEP_INFO))) {
            List<FlowPathStepNotificationDTO> ccNotificationList = (List<FlowPathStepNotificationDTO>) paramMap.get(CC_NOTIFICATION_LIST);
            FlowPathStepDTO nextWaitStepInfo = (FlowPathStepDTO) paramMap.get(NEXT_WAIT_STEP_INFO);
            List<String> approvalUuidList = (List<String>) paramMap.get(PASS_APPROVAL_UUID_LIST);
            // 1. 查询待处理节点列表
            List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(
                    GetFlowPathStepCondition.builder().
                            flowPathId(flowPathDTO.getId()).
                            status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .build());
            if (CollectionUtil.isNotEmpty(newStepInfoDTOList)) {
                // 2.处理空节点，重新获取下一个节点
                nextWaitStepInfo = null;
                Iterator<FlowPathStepDTO> iterator = newStepInfoDTOList.iterator();
                while (iterator.hasNext()) {
                    nextWaitStepInfo = iterator.next();
                    if (CollectionUtil.isNotEmpty(nextWaitStepInfo.getNotificationList())) {
                        break;
                    }
                    if (CollectionUtil.isEmpty(nextWaitStepInfo.getCcNotificationList())) {
                        boolean result = execEmptyStepByConfig(flowPathDTO, paramMap, nextWaitStepInfo);
                        if (!result) {
                            break;
                        }
                        continue;
                    }
                    ccNotificationList.addAll(nextWaitStepInfo.getCcNotificationList());
                    flowPathStepService.update(
                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                            GetFlowPathStepCondition.builder().flowPathId(nextWaitStepInfo.getFlowPathId()).stepNo(nextWaitStepInfo.getStepNo()).build()
                    );
                }

                String flowTitle = getFlowTitle(flowPathDTO);
                // 3. 节点是最后一个节点并且不是审核节点，此时就执行审核完成操作
                if (!iterator.hasNext() && CollectionUtil.isEmpty(nextWaitStepInfo.getNotificationList())) {
                    String tempShowDataJson = JSONUtil.toJsonStr(flowPathDTO.getTempShowData());
                    String tempDataJson = JSONUtil.toJsonStr(flowPathDTO.getTempData());
                    String extraContentJson = JSONUtil.toJsonStr(flowPathDTO.getExtraContent());
                    String stepListJson = JSONUtil.toJsonStr(newStepInfoDTOList);
                    // 3.封装标题
                    title = StringUtil.isBlank(title) ? (showName + " 通过了 " + flowPathDTO.getShowName() + "的" + flowTitle) : title;
                    // 修改审批表
                    flowPathMapper.updateFlowPath(
                            FlowPathDAO.builder()
                                    .id(flowPathDTO.getId())
                                    .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                                    .updateTime(new Date())
                                    .build()
                    );
                    // 通知回调
                    callbackExtend.callback(
                            flowPathDTO.getApproverUrl(),
                            callbackExtend.createNotificationData(
                                    flowPathDTO.getFlowId(),
                                    flowPathDTO.getId(),
                                    flowPathDTO.getUuid(),
                                    "approve",
                                    "pass",
                                    tempShowDataJson,
                                    title,
                                    "",
                                    tempDataJson,
                                    stepListJson,
                                    extraContentJson,
                                    flowPathDTO.getStatus()
                            )
                    );

                    // 通过回调
                    callbackExtend.callback(
                            flowPathDTO.getPassUrl(),
                            callbackExtend.createNotificationData(
                                    flowPathDTO.getFlowId(),
                                    flowPathDTO.getId(),
                                    flowPathDTO.getUuid(),
                                    "approve",
                                    "pass",
                                    tempShowDataJson,
                                    title,
                                    "",
                                    tempDataJson,
                                    stepListJson,
                                    extraContentJson,
                                    ApproveConstant.flowPathAction.PASS.getCode()
                            )
                    );
                }

                // 通知下一个节点审核人
                if (Objects.nonNull(nextWaitStepInfo) && CollectionUtil.isNotEmpty(nextWaitStepInfo.getNotificationList())) {
                    String tempShowDataJson = JSONUtil.toJsonStr(flowPathDTO.getTempShowData());
                    String extraContentJson = JSONUtil.toJsonStr(flowPathDTO.getExtraContent());
                    // 待审
                    if (ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == nextWaitStepInfo.getPassAll()) {
                        //依次审批只通知下一个待审
                        int isNextPerson = 0;
                        for (FlowPathStepNotificationDTO notifyOne : nextWaitStepInfo.getNotificationList()) {
                            if (isNextPerson > 0 || 0 == action || (Boolean) paramMap.getOrDefault(IS_ENABLE_CC, false)) {
                                //存储待我处理的审批
                                title = flowPathDTO.getShowName() + "的" + flowTitle + "申请";
                                title = configTitle(title, tempShowDataJson, flowPathDTO.getFlowId(), flowPathDTO.getShowName(), extraContentJson, flowPathDTO.getTitle(), flowPathDTO.getCreateTime()); //合并代码
                                approvalUuidList.add(notifyOne.getUuid());
                                flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(flowPathDTO.getId()).uuid(notifyOne.getUuid()).type(2).appChannelId(flowPathDTO.getAppChannelId()).dataChannelId(flowPathDTO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
                                Map<String, Object> notificationData = new HashMap<>();
                                notificationData.put("action", "approve");
                                notificationData.put("event", "wait");
                                notificationData.put("title", title);
                                notificationData.put("uuid", notifyOne.getUuid());
                                notificationData.put("flowPathId", flowPathDTO.getId());
                                notificationData.put("flowId", flowPathDTO.getFlowId());
                                notificationData.put("tempShowData", flowPathDTO.getTempShowData());
                                notificationData.put("extraContent", JSONUtil.toJsonStr(notifyOne));
                                callbackExtend.callback(flowPathDTO.getApproverUrl(), notificationData);
                                break;
                            }
                            if (notifyOne.getUuid().equals(uuid)) {
                                isNextPerson = 1;
                            }
                        }
                    } else {
                        List<String> userIdList = new ArrayList<>();
                        title = flowPathDTO.getShowName() + "的" + flowTitle + "申请";
                        title = configTitle(title, tempShowDataJson, flowPathDTO.getFlowId(), flowPathDTO.getShowName(), extraContentJson, flowPathDTO.getTitle(), flowPathDTO.getCreateTime());
                        for (FlowPathStepNotificationDTO notifyOne : nextWaitStepInfo.getNotificationList()) {
                            String userId = notifyOne.getUuid();
                            //存储待我处理的审批
                            flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(flowPathDTO.getId()).uuid(userId).type(2).appChannelId(flowPathDTO.getAppChannelId()).dataChannelId(flowPathDTO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
                            approvalUuidList.add(userId);
                            userIdList.add(userId);
                        }
                        Map<String, Object> callbackRequestMap = new HashMap<>();
                        callbackRequestMap.put("flowId", flowPathDTO.getFlowId());
                        callbackRequestMap.put("flowPathId", flowPathDTO.getId());
                        callbackRequestMap.put("userIdList", userIdList);
                        callbackRequestMap.put("action", "approve");
                        callbackRequestMap.put("event", "wait");
                        callbackRequestMap.put("title", title);
                        callbackExtend.callback(flowPathDTO.getApproverUrl(), callbackRequestMap);
                    }
                }
            }
            paramMap.put(NEXT_WAIT_STEP_INFO, nextWaitStepInfo);
            paramMap.put(PASS_APPROVAL_UUID_LIST, approvalUuidList);
            paramMap.put(CC_NOTIFICATION_LIST, ccNotificationList);
        }
        return paramMap;
    }

    /**
     * 退回审批
     *
     * @param flowPathDTO
     * @param lastStepInfo
     * @param nextStepInfo
     * @param thisStepInfo
     * @param title
     * @param showName
     * @param uuid
     * @param reason
     * @param paramMap
     * @return java.util.Map<java.lang.String, java.lang.Object>
     * <AUTHOR>
     * @date 2025/5/21 17:03
     **/
    private Map<String, Object> approveBack(FlowPathDTO flowPathDTO, FlowPathStepDTO lastStepInfo, FlowPathStepDTO nextStepInfo
            , FlowPathStepDTO thisStepInfo, String title, String showName, String uuid, String reason, Map<String, Object> paramMap) {
        String flowTitle = getFlowTitle(flowPathDTO);
        title = StringUtil.isBlank(title) ? (showName + " 退回了 " + flowPathDTO.getShowName() + "的" + flowTitle) : title;
        //如果有上一步，退回到上一步，状态不变
        if (Objects.nonNull(lastStepInfo)) {
            nextStepInfo = thisStepInfo;
            thisStepInfo = lastStepInfo;
            paramMap.put(NEXT_WAIT_STEP_INFO, nextStepInfo);
            // 1. 添加操作历史
            flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO.builder()
                    .title(title)
                    .flowPathId(flowPathDTO.getId())
                    .uuid(uuid)
                    .stepId(thisStepInfo.getId())
                    .nextStepId(nextStepInfo.getId())
                    .action(ApproveConstant.flowPathAction.BACK.getCode())
                    .reason(reason).build());

            // 2.变更我的审核列表
            List<String> lastUuidList;
            if (CollectionUtil.isNotEmpty(lastStepInfo.getNotificationList())) {
                lastUuidList = lastStepInfo.getNotificationList().stream().map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toList());
            } else {
                lastUuidList = Collections.emptyList();
            }
            List<String> notificationUuidList = Collections.emptyList();
            if (CollectionUtil.isNotEmpty(nextStepInfo.getNotificationList())) {
                notificationUuidList = nextStepInfo.getNotificationList().stream().filter(v -> !lastUuidList.contains(v.getUuid())).map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toList());
            }
            if (!notificationUuidList.isEmpty()) {
                //删除 不在上不步的审核人 的我的审核 -> 待处理 列表数据
                flowPathListMapper.delete(GetFlowPathListDAOListCondition.builder()
                        .flowPathId(flowPathDTO.getId()).uuidList(notificationUuidList).typeList(new ArrayList<Integer>() {{
                            add(2);
                            add(3);
                        }}).build());
            }
            // 上一步审批人 我的审核列表 已处理 变更成 待处理
            flowPathListMapper.update(
                    FlowPathListDAO.builder().type(3).build(),
                    GetFlowPathListDAOListCondition.builder().flowPathId(flowPathDTO.getId()).uuidList(lastUuidList).type(2).build()
            );

            // 3.更新上一步flow_path_step_notification表的状态为待审核
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(lastStepInfo.getFlowPathId())
                            .stepNo(lastStepInfo.getStepNo())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
            // 更新此步骤flow_path_step_notification表的状态为待审核 ， 这里有必要，万一此步骤是全部通过或依次通过呢
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(nextStepInfo.getFlowPathId())
                            .stepNo(nextStepInfo.getStepNo())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
        } else {
            //如果没有上一步，视为撤回，状态为5
            // 1. 更新状态
            flowPathMapper.updateFlowPath(FlowPathDAO.builder().id(flowPathDTO.getId()).status(ApproveConstant.flowPathStatus.BACK.getCode()).updateTime(new Date()).build());
            // 2. 添加操作历史
            title = StringUtil.isBlank(title) ? (showName + " 退回了 " + flowPathDTO.getShowName() + "的" + flowTitle) : title;
            flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO.builder()
                    .title(title)
                    .flowPathId(flowPathDTO.getId())
                    .uuid(uuid)
                    .stepId(thisStepInfo.getId())
                    .nextStepId(0)
                    .action(ApproveConstant.flowPathAction.BACK.getCode())
                    .reason(reason).build());
            // 3. 我的审批 ： 待处理  -> 已处理
            flowPathListMapper.update(FlowPathListDAO.builder().type(3).build(), GetFlowPathListDAOListCondition.builder().flowPathId(flowPathDTO.getId()).type(2).build());
            // 4. 更新此步骤flow_path_step_notification表的状态为待审核
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(thisStepInfo.getFlowPathId())
                            .stepNo(thisStepInfo.getStepNo())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
            paramMap.put(IS_APPROVE_COMPLETED, true);
        }
        return paramMap;
    }

    /**
     * 审批撤销
     *
     * @param flowPathDTO
     * @param uuid
     * @param paramMap
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 17:31
     **/
    private Map<String, Object> approveWithdraw(FlowPathDTO flowPathDTO, String uuid, Map<String, Object> paramMap) {
        if (!flowPathDTO.getUuid().equals(uuid)) {
            throw new BsException("对不起，当前用户权限不符合当前规则");
        }
        if (flowPathDTO.getWithdraw() == FlowConstant.flowWithdraw.PROHIBIT.getCode()) {
            throw new BsException("对不起，当前流程不支持撤销操作");
        } else if (FlowConstant.flowWithdraw.PERMIT.getCode() == flowPathDTO.getWithdraw()) {
            // 正常撤回，直接撤回成功
            withdrawnPermit(flowPathDTO);
            paramMap.put(IS_APPROVE_COMPLETED, true);
        } else if (FlowConstant.flowWithdraw.CONFIRMATION.getCode() == flowPathDTO.getWithdraw()) {
            // 如果当前步骤内有已审核通过的人，则将该步骤也放入循环中，当作新审批内的最后一步
            List<FlowPathStepDTO> passStepList = flowPathDTO.getStepInfoList().stream()
                    .filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus() && CollectionUtil.isNotEmpty(v.getNotificationList()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(passStepList)) {
                withdrawnPermit(flowPathDTO);
            } else {
                withdrawnConfirmation(flowPathDTO, passStepList,new ArrayList<>());
            }
        }else if(FlowConstant.flowWithdraw.CONFIRMATION_CURRENT_AUDIT.getCode() == flowPathDTO.getWithdraw()){
            //获取第一个待审批节点，当前节点
            Optional<FlowPathStepDTO> first = flowPathDTO.getStepInfoList().stream().filter(v -> ApproveConstant.flowPathStatus.WAITING.getCode() == v.getStatus()).findFirst();
            if(first.isPresent()){
                withdrawnConfirmation(flowPathDTO, new ArrayList<>(), Collections.singletonList(first.get()));
            }else{
                withdrawnPermit(flowPathDTO);
            }
        }else if(FlowConstant.flowWithdraw.CONFIRMATION_AND_CURRENT_AUDIT.getCode() == flowPathDTO.getWithdraw()){
            //获取所有已审核通过节点
            List<FlowPathStepDTO> passStepList = flowPathDTO.getStepInfoList().stream()
                    .filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus() && CollectionUtil.isNotEmpty(v.getNotificationList()))
                    .collect(Collectors.toList());
            //获取第一个待审批节点，当前节点
            Optional<FlowPathStepDTO> first = flowPathDTO.getStepInfoList().stream().filter(v -> ApproveConstant.flowPathStatus.WAITING.getCode() == v.getStatus()).findFirst();
            if(CollectionUtil.isEmpty(passStepList)&&!first.isPresent()){
                withdrawnPermit(flowPathDTO);
            }else{
                //passStepList.add(first.get());
                withdrawnConfirmation(flowPathDTO, passStepList, Collections.singletonList(first.get()));
            }
        }
        /*
         * 当设置了撤销后通知已审核人，那么就对已经审核通过的人进行通知
         */
        if (1 == flowPathDTO.getIsRevokeNotice()) {
            // 获取撤销时已经审核过的人
            Set<String> uuidList = flowPathDTO.getStepInfoList().stream()
                    .filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus())
                    .map(FlowPathStepDTO::getNotification).filter(CollectionUtil::isNotEmpty)
                    .flatMap(Collection::stream)
                    .filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus())
                    .map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(uuidList)) {
                String title = flowPathDTO.getShowName() + " 撤销了 " + getFlowTitle(flowPathDTO);
                Map<String, Object> callbackRequestMap = new HashMap<>();
                callbackRequestMap.put("flowId", flowPathDTO.getFlowId());
                callbackRequestMap.put("flowPathId", flowPathDTO.getId());
                callbackRequestMap.put("userIdList", uuidList);
                callbackRequestMap.put("action", "approve");
                callbackRequestMap.put("event", "revocation");
                callbackRequestMap.put("title", title);
                callbackExtend.callback(flowPathDTO.getApproverUrl(), callbackRequestMap);
            }
        }
        return paramMap;
    }

    /**
     * 撤销需要已通过的审核人审核时，生成新的审批
     *
     * @param flowPathDTO
     * @param passStepList
     * @return void
     * <AUTHOR>
     * @date 2025/5/27 9:54
     **/
    private void withdrawnConfirmation(FlowPathDTO flowPathDTO, List<FlowPathStepDTO> passStepList, List<FlowPathStepDTO> waitStepList) {
        // 1. 首先验证是否已存在 该条流程的 撤销确认申请
        List<FlowPathDAO> flowPathList = flowPathMapper.getFlowPathList(
                GetFlowPathCondition
                        .builder()
                        .withdrawPathId(flowPathDTO.getId())
                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                        .build(),
                0,
                0
        );
        if (CollectionUtil.isNotEmpty(flowPathList)) {
            return;
        }
        // 2、循环已经通过的步骤
        for (FlowPathStepDTO stepInfo : passStepList) {
            // 剔除步骤内未审核通过的人
            if (CollectionUtil.isNotEmpty(stepInfo.getNotification())) {
                List<FlowPathStepNotificationDTO> notificationList = stepInfo.getNotification().stream()
                        .filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus()
                                || ApproveConstant.flowPathStatus.RETURN_BACL.getCode() == v.getStatus())
                        .collect(Collectors.toList());
                stepInfo.setNotification(notificationList);
            }
        }

        //添加待审核的节点
        if(FlowConstant.flowWithdraw.CONFIRMATION_CURRENT_AUDIT.getCode() == flowPathDTO.getWithdraw()||
                FlowConstant.flowWithdraw.CONFIRMATION_AND_CURRENT_AUDIT.getCode() == flowPathDTO.getWithdraw()){
            passStepList.addAll(waitStepList);
        }

        // 3、将当前审批设置为 暂停状态
        interruptApproval(flowPathDTO);
        // 4、生成新的 DAO
        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathDTO.getId()).build());
        flowPathDAO.setTitle(flowPathDAO.getTitle() + "的撤销");
        flowPathDAO.setStatus(ApproveConstant.flowPathStatus.WAITING.getCode());
        flowPathDAO.setCreateTime(null);
        flowPathDAO.setUpdateTime(null);
        flowPathDAO.setIsInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode());
        // 耀宏紧急需求，撤销的申请不允许撤销
        flowPathDAO.setWithdraw(FlowConstant.flowWithdraw.PROHIBIT.getCode());
        flowPathDAO.setStepInfo(JSONUtil.toJsonStr(passStepList));
        flowPathDAO.setWithdrawPathId(flowPathDAO.getId());
        // 5、保存新审批流程
        if (flowPathMapper.addFlowPath(flowPathDAO) <= 0) {
            throw new BsException("创建审批流失败");
        }
        // 6、存储flow_path_step表
        for (FlowPathStepDTO flowPathStepDTO : passStepList) {
            List<StepInfoNotificationDTO> stepNotificationList = Collections.emptyList();
            if (CollectionUtil.isNotEmpty(flowPathStepDTO.getNotification())) {
                stepNotificationList = flowPathStepDTO.getNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
            }
            List<StepInfoNotificationDTO> stepCcNotificationList = Collections.emptyList();
            if (CollectionUtil.isNotEmpty(flowPathStepDTO.getCcNotification())) {
                stepCcNotificationList = flowPathStepDTO.getCcNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
            }
            flowPathStepService.addFlowPathStep(
                    flowPathDAO.getId(),
                    flowPathStepDTO.getStepNo(),
                    flowPathStepDTO.getTitle(),
                    flowPathStepDTO.getPassCons(),
                    stepNotificationList,
                    stepCcNotificationList,
                    flowPathStepDTO.getPassAll(),
                    flowPathStepDTO.getWithdraw(),
                    flowPathStepDTO.getGoback(),
                    ApproveConstant.flowPathStatus.WAITING.getCode(),
                    ApproveConstant.appiont.NO.getCode(),
                    ApproveConstant.returnBack.NO.getCode(),
                    flowPathStepDTO.getExtraContent(),
                    0,
                    flowPathStepDTO.getPreId(),
                    flowPathStepDTO.getStepType()
            );
        }

        // 7、获取stepInfoList
        List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
                .flowPathId(flowPathDAO.getId())
                .build());

        // 8、存储操作历史
        flowPathHistoryMapper.addFlowPathHistory(
                FlowPathHistoryDAO
                        .builder()
                        .title(flowPathDAO.getTitle())
                        .flowPathId(flowPathDAO.getId())
                        .uuid(flowPathDAO.getUuid())
                        .stepId(0)
                        .nextStepId(newStepInfoDTOList.get(0).getId())
                        .action(ApproveConstant.flowPathAction.CREATE.getCode())
                        .reason("")
                        .build()
        );
        // 9、存储到 我的审批 -> 已提交
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO
                        .builder()
                        .title(flowPathDAO.getTitle())
                        .flowPathId(flowPathDAO.getId())
                        .uuid(flowPathDAO.getUuid())
                        .type(ApproveConstant.flowPathListType.MY.getCode())
                        .appChannelId(flowPathDAO.getAppChannelId())
                        .dataChannelId(flowPathDAO.getDataChannelId())
                        .extraContent(flowPathDAO.getExtraContent())
                        .build()
        );

        // 10、获取新建审批第一个待审核步骤
        Optional<FlowPathStepDTO> firstWaitingStepInfo = newStepInfoDTOList.stream().filter(newStepInfoDTO -> newStepInfoDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
        if (!firstWaitingStepInfo.isPresent()) {
            throw new BsException("创建审批流失败");
        }
        FlowPathStepDTO flowPathStepDTO = firstWaitingStepInfo.get();

        // 11、依据当前步骤规则设置待审批人员
        if (flowPathStepDTO.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
            Optional<FlowPathStepNotificationDTO> firstNotificationInfo = flowPathStepDTO.getNotification().stream().filter(notification -> notification.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
            if (!firstNotificationInfo.isPresent()) {
                throw new BsException("创建审批流失败");
            }
            FlowPathStepNotificationDTO flowPathStepNotificationDTO = firstNotificationInfo.get();
            setWaitApprove(flowPathDAO, newStepInfoDTOList, flowPathStepNotificationDTO);
        } else {
            for (FlowPathStepNotificationDTO notificationInfo : flowPathStepDTO.getNotification()) {
                setWaitApprove(flowPathDAO, newStepInfoDTOList, notificationInfo);
            }
        }
    }

    /**
     * 给审核人 我的审批 -> 待处理列表加数据
     * 给审核人发送消息通知
     *
     * @param newFlowPathDAO
     * @param newStepInfoDTOList
     * @param flowPathStepNotificationDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 17:29
     **/
    private void setWaitApprove(FlowPathDAO newFlowPathDAO, List<FlowPathStepDTO> newStepInfoDTOList, FlowPathStepNotificationDTO flowPathStepNotificationDTO) {
        // 1、未审批人添加待审
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO.builder()
                        .title(newFlowPathDAO.getShowName() + "的" + newFlowPathDAO.getTitle() + "申请")
                        .flowPathId(newFlowPathDAO.getId())
                        .uuid(flowPathStepNotificationDTO.getUuid())
                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
                        .appChannelId(newFlowPathDAO.getAppChannelId())
                        .dataChannelId(newFlowPathDAO.getDataChannelId())
                        .extraContent(JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()))
                        .build()
        );

        // 2、回调业务层-新审批开始流转
        callbackExtend.callback(
                newFlowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        newFlowPathDAO.getFlowId(),
                        newFlowPathDAO.getId(),
                        flowPathStepNotificationDTO.getUuid(),
                        "approve",
                        "normal",
                        newFlowPathDAO.getTempShowData(),
                        newFlowPathDAO.getTitle(),
                        JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()),
                        newFlowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(newStepInfoDTOList),
                        newFlowPathDAO.getExtraContent(),
                        newFlowPathDAO.getStatus()
                )
        );
    }

    /**
     * 将当前审批设置为 暂停状态
     *
     * @param flowPathDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 17:11
     **/
    private void interruptApproval(FlowPathDTO flowPathDTO) {
        // 1、更新现有审批流程状态为暂停
        flowPathMapper.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathDTO.getId())
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.YES.getCode())
                        .build()
        );

        // 2、回调业务层-将原审批暂停
        callbackExtend.callback(
                flowPathDTO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "interrupt",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        flowPathDTO.getTitle() + "已暂停",
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        flowPathDTO.getStatus()
                )
        );
    }

    /**
     * 正常撤回
     *
     * @param flowPathDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 17:07
     **/
    private void withdrawnPermit(FlowPathDTO flowPathDTO) {
        // 1、判断该撤回申请是否有关联 撤销目标审批流程 ，如果有，需要把目标流程状态设置为 进行中
        if (flowPathDTO.getWithdrawPathId() != 0) {
            flowPathDTO = flowPathService.getFlowPathDetail(flowPathDTO.getWithdrawPathId());
            proceedApproval(flowPathDTO);
        }
        // 2、获取当前审批步骤
        Optional<FlowPathStepDTO> flowPathStepDTO = flowPathDTO.getStepInfoList().stream().filter(v -> v.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
        if (!flowPathStepDTO.isPresent()) {
            throw new BsException("撤销目标流程失败");
        }
        FlowPathStepDTO withdrawFlowPathStepDTO = flowPathStepDTO.get();
        // 3、更新流程状态
        flowPathMapper.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathDTO.getId())
                        .updateTime(new Date())
                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
                        .build()
        );
        // 4、更新flow_path_step表
        flowPathStepService.update(
                FlowPathStepDAO.builder()
                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
                        .build(),
                GetFlowPathStepCondition.builder()
                        .flowPathId(flowPathDTO.getId())
                        .stepNo(withdrawFlowPathStepDTO.getStepNo())
                        .build()
        );

        // 5、添加历史数据（意义不大）
        String title = StringUtil.isBlank(flowPathDTO.getTitle()) ? (flowPathDTO.getFlowTitle() + "已撤回") : flowPathDTO.getTitle();
        FlowPathHistoryDAO flowPathHistoryDAO = FlowPathHistoryDAO.builder()
                .title(title)
                .flowPathId(flowPathDTO.getId())
                .uuid(flowPathDTO.getUuid())
                .stepId(withdrawFlowPathStepDTO.getId())
                .nextStepId(0)
                .action(ApproveConstant.flowPathAction.WITHDRAW.getCode())
                .reason("")
                .build();
        flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);

        // 7、更新 我的审批列表从 待处理 到 已处理
        flowPathListMapper.update(
                FlowPathListDAO.builder()
                        .type(ApproveConstant.flowPathListType.PROCESSED.getCode())
                        .build(),
                GetFlowPathListDAOListCondition.builder()
                        .flowPathId(flowPathDTO.getId())
                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
                        .build()
        );
        // 8. 通知回调
        callbackExtend.callback(
                flowPathDTO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "revocation",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        flowPathDTO.getStatus()
                )
        );

        // 拒绝回调
        callbackExtend.callback(flowPathDTO.getRefuseUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "revocation",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        ApproveConstant.flowPathStatus.WITHDRAWN.getCode()
                ));
    }

    /**
     * 恢复冻结流程
     *
     * @param flowPathDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 16:49
     **/
    private void proceedApproval(FlowPathDTO flowPathDTO) {
        if (Objects.isNull(flowPathDTO)) {
            return;
        }
        // 1、更新撤回目标流程的暂停状态为 恢复
        flowPathService.updateFlowPath(
                FlowPathDAO.builder()
                        .id(flowPathDTO.getId())
                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
                        .updateTime(new Date())
                        .build()
        );

        // 2、回调业务层-将原审批恢复
        callbackExtend.callback(
                flowPathDTO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "proceed",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        flowPathDTO.getTitle() + "已恢复",
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        flowPathDTO.getStatus()
                )
        );
    }

    /**
     * 审核通过
     *
     * @param flowPathDTO
     * @param nextStepInfo
     * @param passStepUuidMap
     * @param uuid
     * @param title
     * @param showName
     * @param reason
     * @param paramMap
     * @return void
     * <AUTHOR>
     * @date 2025/5/16 14:43
     **/
    private Map<String, Object> approvePass(FlowPathDTO flowPathDTO, FlowPathStepDTO thisStepInfo, FlowPathStepDTO nextStepInfo
            , Map<Integer, List<String>> passStepUuidMap, String uuid, String title, String showName, String reason, Map<String, Object> paramMap) {
        // 1.将当前审批人加入到已通过用户集合中
        List<String> passUuidList = passStepUuidMap.getOrDefault(thisStepInfo.getId(), new ArrayList<>());
        passUuidList.add(uuid);

        // 获取当前用户的代理关系信息
        AgentRelationshipDTO agentRelationship = getAgentRelationship(thisStepInfo, uuid);

        // 补充代理关系人ID
        passUuidList.add(agentRelationship.getAgentUserId());
        passUuidList.add(agentRelationship.getAssignUserId());

        passStepUuidMap.put(thisStepInfo.getId(), passUuidList);

        List<String> notificationUuidList = new ArrayList<>();
        //下一个审批人
        FlowPathStepNotificationDTO nextNotification = null;
        // 是否执行下一步， 依次和全部可能通过完本节点还没有执行完
        boolean toNext = true;
        // 2.全员通过和依次通过特殊处理
        if (ApproveConstant.flowPassAll.ALL.getCode() == thisStepInfo.getPassAll() ||
                ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == thisStepInfo.getPassAll()) {
            List<String> actualApprovers = new ArrayList<>();
            for (FlowPathStepNotificationDTO notification : thisStepInfo.getNotificationList()) {
                if (notification.getStatus() == ApproveConstant.flowPathStatus.TRANSFERRED.getCode()) {
                    continue;
                }

                // 排除代理用户（避免重复计算）
                JSONObject extra = JSONUtil.parseObj(notification.getExtraContent());
                if (extra.getInt("isAgent", 0) == 1) {
                    continue;
                }

                actualApprovers.add(notification.getUuid());
            }

            // 判断是否所有实际审批人都已通过
            for (String approver : actualApprovers) {
                if (!passUuidList.contains(approver)) {
                    toNext = false;

                    // 找到下一个待审批人（仅在依次通过模式下）
                    if (ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == thisStepInfo.getPassAll() &&
                            nextNotification == null) {

                        for (FlowPathStepNotificationDTO notification : thisStepInfo.getNotificationList()) {
                            if (notification.getUuid().equals(approver)) {
                                nextNotification = notification;
                                break;
                            }
                        }
                    }
                    break;
                }
            }
        }
        // 3.封装标题
        title = StringUtil.isBlank(title) ? (showName + " 通过了 " + flowPathDTO.getShowName() + "的" + getFlowTitle(flowPathDTO)) : title;

        // 4.区分是否执行下一步处理
        if (toNext) {
            // 执行下一步
            // 4.1 更新当前节点状态为完成
            flowPathStepService.update(
                    FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                    GetFlowPathStepCondition.builder().flowPathId(thisStepInfo.getFlowPathId()).stepNo(thisStepInfo.getStepNo()).build()
            );
            // 4.2 处理后续空节点
            nextStepInfo = null;
            for (FlowPathStepDTO stepInfo : flowPathDTO.getStepInfoList()) {
                if (stepInfo.getId() == thisStepInfo.getId()) {
                    stepInfo.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
                    continue;
                }
                if (ApproveConstant.flowPathStatus.WAITING.getCode() == stepInfo.getStatus()) {
                    if (CollectionUtil.isEmpty(stepInfo.getNotification()) && CollectionUtil.isEmpty(stepInfo.getCcNotification())) {
                        boolean result = execEmptyStepByConfig(flowPathDTO, paramMap, stepInfo);
                        if (!result) {
                            nextStepInfo = stepInfo;
                            break;
                        }
                    } else {
                        nextStepInfo = stepInfo;
                        break;
                    }
                }
            }
            // 4.3 没有待执行的节点，执行完成操作
            if (Objects.isNull(nextStepInfo)) {
                // 修改审批状态
                flowPathService.update(new LambdaUpdateWrapper<FlowPathEntity>()
                        .set(FlowPathEntity::getStatus, ApproveConstant.flowPathStatus.PASSED.getCode())
                        .set(FlowPathEntity::getUpdateTime, LocalDateTime.now())
                        .eq(FlowPathEntity::getId, flowPathDTO.getId()));

                // 通过回调前，通知回调
                callbackExtend.callback(
                        flowPathDTO.getApproverUrl(),
                        callbackExtend.createNotificationData(
                                flowPathDTO.getFlowId(),
                                flowPathDTO.getId(),
                                flowPathDTO.getUuid(),
                                "approve",
                                "pass",
                                JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                                title,
                                "",
                                JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                                JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                                JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                                flowPathDTO.getStatus()
                        )
                );
                //通过回调
                callbackExtend.callback(
                        flowPathDTO.getPassUrl(),
                        callbackExtend.createNotificationData(
                                flowPathDTO.getFlowId(),
                                flowPathDTO.getId(),
                                flowPathDTO.getUuid(),
                                "approve",
                                "pass",
                                JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                                title,
                                "",
                                JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                                JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                                JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                                ApproveConstant.flowPathAction.PASS.getCode()
                        )
                );
                // 这里是用于撤回需要审批时，审批完成后执行撤回操作
                if (flowPathDTO.getWithdrawPathId() != 0) {
                    // 正常撤回，直接撤回成功
                    withdrawnPermit(flowPathDTO);
                }
                paramMap.put(IS_APPROVE_COMPLETED, true);
                paramMap.put(IS_ENABLE_CC, true);
            } else {
                // 抄送
                paramMap.put(IS_ENABLE_CC, true);
                // 标记待审
                paramMap.put(NEXT_WAIT_STEP_INFO, nextStepInfo);
            }
            // 我的审批 从待处理 到 已处理
            GetFlowPathListDAOListCondition condition = GetFlowPathListDAOListCondition.builder()
                    .flowPathId(flowPathDTO.getId())
                    .type(ApproveConstant.flowPathListType.WAIT.getCode())
                    .build();
            if (ApproveConstant.flowPassAll.ALL.getCode() == thisStepInfo.getPassAll() || ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == thisStepInfo.getPassAll()) {
                condition.setUuid(uuid);
            }
            flowPathListMapper.update(FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(), condition);

            // 处理代理关系（更新flow_path_list）
            processAgentRelationship(flowPathDTO, agentRelationship);
        } else {
            // 不执行下一步
            nextStepInfo = thisStepInfo;
            // 我的审核 从 待处理 移动到 已处理
            flowPathListMapper.update(
                    FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                    GetFlowPathListDAOListCondition.builder().flowPathId(flowPathDTO.getId()).uuid(uuid).type(ApproveConstant.flowPathListType.WAIT.getCode()).build());

            //依次通过模式通知下一个审批人
            if (ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == thisStepInfo.getPassAll() && Objects.nonNull(nextNotification)) {
                paramMap.put(NEXT_WAIT_STEP_INFO, nextStepInfo);
                List<String> approvalUuidList = (List<String>) paramMap.get(PASS_APPROVAL_UUID_LIST);
                approvalUuidList.add(nextNotification.getUuid());
                paramMap.put(PASS_APPROVAL_UUID_LIST, approvalUuidList);
            }

            // 处理代理关系（更新flow_path_list）
            processAgentRelationship(flowPathDTO, agentRelationship);
        }
        // 5.添加执行历史
        flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO.builder()
                .title(title).flowPathId(flowPathDTO.getId())
                .uuid(uuid)
                .stepId(thisStepInfo.getId())
                .action(ApproveConstant.flowPathAction.PASS.getCode())
                .reason(reason).build());
        // 6.更新flow_path_step_notification表
        flowPathStepNotificationService.update(
                FlowPathStepNotificationDAO.builder()
                        .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                        .updateTime(new Date())
                        .build(),
                GetFlowPathStepNotificationCondition.builder()
                        .flowPathId(thisStepInfo.getFlowPathId())
                        .stepNo(thisStepInfo.getStepNo())
                        .uuid(uuid)
                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                        .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                        .build()
        );

        // 处理代理关系（更新flow_path_step_notification）
        processAgentRelationshipNotification(thisStepInfo, agentRelationship);

        paramMap.put(PASS_STEP_UUID_MAP, passStepUuidMap);
        return paramMap;
    }


    /**
     * 获取代理关系信息
     */
    private AgentRelationshipDTO getAgentRelationship(FlowPathStepDTO stepInfo, String uuid) {
        AgentRelationshipDTO relationship = new AgentRelationshipDTO();

        // 查找当前用户的通知记录，获取代理信息
        if (CollectionUtil.isNotEmpty(stepInfo.getNotificationList())) {
            for (FlowPathStepNotificationDTO notification : stepInfo.getNotificationList()) {
                if (notification.getUuid().equals(uuid)) {
                    // 解析extra_content中的代理信息
                    JSONObject extraContent = JSONUtil.parseObj(notification.getExtraContent());
                    if (extraContent.getInt("isAgent", 0) == 1) {
                        LocalDateTime agentExpirationTime = TimeUtil.parseLocalDateTime(notification.getAgentExpirationTime());
                        if (Objects.nonNull(agentExpirationTime) && LocalDateTime.now().isBefore(agentExpirationTime)) {
                            relationship.setAssignUserId(extraContent.getStr("assignUserId"));
                        }
                    } else if (StringUtils.isNotBlank(extraContent.getStr("userId"))
                            && !extraContent.getStr("userId").equals(uuid)) {
                        // 检查是否存在被代理的用户
                        for (FlowPathStepNotificationDTO possibleAgent : stepInfo.getNotificationList()) {
                            LocalDateTime possibleAgentExpirationTime = TimeUtil.parseLocalDateTime(notification.getAgentExpirationTime());
                            if (Objects.nonNull(possibleAgentExpirationTime) && LocalDateTime.now().isBefore(possibleAgentExpirationTime)) {
                                JSONObject agentExtra = JSONUtil.parseObj(possibleAgent.getExtraContent());
                                if (agentExtra.getInt("isAgent", 0) == 1
                                        && uuid.equals(agentExtra.getStr("assignUserId"))) {
                                    relationship.setAgentUserId(possibleAgent.getUuid());
                                    break;
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }

        return relationship;
    }

    /**
     * 处理代理关系相关的flow_path_list表更新
     */
    private void processAgentRelationship(FlowPathDTO flowPathDTO, AgentRelationshipDTO relationship) {
        String agentUserId = relationship.getAgentUserId();
        String assignUserId = relationship.getAssignUserId();

        // 同时更新代理用户或被代理用户的状态
        if (StringUtils.isNotBlank(agentUserId)) {
            flowPathListMapper.update(
                    FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                    GetFlowPathListDAOListCondition.builder()
                            .flowPathId(flowPathDTO.getId())
                            .uuid(agentUserId)
                            .type(ApproveConstant.flowPathListType.WAIT.getCode())
                            .build()
            );
        } else if (StringUtils.isNotBlank(assignUserId)) {
            flowPathListMapper.update(
                    FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                    GetFlowPathListDAOListCondition.builder()
                            .flowPathId(flowPathDTO.getId())
                            .uuid(assignUserId)
                            .type(ApproveConstant.flowPathListType.WAIT.getCode())
                            .build()
            );
        }
    }

    /**
     * 处理代理关系相关的flow_path_step_notification表更新
     */
    private void processAgentRelationshipNotification(FlowPathStepDTO stepInfo, AgentRelationshipDTO relationship) {
        String agentUserId = relationship.getAgentUserId();
        String assignUserId = relationship.getAssignUserId();

        // 同时更新代理用户或被代理用户的状态
        if (StringUtils.isNotBlank(agentUserId)) {
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(stepInfo.getFlowPathId())
                            .stepNo(stepInfo.getStepNo())
                            .uuid(agentUserId)
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
        } else if (StringUtils.isNotBlank(assignUserId)) {
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .status(ApproveConstant.flowPathStatus.PASSED.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(stepInfo.getFlowPathId())
                            .stepNo(stepInfo.getStepNo())
                            .uuid(assignUserId)
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
        }
    }


    /**
     * 封装各节点通过的用户
     * 如果设置了只用审核一次或者连续自动通过，根据此对象执行通过
     *
     * @param stepList
     * @return java.util.Map<java.lang.Integer, java.util.List < java.lang.String>>
     * <AUTHOR>
     * @date 2025/5/16 10:26
     **/
    private Map<Integer, List<String>> getPassStepUuidMap(List<FlowPathStepDTO> stepList) {
        Map<Integer, List<String>> passStepUuidMap = new HashMap<>();
        for (FlowPathStepDTO flowPathStepDTO : stepList) {
            List<String> passStepUuidList = passStepUuidMap.getOrDefault(flowPathStepDTO.getId(), new ArrayList<>());
            if (CollectionUtil.isNotEmpty(flowPathStepDTO.getNotificationList())) {
                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : flowPathStepDTO.getNotificationList()) {
                    if (ApproveConstant.flowPathStatus.PASSED.getCode() == flowPathStepNotificationDTO.getStatus()) {
                        passStepUuidList.add(flowPathStepNotificationDTO.getUuid());
                    }
                }
            }
            passStepUuidMap.put(flowPathStepDTO.getId(), passStepUuidList);
        }
        return passStepUuidMap;
    }

    /**
     * 审核权限验证
     *
     * @param thisStepInfo
     * @param uuid
     * @return void
     * <AUTHOR>
     * @date 2025/5/15 19:06
     **/
    private void approvePermissionVerification(FlowPathStepDTO thisStepInfo, String uuid) {
        if (CollectionUtil.isEmpty(thisStepInfo.getNotificationList())) {
            return;
        }
        boolean isNextPerson = Boolean.FALSE;
        boolean isAgentExpiration = Boolean.FALSE;
        if (ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() == thisStepInfo.getPassAll()) {
            // 依次审批  查验是否是当前步骤下一个待审
            for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                if (ApproveConstant.flowPathStatus.WAITING.getCode() == notifyOne.getStatus()) {
                    // 如果当前步骤待审批人是此人
                    if (notifyOne.getUuid().equals(uuid)) {
                        isNextPerson = Boolean.TRUE;
                    }
                    if (notifyOne.getExtraContent().containsKey("isClient")) {
                        isAgentExpiration = checkAgentExpiration(notifyOne.getAgentExpirationTime());
                        if (isAgentExpiration) {
                            break;
                        }
                        continue;
                    }
                    break;
                }
            }
        } else {
            //全员审批 or 单人审批
            for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
                //如果当前步骤其中一个审批人是此人，验证前两个是否和历史记录相符
                //全员审批和任一审批的判断依据都是是否在当前步骤下
                if (notifyOne.getUuid().equals(uuid)
                        && notifyOne.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
                    isAgentExpiration = checkAgentExpiration(notifyOne.getAgentExpirationTime());
                    if (isAgentExpiration) {
                        break;
                    }
                    isNextPerson = Boolean.TRUE;
                    break;
                }
            }
        }
        if (isAgentExpiration) {
            throw new BsException("当前时间暂无代理审批权限");
        }
        if (!isNextPerson) {
            throw new BsException("对不起，当前用户权限不符合当前规则");
        }
    }

    /**
     * 验证交办过期时间
     *
     * @param agentExpirationTime
     * @return
     */
    private Boolean checkAgentExpiration(String agentExpirationTime) {
        boolean isAgentExpiration = Boolean.FALSE;
        if (StringUtil.isNotEmpty(agentExpirationTime)) {
            LocalDateTime expirationTime = TimeUtil.parseLocalDateTime(agentExpirationTime);
            if (!Objects.isNull(expirationTime)) {
                LocalDateTime currentTime = LocalDateTime.now();
                if (expirationTime.isBefore(currentTime)) {
                    isAgentExpiration = Boolean.TRUE;
                }
            }
        }
        return isAgentExpiration;
    }

    /**
     * 空节点处理
     *
     * @param flowPathDTO
     * @return void
     * <AUTHOR>
     * @date 2025/5/15 19:06
     **/
    private Map<String, Object> emptyStepHandle(FlowPathDTO flowPathDTO, Map<String, Object> paramMap) {
        FlowPathStepDTO thisStepInfo = null;
        FlowPathStepDTO lastStepInfo = null;
        FlowPathStepDTO nextStepInfo = null;
        List<FlowPathStepDTO> stepInfoList = flowPathDTO.getStepInfoList();
        for (int i = 0; i < stepInfoList.size(); i++) {
            if (ApproveConstant.flowPathStatus.WAITING.getCode() == stepInfoList.get(i).getStatus()) {
                if (CollectionUtil.isEmpty(stepInfoList.get(i).getNotification()) && CollectionUtil.isEmpty(stepInfoList.get(i).getCcNotification())) {
                    boolean result = execEmptyStepByConfig(flowPathDTO, paramMap, stepInfoList.get(i));
                    if (result) {
                        continue;
                    }
                }
                thisStepInfo = stepInfoList.get(i);
                if ((i + 1) < stepInfoList.size()) {
                    nextStepInfo = stepInfoList.get(i + 1);
                }
                if (i > 0) {
                    lastStepInfo = stepInfoList.get(i - 1);
                }
                break;
            }
        }
        if (Objects.isNull(thisStepInfo)) {
            throw new BsException("审批流程已结束");
        }
        paramMap.put(THIS_STEP_INFO, thisStepInfo);
        paramMap.put(LAST_STEP_INFO, lastStepInfo);
        paramMap.put(NEXT_STEP_INFO, nextStepInfo);
        return paramMap;
    }

    /**
     * 根据配置处理空节点
     *
     * @param flowPathDTO
     * @param paramMap
     * @param stepInfo
     * @return boolean
     * <AUTHOR>
     * @date 2025/5/27 20:41
     **/
    private boolean execEmptyStepByConfig(FlowPathDTO flowPathDTO, Map<String, Object> paramMap, FlowPathStepDTO stepInfo) {
        Object obj = paramMap.get(FLOW_INFO);
        FlowEntity flowEntity = null;
        if (Objects.isNull(obj)) {
            flowEntity = flowMapper.selectById(flowPathDTO.getFlowId());
            if (Objects.nonNull(flowEntity)) {
                paramMap.put(FLOW_INFO, flowEntity);
            }
        } else {
            flowEntity = (FlowEntity) obj;
        }
        if (Objects.nonNull(flowEntity) && StringUtil.isNotEmpty(flowEntity.getExtraContent())
                && 1 == JSONUtil.parseObj(flowEntity.getExtraContent()).getInt("vacancyStatus")
                && 1 == stepInfo.getStepType()) {
            JSONObject vacancyMember = JSONUtil.parseObj(flowEntity.getExtraContent()).getJSONObject("vacancyMember");
            if (Objects.nonNull("vacancyMember") && StringUtil.isNotBlank(vacancyMember.getStr("msUserId"))) {
                String userId = vacancyMember.getStr("msUserId");
                FlowPathStepNotificationDAO flowPathStepNotificationDAO = flowPathStepNotificationService.addFlowPathStepNotification(flowPathDTO.getId(), stepInfo.getStepNo(), 1, 1, ApproveConstant.flowPathStatus.WAITING.getCode(), userId
                        , StepInfoNotificationDTO.builder().uuid(userId).userId(userId).uccId(vacancyMember.getStr("uccId")).avatar(vacancyMember.getStr("avatar")).showName(vacancyMember.getStr("showName")).companyId(vacancyMember.getStr("companyId")).build()
                        , 0);
                stepInfo.setNotificationList(Collections.singletonList(FlowPathStepNotificationDTO.create(flowPathStepNotificationDAO)));
                stepInfo.setNotification(stepInfo.getNotificationList());
            } else {
                flowPathStepService.update(
                        FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                        GetFlowPathStepCondition.builder().flowPathId(stepInfo.getFlowPathId()).stepNo(stepInfo.getStepNo()).build()
                );
                stepInfo.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
                return true;
            }
        } else {
            flowPathStepService.update(
                    FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
                    GetFlowPathStepCondition.builder().flowPathId(stepInfo.getFlowPathId()).stepNo(stepInfo.getStepNo()).build()
            );
            stepInfo.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
            return true;
        }
        return false;
    }

    /**
     * 创建审批流程
     * 创建审批实例 不需要验证权限
     * 创建审批流时，直接下一步:相当于通过
     *
     * @param flowPathDTO
     * @param showName
     * @param uuid
     * @param stepId
     * @param reason
     * @param title
     * @param appChannelId
     * @param dataChannelId
     * @return void
     * <AUTHOR>
     * @date 2025/5/27 9:45
     **/
    public void approveCreate(FlowPathDTO flowPathDTO, String showName, String uuid, Integer stepId, String reason, String title, String appChannelId, String dataChannelId) {
        String flowTitle = getFlowTitle(flowPathDTO);
        title = StringUtil.isBlank(title) ? (showName + " 创建了 " + flowTitle + "申请") : title;
        // 1. 存储操作历史
        flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO
                .builder()
                .title(title)
                .flowPathId(flowPathDTO.getId())
                .uuid(uuid)
                .stepId(0)
                .nextStepId(stepId)
                .action(ApproveConstant.flowPathAction.CREATE.getCode())
                .reason(reason)
                .build());

        flowTitle = configTitle(flowTitle, JSONUtil.toJsonStr(flowPathDTO.getTempShowData()), flowPathDTO.getFlowId(), flowPathDTO.getShowName(), JSONUtil.toJsonStr(flowPathDTO.getExtraContent()), flowPathDTO.getTitle(), flowPathDTO.getCreateTime());
        // 2.存储到我的审批-已提交列表
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO
                        .builder()
                        .title(flowTitle)
                        .flowPathId(flowPathDTO.getId())
                        .uuid(uuid)
                        .type(1)
                        .appChannelId(appChannelId)
                        .dataChannelId(dataChannelId)
                        .extraContent(JSONUtil.toJsonStr(CollectionUtil.isNotEmpty(flowPathDTO.getExtraContent()) ? flowPathDTO.getExtraContent() : Collections.emptyMap()))
                        .build()
        );
    }

    /**
     * 获取审批标题
     *
     * @param flowPathDTO
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/27 9:46
     **/
    private static String getFlowTitle(FlowPathDTO flowPathDTO) {
        return StringUtil.isBlank(flowPathDTO.getTitle()) ? flowPathDTO.getFlowTitle() : flowPathDTO.getTitle();
    }

    /**
     * 复制晓顺配置标题方法
     *
     * @param originalTitle
     * @param tempShowData
     * @param flowId
     * @param showName
     * @param extraContent
     * @param flowPathTitle
     * @param createTime
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/5/15 19:35
     **/
    private String configTitle(String originalTitle, String tempShowData, String flowId, String showName, String extraContent, String flowPathTitle, String createTime) {
        if (StringUtil.isEmpty(tempShowData)) {
            return originalTitle;
        }
        //合并代码
        FlowEntity flowDAO = flowMapper.selectById(flowId);
        if (Objects.isNull(flowDAO)) {
            return originalTitle;
        }
        TemplateEntity templateEntity = templateMapper.selectById(flowDAO.getTempId());
        if (Objects.isNull(templateEntity) || StringUtil.isEmpty(templateEntity.getTitleConfig())) {
            return originalTitle;
        }
        List<Map<String, Object>> titleConfig = StringUtil.jsonDecode(templateEntity.getTitleConfig(), List.class);
        //合并代码
        Map<String, String> tempValueMap = TitleConfigUtil.getTempShowDataMap(new JSONArray(tempShowData));
        log.info("configTitle ->tempValueMap:{}", tempValueMap);
        StringBuilder title = new StringBuilder();
        for (Map<String, Object> config : titleConfig) {
            if (Objects.equals(config.get("type"), "2")) {
                String value = tempValueMap.getOrDefault(config.getOrDefault("val", ""), "");
                title.append(value);
            } else if (Objects.equals(config.get("type"), "3")) {
                title.append(config.getOrDefault("val", ""));
            } else if (Objects.equals(config.get("type"), "1")) {
                String val = String.valueOf(config.getOrDefault("val", ""));
                if (val.equals("1")) {
                    title.append(showName);
                } else if (val.equals("2")) {
                    title.append(createTime);
                } else if (val.equals("3")) {
                    title.append(createTime);
                } else if (val.equals("4")) {
                    if (StringUtil.isNotEmpty(extraContent)) {
                        JSONObject extraContentJson = JSONUtil.parseObj(extraContent);
                        title.append(extraContentJson.getStr("companyName", ""));
                    }
                } else if (val.equals("5")) {
                    title.append(flowPathTitle);
                }
            }
        }
        if (StringUtil.isNotEmpty(title.toString())) {
            return title.toString();
        }
        return originalTitle;
    }

    /**
     * 判断是否自动驳回
     *
     * @param flowPathDTO
     * @param uuid
     * @return boolean
     * <AUTHOR>
     * @date 2025/5/15 13:39
     **/
    public boolean approveAutoReject(FlowPathDTO flowPathDTO, String uuid) {
        // 1. 获取待审核节点集合
        List<FlowPathStepDTO> waitStepList = flowPathDTO.getStepInfoList().stream().filter(Objects::nonNull).filter(item -> item.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(waitStepList)) {
            return false;
        }
        // 2.判断当前节点是否配置了自动驳回
        FlowPathStepDTO currentStep = waitStepList.get(0);
        if (Objects.nonNull(currentStep) && CollectionUtil.isNotEmpty(currentStep.getExtraContent())
                && 1 == (Integer) currentStep.getExtraContent().getOrDefault("isAutoReject", 0)) {
            // 3.执行拒绝
            approveReject(currentStep, flowPathDTO, "系统分支配置", uuid, "系统分支配置自动驳回", true);
            return true;
        }
        return false;
    }

    /**
     * 审批驳回
     *
     * @param currentStep  当前节点
     * @param flowPathDTO  审批详情
     * @param showName     操作人名称
     * @param uuid         操作人ID
     * @param reason       拒绝原因
     * @param isAutoReject 是否是自动拒绝，自动拒绝不用改节点用户审核状态
     * @return void
     * <AUTHOR>
     * @date 2025/5/15 15:02
     **/
    private void approveReject(FlowPathStepDTO currentStep, FlowPathDTO flowPathDTO, String showName, String uuid, String reason, Boolean isAutoReject) {
        String title = showName + " 驳回了 " + flowPathDTO.getShowName() + "的" + getFlowTitle(flowPathDTO);
        // 1.修改节点状态
        flowPathStepService.update(
                FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.REFUSED.getCode()).build(),
                GetFlowPathStepCondition.builder().flowPathId(currentStep.getFlowPathId()).stepNo(currentStep.getStepNo()).build()
        );

        // 2.修改审批状态
        flowPathService.update(new LambdaUpdateWrapper<FlowPathEntity>()
                .set(FlowPathEntity::getStatus, ApproveConstant.flowPathStatus.REFUSED.getCode())
                .set(FlowPathEntity::getUpdateTime, LocalDateTime.now())
                .eq(FlowPathEntity::getId, flowPathDTO.getId()));
        if (Objects.isNull(isAutoReject) || !isAutoReject) {
            // 3.更新审核人状态, 自动拒绝不需要
            flowPathStepNotificationService.update(
                    FlowPathStepNotificationDAO.builder()
                            .reason(reason)
                            .status(ApproveConstant.flowPathStatus.REFUSED.getCode())
                            .updateTime(new Date())
                            .build(),
                    GetFlowPathStepNotificationCondition.builder()
                            .flowPathId(flowPathDTO.getId())
                            .stepNo(currentStep.getStepNo())
                            .uuid(uuid)
                            .status(ApproveConstant.flowPathStatus.WAITING.getCode())
                            .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
                            .build()
            );
            // 4.将该审批从“我的审批”-“待处理”列表 到 “已处理“列表
            flowPathListMapper.update(
                    FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
                    GetFlowPathListDAOListCondition.builder().flowPathId(flowPathDTO.getId()).type(ApproveConstant.flowPathListType.WAIT.getCode()).build()
            );
        } else {
            // 4.将该审批在所有审核人的“我的审批”-“待处理”列表删除
            flowPathListMapper.deleteByFlowPathIdAndType(RemoveFlowPathListCondition.builder()
                    .flowPathId(flowPathDTO.getId())
                    .type(ApproveConstant.flowPathListType.WAIT.getCode())
                    .build());
            title = "系统驳回";
            reason = "系统分支配置自动驳回";
        }

        // 5.发起通知回调 通知发起人
        callbackExtend.callback(
                flowPathDTO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "refuse",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        flowPathDTO.getStatus()
                )
        );
        // 6.拒绝回调
        callbackExtend.callback(flowPathDTO.getRefuseUrl(),
                callbackExtend.createNotificationData(
                        flowPathDTO.getFlowId(),
                        flowPathDTO.getId(),
                        flowPathDTO.getUuid(),
                        "approve",
                        "refuse",
                        JSONUtil.toJsonStr(flowPathDTO.getTempShowData()),
                        title,
                        "",
                        JSONUtil.toJsonStr(flowPathDTO.getTempData()),
                        JSONUtil.toJsonStr(flowPathDTO.getStepInfoList()),
                        JSONUtil.toJsonStr(flowPathDTO.getExtraContent()),
                        ApproveConstant.flowPathStatus.REFUSED.getCode()
                )
        );
        // 7.存储操作历史
        flowPathHistoryMapper.addFlowPathHistory(FlowPathHistoryDAO
                .builder()
                .title(title)
                .flowPathId(flowPathDTO.getId())
                .uuid(uuid)
                .stepId(0)
                .nextStepId(currentStep.getId())
                .action(1)
                .reason(reason)
                .build());

        // 当设置了驳回后通知已审核人，那么就对已经审核通过的人进行通知
        if (1 == flowPathDTO.getIsRejectNotice()) {
            List<String> passUuidList = flowPathDTO.getStepInfoList().stream().filter(v -> ApproveConstant.flowPathStatus.PASSED.getCode() == v.getStatus())
                    .map(FlowPathStepDTO::getNotification).flatMap(Collection::stream).map(FlowPathStepNotificationDTO::getUuid).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(passUuidList)) {
                Map<String, Object> callbackRequestMap = new HashMap<>();
                callbackRequestMap.put("flowId", flowPathDTO.getFlowId());
                callbackRequestMap.put("flowPathId", flowPathDTO.getId());
                callbackRequestMap.put("userIdList", passUuidList);
                callbackRequestMap.put("action", "approve");
                callbackRequestMap.put("event", "refuse");
                callbackRequestMap.put("title", title);
                callbackExtend.callback(flowPathDTO.getApproverUrl(), callbackRequestMap);
            }
        }
        if (Objects.isNull(isAutoReject) || !isAutoReject) {
            if (flowPathDTO.getWithdrawPathId() != 0) {
                // 恢复    这里是为了撤销需要审批的时候，审批拒绝撤销申请，则去掉原审批冻结状态，让其继续执行
                FlowPathDTO withdrawFlowPathDTO = flowPathService.getFlowPathDetail(flowPathDTO.getWithdrawPathId());
                proceedApproval(withdrawFlowPathDTO);
            }
        }
    }
}
