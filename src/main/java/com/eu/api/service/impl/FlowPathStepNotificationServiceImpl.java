package com.eu.api.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.domain.condition.GetFlowPathStepNotificationCondition;
import com.eu.api.domain.dao.FlowPathStepNotificationDAO;
import com.eu.api.domain.dto.FlowPathStepNotificationDTO;
import com.eu.api.domain.dto.StepInfoNotificationDTO;
import com.eu.api.domain.entity.FlowPathListEntity;
import com.eu.api.domain.entity.FlowPathStepEntity;
import com.eu.api.domain.ms.UpdateStepNotificationRequest;
import com.eu.api.mapper.FlowPathListMapper;
import com.eu.api.mapper.FlowPathStepMapper;
import com.eu.api.mapper.FlowPathStepNotificationMapper;
import com.eu.api.service.FlowPathListService;
import com.eu.api.service.FlowPathStepNotificationService;
import com.eu.common.context.ServletContext;
import com.eu.common.exception.BsException;
import com.eu.common.util.StringUtil;
import com.eu.common.util.TimeUtil;
import com.mssdk.approve.dto.FlowPathStepDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class FlowPathStepNotificationServiceImpl implements FlowPathStepNotificationService {

    @Autowired
    private FlowPathStepNotificationMapper flowPathStepNotificationMapper;

    @Autowired
    private FlowPathListMapper flowPathListMapper;

    @Autowired
    private FlowPathStepMapper flowPathStepMapper;

    /**
     * @param flowPathId         流程ID
     * @param stepNo             节点
     * @param sort               顺序
     * @param type               操作类型
     * @param status             状态
     * @param uuid               用户ID
     * @param notificationDTO    目标节点
     * @param fromNotificationId 来源ID
     * @return
     */
    @Override
    public FlowPathStepNotificationDAO addFlowPathStepNotification(int flowPathId, int stepNo, int sort, int type, int status, String uuid, StepInfoNotificationDTO notificationDTO, int fromNotificationId) {
        JSONObject jsonObject = JSONUtil.parseObj(notificationDTO);
        jsonObject.remove("extraContent");
        Map<String, Object> extraContent = notificationDTO.getExtraContent();
        if (Objects.nonNull(extraContent)) {
            jsonObject.putAll(extraContent);
        }
        Date agentExpirationTime = TimeUtil.string2Date(notificationDTO.getAgentExpirationTime());
        FlowPathStepNotificationDAO flowPathStepNotificationDAO = FlowPathStepNotificationDAO.builder()
                .flowPathId(flowPathId)
                .stepNo(stepNo)
                .sort(sort)
                .type(type)
                .status(status)
                .uuid(uuid)
                .extraContent(jsonObject.toString())
                .fromNotificationId(fromNotificationId)
                .createTime(new Date())
                .updateTime(new Date())
                .agentExpirationTime(Optional.ofNullable(agentExpirationTime)
                        .orElse(TimeUtil.getMaxDate()))
                .build();
        int i = flowPathStepNotificationMapper.addFlowPathStepNotification(flowPathStepNotificationDAO);
        if (i < 1) {
            throw new BsException("添加审批流步骤审批人失败");
        }

        return flowPathStepNotificationDAO;
    }

    @Override
    public List<FlowPathStepNotificationDTO> getFlowPathStepNotificationList(GetFlowPathStepNotificationCondition condition) {
        List<FlowPathStepNotificationDAO> flowPathStepNotificationDAOList = flowPathStepNotificationMapper.getFlowPathStepNotificationList(condition);
        return flowPathStepNotificationDAOList.stream().map(FlowPathStepNotificationDTO::create).collect(Collectors.toList());
    }

    @Override
    public void update(FlowPathStepNotificationDAO flowPathStepNotificationDAO, GetFlowPathStepNotificationCondition condition) {
        if (flowPathStepNotificationDAO.getUpdateTime() == null) {
            flowPathStepNotificationDAO.setUpdateTime(new Date());
        }
        // 测试说 如果发起人是 同时是审核人，需要更新 该人员的 审核状态
        flowPathStepNotificationMapper.update(flowPathStepNotificationDAO, condition);
//        if (flowPathStepNotificationMapper.update(flowPathStepNotificationDAO, condition) <= 0) {
//            throw new FlowPathException("50001", "更新失败");
//        }
    }

    /**
     * 删除办理人
     *
     * @param id 主键序号
     * @return
     */
    @Override
    public int deleteById(Integer id) {
        if (Objects.isNull(id) || id <= 0) {
            return 0;
        }
        int delete = flowPathStepNotificationMapper.deleteId(id);
        if (delete <= 0) {
            throw new BsException("删除失败");
        }
        return delete;
    }

    /**
     * 根据流程ID和步骤删除对应的办理人
     *
     * @param flowPathId
     * @param stepNo
     * @return
     */
    @Override
    public int deleteByflowPathIdAndStepNo(Integer flowPathId, Integer stepNo, List<Integer> typeList) {
        if (Objects.isNull(flowPathId) || Objects.isNull(stepNo)) {
            throw new BsException("删除节点办理人审批ID或");
        }
        return flowPathStepNotificationMapper.deleteByFlowPathIdAndStepNo(flowPathId, stepNo, typeList);
    }

    /**
     * 强制覆盖流程办理人
     *
     * @param updateStepNotificationRequest
     * @return
     */
    @Override
    public Map<String, List<String>> coverNotification(UpdateStepNotificationRequest updateStepNotificationRequest) {
        Integer flowPathId = updateStepNotificationRequest.getFlowPathId();
        Integer stepNo = updateStepNotificationRequest.getStepNo();
        List<StepInfoNotificationDTO> stepInfoNotificationList = updateStepNotificationRequest.getStepInfoNotificationList();
        List<StepInfoNotificationDTO> stepInfoCCNotificationList = updateStepNotificationRequest.getStepInfoCCNotificationList();

        List<Integer> typeList = new ArrayList<>();
        if (Objects.nonNull(stepInfoCCNotificationList) && !stepInfoCCNotificationList.isEmpty()) {
            typeList.add(ApproveConstant.flowPathStepNotificationType.CC.getCode());
        }
        List<String> addUuidList = Collections.emptyList();
        List<String> delUuidList = Collections.emptyList();
        if (Objects.nonNull(updateStepNotificationRequest.getIsNextStep()) && updateStepNotificationRequest.getIsNextStep()) {
            List<FlowPathStepNotificationDAO> flowPathStepNotificationDAOList = flowPathStepNotificationMapper.selectList(new LambdaQueryWrapper<FlowPathStepNotificationDAO>()
                    .eq(FlowPathStepNotificationDAO::getFlowPathId, flowPathId)
                    .eq(FlowPathStepNotificationDAO::getStepNo, stepNo)
                    .in(CollectionUtil.isNotEmpty(typeList), FlowPathStepNotificationDAO::getType, typeList)
            );
            if (CollectionUtil.isNotEmpty(stepInfoNotificationList)) {
                if (CollectionUtil.isEmpty(flowPathStepNotificationDAOList)) {
                    addUuidList = stepInfoNotificationList.stream().map(StepInfoNotificationDTO::getUuid).collect(Collectors.toList());
                } else {
                    List<String> newUuidList = stepInfoNotificationList.stream().map(StepInfoNotificationDTO::getUuid).collect(Collectors.toList());
                    List<String> oldUuidList = flowPathStepNotificationDAOList.stream().map(FlowPathStepNotificationDAO::getUuid).collect(Collectors.toList());
                    addUuidList = newUuidList.stream().filter(v -> !oldUuidList.contains(v)).collect(Collectors.toList());
                    delUuidList = oldUuidList.stream().filter(v -> !newUuidList.contains(v)).collect(Collectors.toList());
                }
            } else {
                if (CollectionUtil.isNotEmpty(flowPathStepNotificationDAOList)) {
                    delUuidList = stepInfoNotificationList.stream().map(StepInfoNotificationDTO::getUuid).collect(Collectors.toList());
                }
            }
        }

        /*
         * 清除对应的审批人
         */
        deleteByflowPathIdAndStepNo(flowPathId, stepNo, typeList);

        /*
         * 设置新的审批人
         */
        int sort = 1;
        boolean filterAddUuid = false;
        if (Objects.nonNull(stepInfoNotificationList) && !stepInfoNotificationList.isEmpty()) {
            for (StepInfoNotificationDTO stepInfoNotificationDTO : stepInfoNotificationList) {
                if (stepInfoNotificationDTO.getExtraContent().containsKey("isAgent") &&
                        Integer.parseInt(stepInfoNotificationDTO.getExtraContent().get("isAgent").toString()) != 0) {
                    int isAgent = Integer.parseInt(stepInfoNotificationDTO.getExtraContent().get("isAgent").toString());
                    if (isAgent != 0) {
                        sort = sort - 1;
                        filterAddUuid = true;
                    }
                    addFlowPathStepNotification(
                            flowPathId,
                            stepNo,
                            sort,
                            Objects.isNull(stepInfoNotificationDTO.getType()) ? ApproveConstant.flowPathStepNotificationType.APPROVE.getCode() : stepInfoNotificationDTO.getType(),
                            Objects.isNull(stepInfoNotificationDTO.getStatus()) ? ApproveConstant.flowPathStatus.WAITING.getCode() : stepInfoNotificationDTO.getStatus(),
                            stepInfoNotificationDTO.getUuid(),
                            stepInfoNotificationDTO,
                            0
                    );
                } else {
                    addFlowPathStepNotification(
                            flowPathId,
                            stepNo,
                            sort,
                            Objects.isNull(stepInfoNotificationDTO.getType()) ? ApproveConstant.flowPathStepNotificationType.APPROVE.getCode() : stepInfoNotificationDTO.getType(),
                            Objects.isNull(stepInfoNotificationDTO.getStatus()) ? ApproveConstant.flowPathStatus.WAITING.getCode() : stepInfoNotificationDTO.getStatus(),
                            stepInfoNotificationDTO.getUuid(),
                            stepInfoNotificationDTO,
                            0
                    );
                }
                sort++;
            }
        }

        if (Objects.nonNull(stepInfoCCNotificationList) && !stepInfoCCNotificationList.isEmpty()) {
            for (StepInfoNotificationDTO stepInfoNotificationDTO : stepInfoCCNotificationList) {
                addFlowPathStepNotification(
                        flowPathId,
                        stepNo,
                        sort,
                        Objects.isNull(stepInfoNotificationDTO.getType()) ? ApproveConstant.flowPathStepNotificationType.CC.getCode() : stepInfoNotificationDTO.getType(),
                        Objects.isNull(stepInfoNotificationDTO.getStatus()) ? ApproveConstant.flowPathStatus.WAITING.getCode() : stepInfoNotificationDTO.getStatus(),
                        stepInfoNotificationDTO.getUuid(),
                        stepInfoNotificationDTO,
                        0
                );
                sort++;
            }
        }
        if(filterAddUuid){
            List<String> currentUuidList = new ArrayList<>();
            if (!CollectionUtil.isNotEmpty(stepInfoNotificationList)) {
                typeList.add(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode());
                QueryWrapper<FlowPathStepEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("flow_path_id", flowPathId)
                        .eq("status", 0)
                        .orderByAsc("id")
                        .last("LIMIT 1");
                FlowPathStepEntity flowPathStepEntity = flowPathStepMapper.selectOne(queryWrapper);
                if (Objects.nonNull(flowPathStepEntity)) {
                    if(flowPathStepEntity.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()){
                        currentUuidList = stepInfoNotificationList.stream()
                                .filter(Objects::nonNull)
                                .filter(dto -> StringUtil.isNotEmpty(dto.getUuid()))
                                .filter(dto -> dto.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode())
                                .findFirst()
                                .map(dto -> Collections.singletonList(dto.getUuid()))
                                .orElse(Collections.emptyList());
                    } else {
                        currentUuidList = stepInfoNotificationList.stream()
                                .filter(Objects::nonNull)
                                .filter(dto -> StringUtil.isNotEmpty(dto.getUuid()))
                                .filter(dto -> dto.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode())
                                .map(StepInfoNotificationDTO::getUuid)
                                .collect(Collectors.toList());
                    }
                }
            }
            if(CollectionUtil.isNotEmpty(currentUuidList)){
                addUuidList.retainAll(currentUuidList);
            }
        }
        return Map.of("addUuidList", addUuidList, "delUuidList", delUuidList);
    }

    @Override
    public void batchUpdateNotifications(List<FlowPathStepNotificationDAO> toUpdateNotifications) {
        toUpdateNotifications.stream().forEach(item -> {
            if (item.getUpdateTime() == null) {
                item.setUpdateTime(new Date());
            }
        });
        try {
            flowPathStepNotificationMapper.batchUpdateNotifications(toUpdateNotifications);
        } catch (Exception e) {
            throw new BsException("批量更新失败");
        }
    }
}
