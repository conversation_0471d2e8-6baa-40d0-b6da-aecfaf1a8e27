package com.eu.api.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.eu.api.constant.ApproveConstant;
import com.eu.api.domain.condition.GetFlowCondition;
import com.eu.api.domain.condition.GetStepCondition;
import com.eu.api.domain.condition.GetTemplateCondition;
import com.eu.api.domain.dao.*;
import com.eu.api.domain.dto.*;
import com.eu.api.domain.dto.flow.Step;
import com.eu.api.domain.entity.FlowEntity;
import com.eu.api.domain.ms.MsFlowAddRequest;
import com.eu.api.domain.ms.MsFlowUpdateRequest;
import com.eu.api.domain.ms.MsStepListRequest;
import com.eu.api.domain.order.GetStepOrder;
import com.eu.api.mapper.*;
import com.eu.api.service.BPService;
import com.eu.api.service.FlowService;
import com.eu.api.service.StepService;
import com.eu.api.service.extend.CallbackExtend;
import com.eu.api.service.extend.FlowServiceExtend;
import com.eu.api.service.extend.StepServiceExtend;
import com.eu.common.exception.BsException;
import com.eu.common.util.FlowBuilderUtil;
import com.eu.common.util.StringUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FlowServiceImpl extends ServiceImpl<FlowMapper, FlowEntity> implements FlowService {

    @Autowired
    private FlowMapper flowMapper;
    //    @Autowired
//    private FlowPathMapper flowPathMapper;
//    @Autowired
//    private FlowPathHistoryMapper flowPathHistoryMapper;
    @Autowired
    private FlowPathListMapper flowPathListMapper;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private StepService stepService;
    //    @Autowired
//    private FlowPathStepService flowPathStepService;
//    @Autowired
//    private FlowPathStepNotificationService flowPathStepNotificationService;
    @Autowired
    private FlowServiceExtend flowServiceExtend;
    //    @Autowired
//    private FlowPathService flowPathService;
    @Autowired
    private StepServiceExtend stepServiceExtend;
    @Autowired
    private StepMapper stepMapper;
    @Autowired
    private CallbackExtend callbackExtend;
    @Autowired
    private BusinessMapper businessMapper;
    @Autowired
    private BPService bpService;

    /**
     * 创建流程
     *
     * @param request
     * @return
     */
    @Override
    public FlowDAO addFlow(MsFlowAddRequest request) {
        FlowDAO flowDAO = FlowDAO.create(request);
        flowDAO.setId(StringUtil.createUuid());
        if (flowMapper.addFlow(flowDAO) <= 0) {
            throw new BsException("添加审批流失败");
        }
        //插入步骤 老方式 childTrue childFalse 电商用
        if (request.getStepInfo() != null) {
            StepInfoDTO stepInfoDTO = StringUtil.jsonDecode(JSONUtil.toJsonStr(request.getStepInfo()), StepInfoDTO.class);
            stepService.createStep(stepInfoDTO, flowDAO.getId());
        }
        // 步骤列表，为了能兼容 第一步就是多分支的条件情况
        if (request.getStepList() != null && !request.getStepList().isEmpty()) {
            List<StepInfoDTO> stepInfoDTOList = JSONUtil.toList(JSONUtil.toJsonStr(request.getStepList()), StepInfoDTO.class);
            stepService.saveStepList(stepInfoDTOList, flowDAO.getId());
        }
        //获取最新数据
        flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(flowDAO.getId())
                .build());
        return flowDAO;
    }

    /**
     * 插入审批流
     *
     * @param request
     * @return
     */
    public FlowDAO addFlowByStep(MsFlowAddRequest request) {
        FlowDAO flowDAO = FlowDAO.create(request);
        flowDAO.setId(StringUtil.createUuid());
        if (flowMapper.addFlow(flowDAO) <= 0) {
            throw new BsException("添加审批流失败");
        }
        //获取最新数据
        flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(flowDAO.getId())
                .build());
        return flowDAO;
    }


    public FlowDAO addFlowByStep(FlowDAO flowDAO) {
//        flowMapper.updateFlow(flowDAO);
        FlowEntity flowEntity = new FlowEntity();
        BeanUtils.copyProperties(flowDAO, flowEntity);
        flowMapper.update(flowEntity, new QueryWrapper<FlowEntity>().eq("id", flowDAO.getId()));
        //获取最新数据
        flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(flowDAO.getId())
                .build());
        return flowDAO;
    }


    @Override
    public FlowDAO updateFlowByStep(MsFlowUpdateRequest request) {
        FlowDAO flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(request.getId())
                .build());
        if (flowDAO == null) {
            throw new BsException("审批流不存在");
        }
        flowDAO = FlowDAO.create(request);
        flowDAO.setUpdateTime(new Date());
        FlowEntity flowEntity = new FlowEntity();
        BeanUtils.copyProperties(flowDAO, flowEntity);
//        if (flowMapper.updateFlow(flowDAO) <= 0) {
//            throw new BsException("修改审批流失败");
//        }
        if (flowMapper.update(flowEntity, new QueryWrapper<FlowEntity>().eq("id", flowDAO.getId())) <= 0) {
            throw new BsException("修改审批流失败");
        }

        //获取最新数据
        flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(flowDAO.getId())
                .build());
        return flowDAO;
    }


    /**
     * 修改流程
     *
     * @param request
     * @return
     */
    @Override
    public FlowDAO updateFlow(MsFlowUpdateRequest request) {
        FlowDAO flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(request.getId())
                .build());
        if (flowDAO == null) {
            throw new BsException("审批流不存在");
        }
        flowDAO = FlowDAO.create(request);
        flowDAO.setUpdateTime(new Date());
        if (flowMapper.updateFlow(flowDAO) <= 0) {
            throw new BsException("修改审批流失败");
        }
        //插入步骤
        if (request.getStepInfo() != null) {
            StepInfoDTO stepInfoDTO = StringUtil.jsonDecode(JSONUtil.toJsonStr(request.getStepInfo()), StepInfoDTO.class);
            stepService.createStep(stepInfoDTO, flowDAO.getId());
        }
        // 步骤列表，为了能兼容第一步就是多分支的条件情况
        if (request.getStepList() != null && !request.getStepList().isEmpty()) {
            List<StepInfoDTO> stepInfoDTOList = JSONUtil.toList(JSONUtil.toJsonStr(request.getStepList()), StepInfoDTO.class);
            stepService.saveStepList(stepInfoDTOList, flowDAO.getId());
        }
        //获取最新数据
        flowDAO = flowMapper.getFlow(GetFlowCondition.builder()
                .flowId(flowDAO.getId())
                .build());
        return flowDAO;
    }

    /**
     * 获取审批流详情
     *
     * @param condition
     * @return
     */
    @Override
    public FlowDTO getFlow(GetFlowCondition condition) {
        FlowDAO flowDAO = flowMapper.getFlow(condition);
        if (Objects.isNull(flowDAO)) {
            return null;
        }
        FlowDTO flowDTO = FlowDTO.create(flowDAO);
        if (!StringUtil.isEmpty(flowDAO.getCcNotification())) {
            flowDTO.setCcNotification(JSONUtil.toList(flowDAO.getCcNotification(), StepInfoNotificationDTO.class));
        }
        //获取模板
        TemplateDAO templateDAO = templateMapper.getTemplate(GetTemplateCondition.builder().id(flowDAO.getTempId()).build());
        if (templateDAO != null) {
            flowDTO.setTemplate(TemplateDTO.create(templateDAO));
        }
        //获取步骤
        List<StepDAO> stepDAOList = stepService.getStepList(
                GetStepCondition.builder().flowId(flowDAO.getId()).build(),
                GetStepOrder.builder().id("ASC").build()
        );

        flowDTO.setStepInfoList(stepDAOList.stream().map(StepInfoDTO::create).sorted(Comparator.comparing(StepInfoDTO::getId).reversed()).collect(Collectors.toList()));

        flowDTO.setStepInfo(getStepTree(stepDAOList));
        return flowDTO;
    }

    /**
     * V2审批详情
     *
     * @param condition
     * @return
     */
    @Override
    public FlowDTO getFlowStep(GetFlowCondition condition) {
        FlowDAO flowDAO = flowMapper.getFlow(condition);
        if (Objects.isNull(flowDAO)) {
            return null;
        }
        // 如果发现当前节点的customize是null 那么他极有可能是一个老节点
        if (flowDAO.getCustomize() == null || flowDAO.getCustomize().isEmpty()) {
            //获取节点步骤
            List<Step> stepList = businessMapper.selectList(new QueryWrapper<Step>().eq("flow_id", flowDAO.getId()));
            // 构建工具类
            FlowBuilderUtil builder = new FlowBuilderUtil();
            List<Map<String, Object>> flow = builder.buildFlow(stepList);
            String json = null;
            // 如果构建树返回null
            if (Objects.isNull(flow)) {
                // 可能是一个没有id的节点
                flowDAO.setCustomize(stepList.toString());
            } else {
                try {
                    json = new ObjectMapper().writerWithDefaultPrettyPrinter().writeValueAsString(flow);
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
                flowDAO.setCustomize(json);
            }

        }
        FlowDTO flowDTO = FlowDTO.create(flowDAO);
        if (!StringUtil.isEmpty(flowDAO.getCcNotification())) {
            flowDTO.setCcNotification(JSONUtil.toList(flowDAO.getCcNotification(), StepInfoNotificationDTO.class));
        }
        //获取模板
        TemplateDAO templateDAO = templateMapper.getTemplate(GetTemplateCondition.builder().id(flowDAO.getTempId()).build());
        if (templateDAO != null) {
            flowDTO.setTemplate(TemplateDTO.create(templateDAO));
        }
        // 取消oa方面 返回stepInfo信息
//        //获取步骤
//        List<StepDAO> stepDAOList = stepService.getStepList(
//                GetStepCondition.builder().flowId(flowDAO.getId()).build(),
//                GetStepOrder.builder().id("ASC").build()
//        );
//
//        flowDTO.setStepInfo(getStepTree(stepDAOList));
        return flowDTO;
    }

    @Override
    public StepInfoDTO getStepTree(String flowId) {
        List<StepDAO> stepDAOList = stepService.getStepList(
                GetStepCondition.builder().flowId(flowId).build(),
                GetStepOrder.builder().id("ASC").build()
        );
        return getStepTree(stepDAOList);
    }

    /**
     * 获取步骤树形结构信息
     * 该方法用于根据步骤列表构建一个树形结构的步骤信息DTO，用于展示步骤之间的关联和传递条件
     *
     * @param stepDAOList 步骤DAO列表，包含所有需要构建树形结构的步骤信息
     * @return 返回构建好的步骤信息DTO树形结构，如果没有有效的步骤信息则返回null
     */
    @Override
    public StepInfoDTO getStepTree(List<StepDAO> stepDAOList) {
        // 存储步骤ID与通过条件的映射关系
        Map<Integer, String> passConsMap = new HashMap<>();
        // 记录起始步骤的ID
        Integer startId = null;
        // 存储步骤ID与步骤信息DTO的映射关系
        Map<Integer, StepInfoDTO> stepInfoDTOMap = new HashMap<>();

        // 遍历步骤列表，创建步骤信息DTO并记录必要的信息
        for (StepDAO stepDAO : stepDAOList) {
            StepInfoDTO stepInfoDTO = StepInfoDTO.create(stepDAO);
            // 如果步骤的通过条件不为空，则记录该步骤的通过条件
            if (!StringUtil.isEmpty(stepInfoDTO.getPassCons())) {
                passConsMap.put(stepDAO.getId(), stepInfoDTO.getPassCons());
            }
            // 记录第一个步骤的ID作为起始步骤ID
            startId = stepDAO.getId();
            // 如果步骤的通知配置不为空，则解析通知配置并设置到步骤信息DTO中
//        if (!StringUtil.isEmpty(stepDAO.getNotification())) {
//            stepInfoDTO.setNotification(approveService.getListFromString(stepDAO.getNotification(), StepInfoNotificationDTO.class));
//        }
            // 将步骤信息DTO添加到映射中
            stepInfoDTOMap.put(stepDAO.getId(), stepInfoDTO);
        }

        // 如果起始步骤ID不为空，则根据记录的信息构建并返回步骤树形结构的DTO
        if (Objects.nonNull(startId)) {
            return createTreeDfs(stepInfoDTOMap, passConsMap, startId);
        }
        // 如果没有有效的起始步骤ID，则返回null
        return null;
    }


    /**
     * 使用深度优先搜索创建审批步骤树
     * 此方法的目的是根据步骤ID和传递条件，从映射中提取步骤信息，并构建一个表示审批流程的树结构
     * 它通过递归调用自身来处理每个步骤的子步骤，直到达到没有子步骤的步骤为止
     *
     * @param stepInfoDTOMap 包含所有步骤信息的映射，键为步骤ID，值为步骤信息DTO
     * @param passConsMap    包含传递条件的映射，键为步骤ID，值为传递条件字符串
     * @param stepId         当前处理的步骤ID
     * @return 返回构建的步骤信息DTO树结构
     * @throws BsException 如果步骤信息错误，则抛出此异常
     */
    @Override
    public StepInfoDTO createTreeDfs(Map<Integer, StepInfoDTO> stepInfoDTOMap, Map<Integer, String> passConsMap, Integer stepId) {
        // 获取当前步骤的详细信息
        StepInfoDTO stepInfoDTO = stepInfoDTOMap.get(stepId);
        // 如果当前步骤信息为空，则抛出异常
        if (stepInfoDTO == null) {
            throw new BsException("审批步骤错误");
        }
        // 处理步骤的通过条件，如果存在问号，则截取问号前的部分，否则设置为结束标志
        if (!StringUtil.isEmpty(stepInfoDTO.getPassCons())) {
            if (stepInfoDTO.getPassCons().contains("?")) {
                stepInfoDTO.setPassCons(stepInfoDTO.getPassCons().substring(0, stepInfoDTO.getPassCons().indexOf("?")));
            } else {
                stepInfoDTO.setPassCons("===END===");
            }
        }
        // 如果当前步骤有传递条件，则根据条件递归构建子步骤
        if (passConsMap.containsKey(stepInfoDTO.getId())) {
            String passCons = passConsMap.get(stepInfoDTO.getId());
            if (passCons.contains("?")) {
                String[] childIdArr = passCons.substring(passCons.indexOf("?") + 1).split(":");
                stepInfoDTO.setChildTrue(createTreeDfs(stepInfoDTOMap, passConsMap, Integer.valueOf(childIdArr[0])));
                stepInfoDTO.setChildFalse(createTreeDfs(stepInfoDTOMap, passConsMap, Integer.valueOf(childIdArr[1])));
            } else {
                stepInfoDTO.setChildTrue(createTreeDfs(stepInfoDTOMap, passConsMap, Integer.valueOf(passCons)));
            }
        }
        // 返回构建完成的步骤信息DTO
        return stepInfoDTO;
    }


    @Override
    public List<StepInfoDTO> createStepInfo(String flowId, Map<String, Object> tempDataMap) {
        List<StepDAO> stepDAOList = getStepList(GetStepCondition.builder().flowId(flowId).build());
        if (stepDAOList.isEmpty()) {
            return new ArrayList<>();
        }
        StepDAO startStepDAO = null;
        Map<Integer, StepDAO> stepDAOMap = new TreeMap<>();
        for (StepDAO stepDAO : stepDAOList) {
            List<StepInfoNotificationDTO> stepInfoNotificationDTOList = new ArrayList<>();
            if (!StringUtil.isEmpty(stepDAO.getNotification())) {
                stepInfoNotificationDTOList = JSONUtil.toList(stepDAO.getNotification(), StepInfoNotificationDTO.class);
            }
            if (!stepInfoNotificationDTOList.isEmpty()) {
                for (StepInfoNotificationDTO stepInfoNotificationDTO : stepInfoNotificationDTOList) {
                    if (!StringUtil.isEmpty(stepInfoNotificationDTO.getUuid())) {
                        stepInfoNotificationDTO.setUserId(stepInfoNotificationDTO.getUuid());
                    }
                    if (!StringUtil.isEmpty(stepInfoNotificationDTO.getUserId())) {
                        stepInfoNotificationDTO.setUuid(stepInfoNotificationDTO.getUserId());
                    }
                }
            }
            stepDAO.setNotification(JSONUtil.toJsonStr(stepInfoNotificationDTOList));
            stepDAOMap.put(stepDAO.getId(), stepDAO);
            if (startStepDAO == null) {
                startStepDAO = stepDAO;
            }
        }
        //筛选审批步骤//初始化
        List<StepDAO> thisStepDAOList = new ArrayList<>();
        List<StepInfoNotificationDTO> startStepDAONotificationList = JSONUtil.toList(startStepDAO.getNotification(), StepInfoNotificationDTO.class);
        if (!startStepDAONotificationList.isEmpty()) {
            thisStepDAOList.add(startStepDAO);
        }
        StepDAO thisStepDAO = null;
        while (true) {
            //当前步骤节点
            thisStepDAO = thisStepDAO == null ? startStepDAO : thisStepDAO;
            //当前下一步条件
            String passCons = thisStepDAO.getPassCons();
            if (StringUtil.isEmpty(passCons)) {
                break;
            }
            if (StringUtil.isNumberic(passCons)) {
                //转出条件是一个数字，这就是下一个步骤//登入此步骤，准备进入下一次寻找
                if (!stepDAOMap.containsKey(Integer.valueOf(passCons))) {
                    break;
                }
                thisStepDAO = stepDAOMap.get(Integer.valueOf(passCons));
                List<StepInfoNotificationDTO> stepInfoNotificationDTOList = JSONUtil.toList(thisStepDAO.getNotification(), StepInfoNotificationDTO.class);
                if (!stepInfoNotificationDTOList.isEmpty()) {
                    thisStepDAOList.add(thisStepDAO);
                }
            } else if (!tempDataMap.isEmpty() && passCons.contains("?") && passCons.contains(":")) {
                //二元条件判断和寻找
                String[] passConsArr = passCons.split("\\?");
                String[] stepIdArr = passConsArr[1].split(":");
                String stepId = "";
                if (judge(passConsArr[0], tempDataMap)) {
                    stepId = stepIdArr[0];
                } else {
                    stepId = stepIdArr[1];
                }
                if (!stepDAOMap.containsKey(Integer.valueOf(stepId))) {
                    break;
                }
                thisStepDAO = stepDAOMap.get(Integer.valueOf(stepId));
                List<StepInfoNotificationDTO> stepInfoNotificationDTOList = JSONUtil.toList(thisStepDAO.getNotification(), StepInfoNotificationDTO.class);
                if (!stepInfoNotificationDTOList.isEmpty()) {
                    thisStepDAOList.add(thisStepDAO);
                }
            } else if (passCons.contains("?") && passCons.contains(":")) {
                //二元但是没有提交表单的不给展示list待审
                break;
            }
        }
        return thisStepDAOList.stream().map(StepInfoDTO::create).collect(Collectors.toList());
    }

    @Override
    public List<StepInfoDTO> createStepList(String flowId, Map<String, Object> tempDataMap, List<StepInfoDTO> stepInfoList, Map<String, Boolean> verifyResult) {
        // 1、获取审批表单所有步骤
        List<StepDAO> stepDAOList = getStepList(GetStepCondition.builder().flowId(flowId).build());
        if (stepDAOList.isEmpty()) {
            return new ArrayList<>();
        }

        if (Objects.isNull(stepInfoList)) {
            throw new BsException("审批提交失败,当前审批人需要提交节点信息");
        }

        // 2、将DAO 转为 DTO
        List<StepInfoDTO> stepInfoDTOList = stepDAOList.stream().map(StepInfoDTO::create).collect(Collectors.toList());

        // 3、获取创建审批流传入的步骤信息（有些步骤可能在 step 表里没有人员信息，比如发起流程时 用户自选的审批人）
        Map<String, StepInfoDTO> stepInfoDTOMap = stepInfoList.stream().collect(Collectors.toMap(StepInfoDTO::getStepId, stepInfoDTO -> stepInfoDTO));

        // 4、循环一遍 步骤，将其中内容补全（假设该步骤为自选步骤，将 用户 选择的审批人，填入到该步骤中）
        for (StepInfoDTO stepInfoDTO : stepInfoDTOList) {
            // 获取 业务 传入步骤中，与当前步骤 相对应的 步骤
            StepInfoDTO stepInfo = stepInfoDTOMap.get(stepInfoDTO.getStepId());

            // 与传入的 step 做对比，优先试用微服务自身数据
            if (stepInfo != null) {
                if (stepInfoDTO.getNotification() != null && stepInfo.getNotification() != null && stepInfoDTO.getNotification().isEmpty() && !stepInfo.getNotification().isEmpty()) {
                    stepInfoDTO.setNotification(stepInfo.getNotification());
                }
                // 判断传入的抄送人是否不为空
                if (stepInfoDTO.getCcNotification() != null && stepInfo.getCcNotification() != null && !stepInfo.getCcNotification().isEmpty()) {
                    // 如果当前抄送人为空，但传入的抄送人有值，则采用传入的抄送人
                    if (stepInfoDTO.getCcNotification().isEmpty()) {
                        stepInfoDTO.setCcNotification(stepInfo.getCcNotification());
                    } else {
                        // 如果该审批步骤设置抄送人，而传入
//                        List<StepInfoNotificationDTO> stepInfoDTOUuidList = stepInfoDTO.getCcNotification();
//                        List<StepInfoNotificationDTO> stepInfoUuidList = stepInfo.getCcNotification();
//                        stepInfoDTOUuidList.addAll(stepInfoUuidList);
//                        Map<String, StepInfoNotificationDTO> map = stepInfoDTOUuidList.stream().collect(Collectors.toMap(StepInfoNotificationDTO::getUuid, dto -> dto, (dto1, dto2) -> dto1));
//                        stepInfoDTO.setCcNotification(new ArrayList<>(map.values()));

                        Map<String, StepInfoNotificationDTO> map = stepInfo.getCcNotification().stream().collect(Collectors.toMap(StepInfoNotificationDTO::getUuid, dto -> dto, (dto1, dto2) -> dto1));
                        stepInfoDTO.setCcNotification(new ArrayList<>(map.values()));
                    }
                }
                if (Objects.nonNull(stepInfo.getExtraContent()) && !stepInfo.getExtraContent().isEmpty()) {
                    stepInfoDTO.setExtraContent(stepInfo.getExtraContent());
                }
            }

            // 规范步骤内的值
            assert stepInfoDTO.getNotification() != null;
            if (!stepInfoDTO.getNotification().isEmpty()) {
                for (StepInfoNotificationDTO stepInfoNotificationDTO : stepInfoDTO.getNotification()) {
                    if (!StringUtil.isEmpty(stepInfoNotificationDTO.getUuid())) {
                        stepInfoNotificationDTO.setUserId(stepInfoNotificationDTO.getUuid());
                    }
                    if (!StringUtil.isEmpty(stepInfoNotificationDTO.getUserId())) {
                        stepInfoNotificationDTO.setUuid(stepInfoNotificationDTO.getUserId());
                    }
                }
            }
        }

        JSONObject jsonObject = flowServiceExtend.getJsonObject(JSONUtil.parseObj(tempDataMap));
//        Set<String> keySet = jsonObject.keySet();
//        for (String key : keySet) {
//            if (JSONUtil.isTypeJSONObject(jsonObject.getStr(key))) {
//                JSONObject jsonITEM = jsonObject.getJSONObject(key);
//                String str = jsonITEM.getStr("value");
//                if (StrUtil.isNotEmpty(str)) {
//                    jsonObject.set(key, str);
//                }
//            } else if (JSONUtil.isTypeJSONArray(jsonObject.getStr(key))) {
//                JSONArray jsonITEM = jsonObject.getJSONArray(key);
//                int size = jsonITEM.size();
//                for (int i = 0; i < size; i++) {
//                    JSONObject jsonObjectItem = jsonITEM.getJSONObject(i);
//                    String str = jsonObjectItem.getStr("value");
//                    if (StrUtil.isNotEmpty(str)) {
//                        jsonObject.set(key, str);
//                    }
//                }
//            }
//        }

        // 5、依据 开始步骤内的 权重做排序，依次作为顺序 查找应该走入的分支
        return recursionStepInfo(stepInfoDTOList, jsonObject, "start", verifyResult);
    }
//    @Override TODO  字段类型改动
//    public StepInfoDTO getStepTree(List<StepDAO> stepDAOList) {
//        Map<Integer, String> passConsMap = new HashMap<>();
//        int startId = 0;
//        Map<Integer, StepInfoDTO> stepInfoDTOMap = new HashMap<>();
//        for (StepDAO stepDAO : stepDAOList) {
//            StepInfoDTO stepInfoDTO = StepInfoDTO.create(stepDAO);
//            if (!StringUtil.isEmpty(stepInfoDTO.getPassCons())) {
//                passConsMap.put(stepDAO.getId(), stepInfoDTO.getPassCons());
//            }
//            startId = stepDAO.getId();

    /// /            if (!StringUtil.isEmpty(stepDAO.getNotification())) {
    /// /                stepInfoDTO.setNotification(approveService.getListFromString(stepDAO.getNotification(), StepInfoNotificationDTO.class));
    /// /            }
//            stepInfoDTOMap.put(stepDAO.getId(), stepInfoDTO);
//        }
//        if (startId > 0) {
//            return createTreeDfs(stepInfoDTOMap, passConsMap, startId);
//        }
//        return null;
//    }

    /**
     * 判断需要进入的分支 ,并组成最终的流程节点
     *
     * @param stepInfoDTOList
     * @param tempDataMap
     * @param srcId
     * @param verifyResult
     * @return
     */
    @Override
    public List<StepInfoDTO> recursionStepInfo(List<StepInfoDTO> stepInfoDTOList, Map<String, Object> tempDataMap, String srcId, Map<String, Boolean> verifyResult) {
        // 1、结果集合
        List<StepInfoDTO> stepInfoDTOListResult = new ArrayList<>();

        // 2、依据所有步骤，获取当前步骤的 所有分支
        List<StepInfoDTO> stepInfoDTOListByStepId = stepInfoDTOList.stream().filter(stepInfoDTO -> stepInfoDTO.getSrcId().equals(srcId)).sorted(Comparator.comparing(StepInfoDTO::getWeight)).collect(Collectors.toList());

        // 3、如果找不到 下一步 则返回空
        if (stepInfoDTOListByStepId.isEmpty()) {
            return stepInfoDTOListResult;
        }

        // 4、循环当前步骤分支
        stepDTO:
        for (StepInfoDTO stepDTO : stepInfoDTOListByStepId) {
            // 如果当前步骤的 条件为空（说明该步骤无判断条件），则直接进入该步骤的分支
            if (stepDTO.getConditions() == null || stepDTO.getConditions().isEmpty()) {
                List<StepInfoNotificationDTO> notification = stepDTO.getNotification();
                List<StepInfoNotificationDTO> ccNotificationList = stepDTO.getCcNotification();
                if ((Objects.nonNull(notification) && !notification.isEmpty()) || (Objects.nonNull(ccNotificationList) && !ccNotificationList.isEmpty())) {
                    stepInfoDTOListResult.add(stepDTO);
                }
                stepInfoDTOListResult.addAll(recursionStepInfo(stepInfoDTOList, tempDataMap, stepDTO.getStepId(), verifyResult));
                break;
            } else {
                // 如果该步骤有条件 判断该条件是否成立
                // 循环条件列表
                conditionGroup:
                for (Map<String, ConditionDTO> conditionGroup : stepDTO.getConditions()) {
                    // 循环条件组
                    for (Map.Entry<String, ConditionDTO> condition : conditionGroup.entrySet()) {
                        if (condition.getValue().getIsVerify()) {
                            String conditionString = condition.getKey() + condition.getValue().getType() + condition.getValue().getValue();
                            boolean judgeOneResult = stepServiceExtend.judgeOne(conditionString, tempDataMap);
                            if (!judgeOneResult) {
                                continue conditionGroup;
                            }
                        } else {
                            if (Objects.isNull(verifyResult)) {
                                throw new BsException("审批提交失败,当前审批人需要提交条件判断节点");
                            }
                            // 如果该步骤 不需要微服务来判定 条件结果，则采用 使用方 传递过来的结果判断
                            if (verifyResult.get(stepDTO.getStepId()) == null || !verifyResult.get(stepDTO.getStepId())) {
                                continue conditionGroup;
                            }
                        }
                    }
                    if (!stepDTO.getNotification().isEmpty()) {
                        stepInfoDTOListResult.add(stepDTO);
                    }
                    stepInfoDTOListResult.addAll(recursionStepInfo(stepInfoDTOList, tempDataMap, stepDTO.getStepId(), verifyResult));
                    break stepDTO;
                }
            }
        }

        return stepInfoDTOListResult;
    }

    public boolean judge(String conditionStr, Map<String, Object> tempDataMap) {
        if (conditionStr.contains("&&")) {
            String[] conditionArr = conditionStr.split("&&");
            for (String condition : conditionArr) {
                if (!stepServiceExtend.judgeOne(condition, tempDataMap)) return false;
            }
            return true;
        } else if (conditionStr.contains("||")) {
            String[] conditionArr = conditionStr.split("\\|\\|");
            for (String condition : conditionArr) {
                if (!stepServiceExtend.judgeOne(condition, tempDataMap)) return true;
            }
            return false;
        }
        return stepServiceExtend.judgeOne(conditionStr, tempDataMap);
    }


    /**
     * 审批 数据推进方法
     * @param id            审批流程ID
     * @param action        审批操作类型
     * @param uuid          操作人身份标识
     * @param showName      操作人名称
     * @param reason        操作备注
     * @param appChannelId  操作客户端渠道ID
     * @param dataChannelId 操作数据源ID
     * @param title         操作标题
     * @return
     */
//    @Override
//    public boolean goFlow(int id, int action, String uuid, String showName, String reason, String appChannelId, String dataChannelId, String title) {
//        log.info("操作人 -> {}", uuid);
//        log.info("流程ID -> {}", id);
//        // 1、获取 当前操作 审批流程数据
//        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(id).build());
//        if (flowPathDAO == null) {
//            throw new BsException("审批流程不存在");
//        }
//
//        /*
//         * 判读是否需要驳回
//         */
//        boolean flag = flowPathService.approveReject(flowPathDAO.getId());
//        if (flag) { //已驳回
//            return true;
//        }
//
//        // 2、读取 该审批流程 预设审批步骤
//        List<FlowPathStepDTO> stepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
//                .flowPathId(id)
//                .build());
//
//        // 3、整理操作所需的变量
//        String flowTitle = Optional.of(flowPathDAO.getTitle()).orElse(flowPathDAO.getFlowTitle());
//        String flowPathExtraContent = Optional.of(flowPathDAO.getExtraContent()).orElse("{}");  //审批流flowpathList扩展字段
//        //标记待审
//        FlowPathStepDTO apprNotification = null;
//        List<String> approvalUuidList = new ArrayList<>();
//        int thisStepCompleted = 0;
//        int stepCompleted = 0;
//        //根据设定流转
//        List<FlowPathStepNotificationDTO> ccNotificationList = new ArrayList<>();
//        FlowPathHistoryDAO flowPathHistoryDAO = null;
//
//        // 4、开始流转
//        // 创建审批流程
//        if (action == ApproveConstant.flowPathStatus.WAITING.getCode()) {
//            // 创建审批实例 不需要验证权限
//            // 创建审批流时，直接下一步:相当于通过
//            title = StringUtil.isEmpty(title) ? (showName + " 创建了 " + flowTitle + "申请") : title;
//
//            // 存储操作历史
//            flowPathHistoryDAO = FlowPathHistoryDAO
//                    .builder()
//                    .title(title)
//                    .flowPathId(id)
//                    .uuid(uuid)
//                    .stepId(0)
//                    .nextStepId(stepInfoDTOList.get(0).getId())
//                    .action(action)
//                    .reason(reason)
//                    .build();
//            flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//
//            // 存储到 "我的" 审批列表
//            flowPathListMapper.addFlowPathList(
//                    FlowPathListDAO
//                            .builder()
//                            .title(flowTitle)
//                            .flowPathId(id)
//                            .uuid(uuid)
//                            .type(1)
//                            .appChannelId(appChannelId)
//                            .dataChannelId(dataChannelId)
//                            .extraContent(flowPathExtraContent)
//                            .build()
//            );
//            apprNotification = stepInfoDTOList.get(0);  //第一个节点
//            List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
//            if (Objects.isNull(notificationList) || notificationList.isEmpty()) {
//                thisStepCompleted = 1;
//            }
//        }
//        //创建END
//        //已经通过的审批人//有序
//        Map<Integer, List<String>> passStepUuidMap = new HashMap<>();
//        for (FlowPathStepDTO flowPathStepDTO : stepInfoDTOList) {
//            List<String> passStepUuidList = passStepUuidMap.getOrDefault(flowPathStepDTO.getId(), new ArrayList<>());
//            List<FlowPathStepNotificationDTO> notificationList = flowPathStepDTO.getNotificationList();
//            if (Objects.nonNull(notificationList) && !notificationList.isEmpty()) {
//                for (FlowPathStepNotificationDTO flowPathStepNotificationDTO : notificationList) {
//                    if (flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()) {
//                        passStepUuidList.add(flowPathStepNotificationDTO.getUuid());
//                    }
//                }
//            }
//            passStepUuidMap.put(flowPathStepDTO.getId(), passStepUuidList);
//        }
//        if (action > ApproveConstant.flowPathAction.CREATE.getCode()) {
//            //读取当前步骤&上一步&下一步
//            FlowPathStepDTO thisStepInfo = null, nextStepInfo = null, lastStepInfo = null;
//            List<FlowPathStepDTO> passStepList = new ArrayList<>();
//            int size = stepInfoDTOList.size();
//            for (int i = 0; i < size; i++) {
//                //取出第一个待审核的步骤，为当前步骤
//                if (stepInfoDTOList.get(i).getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
//                    thisStepInfo = stepInfoDTOList.get(i);
//                    if ((i + 1) < stepInfoDTOList.size()) {
//                        nextStepInfo = stepInfoDTOList.get(i + 1);
//                    }
//                    if (i > 0) {
//                        lastStepInfo = stepInfoDTOList.get(i - 1);
//                    }
//                    break;
//                } else if (stepInfoDTOList.get(i).getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()) {
//                    passStepList.add(stepInfoDTOList.get(i));
//                }
//            }
//            if (thisStepInfo == null) {
//                throw new BsException("审批流程已结束");
//            }
//            int thisStepHisPathId = thisStepInfo.getId();
//            //业务通知和抄送通知数据
//            ccNotificationList = thisStepInfo.getCcNotificationList();
//            //权限验证
//            //审批通过或拒绝//1拒绝2通过
//            if (action == ApproveConstant.flowPathAction.REFUSE.getCode() || action == ApproveConstant.flowPathAction.PASS.getCode()) {
//                //查看设定：全员、任一、依次
//                int passAll = thisStepInfo.getPassAll();
//                int isNextPerson = 0;
//                if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
//                    //依次审批查验是否是当前步骤下一个待审
//                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
//                        if (notifyOne.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
//                            //如果当前步骤待审批人是此人
//                            if (notifyOne.getUuid().equals(uuid)) {
//                                isNextPerson = 1;
//                            }
//                            break;
//                        }
//                    }
//                } else {
//                    //全员审批
//                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
//                        //如果当前步骤其中一个审批人是此人，验证前两个是否和历史记录相符
//                        //全员审批和任一审批的判断依据都是是否在当前步骤下
//                        if (notifyOne.getUuid().equals(uuid)
//                                && notifyOne.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()) {
//                            isNextPerson = 1;
//                            break;
//                        }
//                    }
//                }
//                if (isNextPerson == 0) {
//                    throw new BsException("对不起，当前用户权限不符合当前规则");
//                }
//                //===== 分割线 =====
//                //权限验证完毕，审核环节
//                if (action == ApproveConstant.flowPathAction.REFUSE.getCode()) {
//                    //拒绝操作
//                    //更新flow_path表状态
//                    flowPathMapper.updateFlowPath(
//                            FlowPathDAO.builder()
//                                    .id(id)
//                                    .status(ApproveConstant.flowPathStatus.REFUSED.getCode())
//                                    .updateTime(new Date())
//                                    .build()
//                    );
//                    //更新flow_path_step表
//                    flowPathStepService.update(
//                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.REFUSED.getCode()).build(),
//                            GetFlowPathStepCondition.builder().flowPathId(thisStepInfo.getFlowPathId()).stepNo(thisStepInfo.getStepNo()).build()
//                    );
//                    //更新flow_path_step_notification表
//                    flowPathStepNotificationService.update(
//                            FlowPathStepNotificationDAO.builder()
//                                    .reason(reason)
//                                    .status(ApproveConstant.flowPathStatus.REFUSED.getCode())
//                                    .updateTime(new Date())
//                                    .build(),
//                            GetFlowPathStepNotificationCondition.builder()
//                                    .flowPathId(thisStepInfo.getFlowPathId())
//                                    .stepNo(thisStepInfo.getStepNo())
//                                    .uuid(uuid)
//                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                                    .build()
//                    );
//                    //历史
//                    title = StringUtil.isEmpty(title) ? (showName + " 驳回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
//                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).action(action).reason(reason).build();
//                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//                    //更新 待我 处理的审批到 已处理//当前实例一起更新
//                    flowPathListMapper.update(
//                            FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
//                            GetFlowPathListDAOListCondition.builder().flowPathId(id).type(ApproveConstant.flowPathListType.WAIT.getCode()).build()
//                    );
//                    title = showName + "驳回了你的" + flowTitle + "申请";
//
//                    //拒绝回调前，先进行通知回调
//                    callbackExtend.callback(
//                            flowPathDAO.getApproverUrl(),
//                            callbackExtend.createNotificationData(
//                                    flowPathDAO.getFlowId(),
//                                    id,
//                                    flowPathDAO.getUuid(),
//                                    "approve",
//                                    "refuse",
//                                    flowPathDAO.getTempShowData(),
//                                    title,
//                                    "",
//                                    flowPathDAO.getTempData(),
//                                    JSONUtil.toJsonStr(stepInfoDTOList),
//                                    flowPathDAO.getExtraContent(),
//                                    flowPathDAO.getStatus()
//                            )
//                    );
//
//                    //拒绝回调
//                    callbackExtend.callback(flowPathDAO.getRefuseUrl(),
//                            callbackExtend.createNotificationData(
//                                    flowPathDAO.getFlowId(),
//                                    id,
//                                    flowPathDAO.getUuid(),
//                                    "approve",
//                                    "refuse",
//                                    flowPathDAO.getTempShowData(),
//                                    title,
//                                    "",
//                                    flowPathDAO.getTempData(),
//                                    JSONUtil.toJsonStr(stepInfoDTOList),
//                                    flowPathDAO.getExtraContent(),
//                                    ApproveConstant.flowPathStatus.REFUSED.getCode() // flowPathDAO.getStatus()
//                            )
//                    );
//
//                    if (flowPathDAO.getWithdrawPathId() != 0) {
//                        // 恢复 撤回目标流程 使其继续执行
//                        proceedApproval(flowPathDAO.getWithdrawPathId());
//                    }
//
//                    //拒绝实例操作完成
//                    stepCompleted = 1;
//                } else {
//                    //通过操作 审批通过
//                    FlowPathStepNotificationDTO nextNotification = null;//下一个审批人
//                    int toNext = 1;
//                    ArrayList<String> notificationUuidList = new ArrayList<>();
//                    /*
//                     * 检查流程状态是全员通过还是依次通过
//                     */
//                    if (passAll == ApproveConstant.flowPassAll.ALL.getCode() || passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
//                        //全员通过和依次通过都是验证是否全部通过
//                        for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
//                            //过滤掉转审状态的审核人或加签状态的审批人
//                            if (notifyOne.getStatus() == ApproveConstant.flowPathStatus.TRANSFERRED.getCode()) {
//                                continue;
//                            }
//                            notificationUuidList.add(notifyOne.getUuid());
//                            //如果当前步骤其中一个审批人是此人，验证前两个是否和历史记录相符
//                            //全员审批和任一审批的判断依据都是是否在当前步骤下
//                            if (!passStepUuidMap.containsKey(thisStepHisPathId)) {
//                                passStepUuidMap.put(thisStepHisPathId, new ArrayList<>());
//                            }
//                            List<String> uuidList = passStepUuidMap.get(thisStepHisPathId);
//                            uuidList.add(uuid);
//                            passStepUuidMap.put(thisStepHisPathId, uuidList);
//                            if (!uuidList.contains(notifyOne.getUuid())) {
//                                //依次通过模式只通知下一个审批人，先记录下一个审批人uuid，后面触发通知
//                                if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode() && nextNotification == null) {
//                                    nextNotification = notifyOne;
//                                }
//                                toNext = 0;
//                            }
//                        }
//                    }
//                    //历史
//                    title = StringUtil.isEmpty(title) ? (showName + " 通过了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
//                    if (toNext == 0) {
//                        //如果不去下一步，那么下一步还是这一步，等待全员审核后才能下一步
//                        nextStepInfo = thisStepInfo;
//                        //更新 待我 处理的审批到 已处理//当前实例一起更新
//                        flowPathListMapper.update(
//                                FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(),
//                                GetFlowPathListDAOListCondition.builder().flowPathId(id).uuid(uuid).type(ApproveConstant.flowPathListType.WAIT.getCode()).build()
//                        );
//                        //依次通过模式通知下一个审批人
//                        if (passAll == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()
//                                && nextNotification != null) {
//                            apprNotification = nextStepInfo;
////                            callbackExtend.callback(flowPathDAO.getApproverUrl(), callbackExtend.createNotificationData(flowPathDAO.getFlowId(), id, nextNotification.getUuid(), "approve", "normal", flowPathDAO.getTempShowData(), flowTitle, JSONUtil.toJsonStr(nextNotification), ""));
//                            approvalUuidList.add(nextNotification.getUuid());
//                        }
//                    } else {
//                        //判断下一步是否存在//如果没有下一步，那么审批结束，全流程通过
//                        if (nextStepInfo == null) {
//                            //更新状态
//                            flowPathMapper.updateFlowPath(
//                                    FlowPathDAO.builder()
//                                            .id(id)
//                                            .status(ApproveConstant.flowPathStatus.PASSED.getCode())
//                                            .updateTime(new Date())
//                                            .build()
//                            );
//                            //通过实例操作完成
//                            stepCompleted = 1;
//                            thisStepCompleted = 1;
//
//                            //通过回调前，通知回调
//                            callbackExtend.callback(
//                                    flowPathDAO.getApproverUrl(),
//                                    callbackExtend.createNotificationData(
//                                            flowPathDAO.getFlowId(),
//                                            id,
//                                            flowPathDAO.getUuid(),
//                                            "approve",
//                                            "pass",
//                                            flowPathDAO.getTempShowData(),
//                                            title,
//                                            "",
//                                            flowPathDAO.getTempData(),
//                                            JSONUtil.toJsonStr(stepInfoDTOList),
//                                            flowPathDAO.getExtraContent(),
//                                            flowPathDAO.getStatus()
//                                    )
//                            );
//
//                            //通过回调
//                            callbackExtend.callback(
//                                    flowPathDAO.getPassUrl(),
//                                    callbackExtend.createNotificationData(
//                                            flowPathDAO.getFlowId(),
//                                            id,
//                                            flowPathDAO.getUuid(),
//                                            "approve",
//                                            "pass",
//                                            flowPathDAO.getTempShowData(),
//                                            title,
//                                            "",
//                                            flowPathDAO.getTempData(),
//                                            JSONUtil.toJsonStr(stepInfoDTOList),
//                                            flowPathDAO.getExtraContent(),
//                                            ApproveConstant.flowPathAction.PASS.getCode()
//                                            // flowPathDAO.getStatus()
//                                    )
//                            );
//
//                            if (flowPathDAO.getWithdrawPathId() != 0) {
//                                // 正常撤回，直接撤回成功
//                                withdrawnPermit(flowPathDAO.getWithdrawPathId());
//                            }
//                        } else {
//                            //跳步抄送
//                            thisStepCompleted = 1;
//                            //标记待审
//                            apprNotification = nextStepInfo;
//                        }
//                        //更新 待我 处理的审批到 已处理//当前实例一起更新的当前步骤
//                        GetFlowPathListDAOListCondition condition = GetFlowPathListDAOListCondition.builder()
//                                .flowPathId(id)
//                                .type(ApproveConstant.flowPathListType.WAIT.getCode())
//                                .build();
//                        if (!notificationUuidList.isEmpty()) {
//                            condition.setUuidList(notificationUuidList);
//                        }
//                        flowPathListMapper.update(FlowPathListDAO.builder().type(ApproveConstant.flowPathListType.PROCESSED.getCode()).build(), condition);
//                        //更新flow_path_step表
//                        flowPathStepService.update(
//                                FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.PASSED.getCode()).build(),
//                                GetFlowPathStepCondition.builder().flowPathId(thisStepInfo.getFlowPathId()).stepNo(thisStepInfo.getStepNo()).build()
//                        );
//                    }
//                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).nextStepId(nextStepInfo == null ? 0 : nextStepInfo.getId()).action(action).reason(reason).build();
//                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//                    //更新flow_path_step_notification表
//                    flowPathStepNotificationService.update(
//                            FlowPathStepNotificationDAO.builder()
//                                    .status(ApproveConstant.flowPathStatus.PASSED.getCode())
//                                    .updateTime(new Date())
//                                    .build(),
//                            GetFlowPathStepNotificationCondition.builder()
//                                    .flowPathId(thisStepInfo.getFlowPathId())
//                                    .stepNo(thisStepInfo.getStepNo())
//                                    .uuid(uuid)
//                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                                    .build()
//                    );
//                }
//            } else if (action == ApproveConstant.flowPathAction.WITHDRAW.getCode()) {      //撤回
//                if (!flowPathDAO.getUuid().equals(uuid)) {
//                    throw new BsException("对不起，当前用户权限不符合当前规则");
//                }
//                if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.PROHIBIT.getCode()) {
//                    throw new BsException("对不起，当前流程不支持撤销操作");
//                } else if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.PERMIT.getCode()) {
//                    // 正常撤回，直接撤回成功
//                    withdrawnPermit(id);
//                    stepCompleted = 1;
//                } else if (flowPathDAO.getWithdraw() == FlowConstant.flowWithdraw.CONFIRMATION.getCode()) {
//                    // 如果当前步骤内有已审核通过的人，则将该步骤也放入循环中，当作新审批内的最后一步
//                    List<FlowPathStepNotificationDTO> thisStepPassApprover = thisStepInfo.getNotification().stream().filter(flowPathStepNotificationDTO -> flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()).collect(Collectors.toList());
//                    if (!thisStepPassApprover.isEmpty()) {
//                        passStepList.add(thisStepInfo);
//                    }
//                    if (passStepList.isEmpty()) {
//                        withdrawnPermit(id);
//                    } else {
//                        withdrawnConfirmation(flowPathDAO, passStepList);
//                    }
//                }
//                //撤回实例操作完成
//            } else if (action == ApproveConstant.flowPathAction.BACK.getCode()) {
//                //权限验证//当前uuid必须在当前stepInfo的uuid中
//                int canGoBack = 0;
//                for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
//                    if (notifyOne.getUuid().equals(uuid)) {
//                        canGoBack = 1;
//                    }
//                }
//                if (canGoBack == 0) {
//                    throw new BsException("对不起，当前用户权限不符合当前规则");
//                }
//                //退回上一步操作
//                if (lastStepInfo != null) {
//                    //如果有上一步，退回到上一步，状态不变
//                    nextStepInfo = thisStepInfo;
//                    thisStepInfo = lastStepInfo;
//                    //历史
//                    title = StringUtil.isEmpty(title) ? (showName + " 退回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
//                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId()).nextStepId(nextStepInfo.getId()).action(action).reason(reason).build();
//                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//                    //提取uuid
//                    ArrayList<String> lastNotificationUuidList = new ArrayList<>();
//                    List<FlowPathStepNotificationDTO> lastStepInfoNotificationDTOList = lastStepInfo.getNotificationList();
//                    //标记待审
//                    apprNotification = nextStepInfo;
//                    for (FlowPathStepNotificationDTO notifyOne : lastStepInfoNotificationDTOList) {
//                        lastNotificationUuidList.add(notifyOne.getUuid());
//                    }
//                    ArrayList<String> notificationUuidList = new ArrayList<>();
//                    //提取uuid
//                    for (FlowPathStepNotificationDTO notifyOne : thisStepInfo.getNotificationList()) {
//                        if (!lastNotificationUuidList.contains(notifyOne.getUuid())) {
//                            notificationUuidList.add(notifyOne.getUuid());
//                        }
//                    }
//                    if (!notificationUuidList.isEmpty()) {
//                        //删除当前 待我 处理的审批
//                        flowPathListMapper.delete(GetFlowPathListDAOListCondition.builder().flowPathId(id).uuidList(notificationUuidList).typeList(new ArrayList<Integer>() {{
//                            add(2);
//                            add(3);
//                        }}).build());
//                    }
//                    //上一步 已 处理的审批，变更为待处理
//                    flowPathListMapper.update(
//                            FlowPathListDAO.builder().type(3).build(),
//                            GetFlowPathListDAOListCondition.builder().flowPathId(id).uuidList(lastNotificationUuidList).type(2).build()
//                    );
//                    //更新上一步flow_path_step表的状态为待审核
//                    flowPathStepService.update(
//                            FlowPathStepDAO.builder().status(ApproveConstant.flowPathStatus.WAITING.getCode()).updateTime(new Date()).build(),
//                            GetFlowPathStepCondition.builder().flowPathId(lastStepInfo.getFlowPathId()).stepNo(lastStepInfo.getStepNo()).build()
//                    );
//                    //更新上一步flow_path_step_notification表的状态为待审核
//                    flowPathStepNotificationService.update(
//                            FlowPathStepNotificationDAO.builder()
//                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                                    .updateTime(new Date())
//                                    .build(),
//                            GetFlowPathStepNotificationCondition.builder()
//                                    .flowPathId(lastStepInfo.getFlowPathId())
//                                    .stepNo(lastStepInfo.getStepNo())
//                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                                    .build()
//                    );
//                    //更新此步骤flow_path_step_notification表的状态为待审核
//                    flowPathStepNotificationService.update(
//                            FlowPathStepNotificationDAO.builder()
//                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                                    .updateTime(new Date())
//                                    .build(),
//                            GetFlowPathStepNotificationCondition.builder()
//                                    .flowPathId(thisStepInfo.getFlowPathId())
//                                    .stepNo(thisStepInfo.getStepNo())
//                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                                    .build()
//                    );
//                } else {
//                    //如果没有上一步，视为撤回，状态为5
//                    //更新状态
//                    flowPathMapper.updateFlowPath(FlowPathDAO.builder().id(id).status(ApproveConstant.flowPathStatus.BACK.getCode()).updateTime(new Date()).build());
//                    //历史
//                    title = StringUtil.isEmpty(title) ? (showName + " 退回了 " + flowPathDAO.getShowName() + "的" + flowTitle) : title;
//                    flowPathHistoryDAO = FlowPathHistoryDAO.builder().title(title).flowPathId(id).uuid(uuid).stepId(thisStepInfo.getId())//已经流转过的步骤id//0表示创建
//                            .nextStepId(0)//正在进行的/下一个流转步骤id
//                            .action(action).reason(reason).build();
//                    flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//                    //更新 待我 处理的审批到 已处理//当前实例一起更新
//                    flowPathListMapper.update(FlowPathListDAO.builder().type(3).build(), GetFlowPathListDAOListCondition.builder().flowPathId(id).type(2).build());
//                    stepCompleted = 1;
//                    //退回实例操作完成
//                    //更新此步骤flow_path_step_notification表的状态为待审核
//                    flowPathStepNotificationService.update(
//                            FlowPathStepNotificationDAO.builder()
//                                    .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                                    .updateTime(new Date())
//                                    .build(),
//                            GetFlowPathStepNotificationCondition.builder()
//                                    .flowPathId(thisStepInfo.getFlowPathId())
//                                    .stepNo(thisStepInfo.getStepNo())
//                                    .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
//                                    .build()
//                    );
//                }
//            }
//        }
//
//        /*
//         *  如果不是发起操作 ,该对象为下一节点
//         */
//        if (apprNotification != null) {
//
//            /*
//             * 查询剩下的待审节点
//             */
//            List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(
//                    GetFlowPathStepCondition.builder().
//                            flowPathId(id).
//                            status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                            .build());
//
//            Iterator<FlowPathStepDTO> iterator = newStepInfoDTOList.iterator(); //使用迭代器.来判断是否时最后的节点
//
//            while (iterator.hasNext()) {
//                apprNotification = iterator.next();
//
//                List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
//                if (Objects.nonNull(notificationList) && !notificationList.isEmpty()) {  //审批节点不为空
//                    break; //跳出循环
//                }
//
//                List<FlowPathStepNotificationDTO> ccNotificationDTOList = apprNotification.getCcNotificationList(); //抄送数据
//                if (Objects.isNull(ccNotificationDTOList) || ccNotificationDTOList.isEmpty()) {
//                    continue;
//                }
//
//                FlowPathStepDAO flowPathStepDAO = new FlowPathStepDAO();
//                flowPathStepDAO.setStatus(ApproveConstant.flowPathStatus.PASSED.getCode());
//                GetFlowPathStepCondition condition = new GetFlowPathStepCondition();
//                condition.setId(apprNotification.getId());
//                flowPathStepService.update(flowPathStepDAO, condition);
//
//                ccNotificationList.addAll(ccNotificationDTOList);
//            }
//            List<FlowPathStepNotificationDTO> notificationList = apprNotification.getNotificationList();
//            if (!iterator.hasNext() && (Objects.isNull(notificationList) || notificationList.isEmpty())) {    //判断是否还有节点,如果没有,说明最后的节点是抄送节点,已经被上边的循环消化掉了 || 判断最后一个节点,是否是审批节点,如果是
//                flowPathMapper.updateFlowPath(
//                        FlowPathDAO.builder()
//                                .id(id)
//                                .status(ApproveConstant.flowPathStatus.PASSED.getCode())
//                                .updateTime(new Date())
//                                .build()
//                );
//                //通过回调前，通知回调
//                callbackExtend.callback(
//                        flowPathDAO.getApproverUrl(),
//                        callbackExtend.createNotificationData(
//                                flowPathDAO.getFlowId(),
//                                id,
//                                flowPathDAO.getUuid(),
//                                "approve",
//                                "pass",
//                                flowPathDAO.getTempShowData(),
//                                title,
//                                "",
//                                flowPathDAO.getTempData(),
//                                JSONUtil.toJsonStr(stepInfoDTOList),
//                                flowPathDAO.getExtraContent(),
//                                flowPathDAO.getStatus()
//                        )
//                );
//
//                //通过回调
//                callbackExtend.callback(
//                        flowPathDAO.getPassUrl(),
//                        callbackExtend.createNotificationData(
//                                flowPathDAO.getFlowId(),
//                                id,
//                                flowPathDAO.getUuid(),
//                                "approve",
//                                "pass",
//                                flowPathDAO.getTempShowData(),
//                                title,
//                                "",
//                                flowPathDAO.getTempData(),
//                                JSONUtil.toJsonStr(stepInfoDTOList),
//                                flowPathDAO.getExtraContent(),
//                                ApproveConstant.flowPathAction.PASS.getCode()
//                                // flowPathDAO.getStatus()
//                        )
//                );
//            }
//
//            List<FlowPathStepNotificationDTO> newNotificationList = apprNotification.getNotificationList();
//            if (Objects.nonNull(newNotificationList) && !newNotificationList.isEmpty()) {
//                //待审
//                if (apprNotification.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
//                    //依次审批只通知下一个待审
//                    int isNextPerson = 0;
//                    for (FlowPathStepNotificationDTO notifyOne : newNotificationList) {
//                        if (isNextPerson > 0 || action == 0 || thisStepCompleted > 0) {
//                            //存储待我处理的审批
//                            title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
//                            approvalUuidList.add(notifyOne.getUuid());
//                            flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(id).uuid(notifyOne.getUuid()).type(2).appChannelId(flowPathDAO.getAppChannelId()).dataChannelId(flowPathDAO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
//                            Map<String, Object> notificationData = new HashMap<>();
//                            notificationData.put("action", "approve");
//                            notificationData.put("event", "wait");
//                            notificationData.put("title", title);
//                            notificationData.put("uuid", notifyOne.getUuid());
//                            notificationData.put("flowPathId", id);
//                            notificationData.put("flowId", flowPathDAO.getFlowId());
//                            notificationData.put("tempShowData", flowPathDAO.getTempShowData());
//                            notificationData.put("extraContent", JSONUtil.toJsonStr(notifyOne));
//                            callbackExtend.callback(flowPathDAO.getApproverUrl(), notificationData);
//                            break;
//                        }
//                        if (notifyOne.getUuid().equals(uuid)) {
//                            isNextPerson = 1;
//                        }
//                    }
//                } else {
//                    //其他审批通知所有待审
//                    for (FlowPathStepNotificationDTO notifyOne : apprNotification.getNotificationList()) {
//                        //存储待我处理的审批
//                        title = flowPathDAO.getShowName() + "的" + flowTitle + "申请";
//                        flowPathListMapper.addFlowPathList(FlowPathListDAO.builder().title(title).flowPathId(id).uuid(notifyOne.getUuid()).type(2).appChannelId(flowPathDAO.getAppChannelId()).dataChannelId(flowPathDAO.getDataChannelId()).extraContent(JSONUtil.toJsonStr(notifyOne)).build());
//                        approvalUuidList.add(notifyOne.getUuid());
//                        callbackExtend.callback(flowPathDAO.getApproverUrl(),
//                                callbackExtend.createNotificationData(
//                                        flowPathDAO.getFlowId(),
//                                        id, notifyOne.getUuid(),
//                                        "approve",
//                                        "wait",
//                                        flowPathDAO.getTempShowData(),
//                                        title, JSONUtil.toJsonStr(notifyOne),
//                                        "", JSONUtil.toJsonStr(stepInfoDTOList), flowPathDAO.getExtraContent(), flowPathDAO.getStatus()));
//                    }
//                }
//            }
//        }
//
//        //抄送
//        if (thisStepCompleted > 0 && !ccNotificationList.isEmpty()) {
//            //抄送通知
//            for (FlowPathStepNotificationDTO notifyOne : ccNotificationList) {
//                //存储抄送我的审批
//                title = flowPathDAO.getShowName() + "抄送了" + flowTitle + "申请给你";
//                flowPathListMapper.addFlowPathList(
//                        FlowPathListDAO.builder()
//                                .title(title)
//                                .flowPathId(id)
//                                .uuid(notifyOne.getUuid())
//                                .type(ApproveConstant.flowPathListType.CC.getCode())
//                                .appChannelId(flowPathDAO.getAppChannelId())
//                                .dataChannelId(flowPathDAO.getDataChannelId())
//                                .extraContent(JSONUtil.toJsonStr(notifyOne))
//                                .build());
//                callbackExtend.callback(flowPathDAO.getApproverUrl(),
//                        callbackExtend.createNotificationData(
//                                flowPathDAO.getFlowId(), id, notifyOne.getUuid(), "approve", "normal",
//                                flowPathDAO.getTempShowData(), title, JSONUtil.toJsonStr(notifyOne),
//                                "", JSONUtil.toJsonStr(stepInfoDTOList),
//                                flowPathDAO.getExtraContent(), flowPathDAO.getStatus()));
//            }
//        }
//
//        //抄送本人
//        if (stepCompleted > 0 && flowPathDAO.getCcCreate() > 0) {
//            //存储抄送我的审批
//            title = "你的" + flowTitle + "申请";
//            flowPathListMapper.addFlowPathList(
//                    FlowPathListDAO.builder()
//                            .title(title)
//                            .flowPathId(id)
//                            .uuid(flowPathDAO.getUuid())
//                            .type(ApproveConstant.flowPathListType.CC.getCode())
//                            .appChannelId(flowPathDAO.getAppChannelId())
//                            .dataChannelId(flowPathDAO.getDataChannelId())
//                            .extraContent(flowPathExtraContent)
//                            .build()
//            );
//            callbackExtend.callback(
//                    flowPathDAO.getApproverUrl(),
//                    callbackExtend.createNotificationData(
//                            flowPathDAO.getFlowId(),
//                            id,
//                            flowPathDAO.getUuid(),
//                            "approve",
//                            "normal",
//                            flowPathDAO.getTempShowData(),
//                            title,
//                            flowPathExtraContent,
//                            "",
//                            JSONUtil.toJsonStr(stepInfoDTOList),
//                            flowPathDAO.getExtraContent(),
//                            flowPathDAO.getStatus()
//                    )
//            );
//        }
//
//
//        //尝试自动通过
//        if (!approvalUuidList.isEmpty() && flowPathHistoryDAO != null &&
//                (flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.CREATE.getCode() || flowPathHistoryDAO.getAction() == ApproveConstant.flowPathAction.PASS.getCode())) {
//            //相同ID自动通过:[1:流转过的相同节点自动通过;2:连续相同节点自动通过;3:不自动]
//            if (flowPathDAO.getAutoappr() == 2) {
//                //连续id自动通过
//                for (String apprUuid : approvalUuidList) {
//                    if (apprUuid.equals(uuid)) {
//                        title = showName + " 自动通过了 " + flowTitle;
//                        goFlow(id, 2, uuid, showName, reason, appChannelId, dataChannelId, title);
//                        break;
//                    }
//                }
//            } else if (flowPathDAO.getAutoappr() == 1) {
//                Set<String> passUuidSet = new HashSet<>();//已通过的uuid
//                for (Map.Entry<Integer, List<String>> entry : passStepUuidMap.entrySet()) {
//                    passUuidSet.addAll(new HashSet<>(entry.getValue()));
//                }
//                passUuidSet.add(flowPathDAO.getUuid());//创建人也当成已通过
//                for (String apprUuid : approvalUuidList) {
//                    if (passUuidSet.contains(apprUuid)) {
//                        title = showName + " 自动通过了 " + flowPathDAO.getShowName() + "的" + flowTitle;
//                        goFlow(id, 2, apprUuid, "", reason, appChannelId, dataChannelId, title);
//                        break;
//                    }
//                }
//            }
//        }
//        return true;
//    }

    /**
     * 撤回，需要已审核人员确认
     *
     * @param flowPathDAO 审批信息
     * @param passStepList 包含已通过的人员节点
     */
//    private void withdrawnConfirmation(FlowPathDAO flowPathDAO, List<FlowPathStepDTO> passStepList) {
//        // 首先验证是否已存在 该条流程的 撤销确认申请
//        List<FlowPathDAO> flowPathList = flowPathMapper.getFlowPathList(
//                GetFlowPathCondition
//                        .builder()
//                        .withdrawPathId(flowPathDAO.getId())
//                        .status(ApproveConstant.flowPathStatus.WAITING.getCode())
//                        .build(),
//                0,
//                0
//        );
//        if (!flowPathList.isEmpty()) {
//            return;
//        }
//
//        // 1、循环已经通过的步骤
//        for (FlowPathStepDTO stepInfo : passStepList) {
//            // 2、剔除步骤内未审核通过的人
//            List<FlowPathStepNotificationDTO> notificationList = stepInfo.getNotification().stream().filter(flowPathStepNotificationDTO -> flowPathStepNotificationDTO.getStatus() == ApproveConstant.flowPathStatus.PASSED.getCode()).collect(Collectors.toList());
//            stepInfo.setNotification(notificationList);
//        }
//
//        // 3、将当前审批设置为 暂停状态
//        interruptApproval(flowPathDAO.getId());
//
//        // 4、生成新的 DAO
//        flowPathDAO.setTitle(flowPathDAO.getTitle() + "的撤销");
//        flowPathDAO.setStatus(ApproveConstant.flowPathStatus.WAITING.getCode());
//        flowPathDAO.setCreateTime(null);
//        flowPathDAO.setUpdateTime(null);
//        flowPathDAO.setStepInfo(JSONUtil.toJsonStr(passStepList));
//        flowPathDAO.setWithdrawPathId(flowPathDAO.getId());
//
//        // 5、保存新的审批流程
//        if (flowPathMapper.addFlowPath(flowPathDAO) <= 0) {
//            throw new BsException("创建审批流失败");
//        }
//
//        // 6、存储flow_path_step表
////        List<FlowPathStepDTO> newStepInfoDTOList = new ArrayList<>();
//        for (FlowPathStepDTO flowPathStepDTO : passStepList) {
//            List<StepInfoNotificationDTO> stepNotificationList = flowPathStepDTO.getNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
//            List<StepInfoNotificationDTO> stepCcNotificationList = flowPathStepDTO.getCcNotification().stream().map(StepInfoNotificationDTO::create).collect(Collectors.toList());
//            flowPathStepService.addFlowPathStep(
//                    flowPathDAO.getId(),
//                    flowPathStepDTO.getStepNo(),
//                    flowPathStepDTO.getTitle(),
//                    flowPathStepDTO.getPassCons(),
//                    stepNotificationList,
//                    stepCcNotificationList,
//                    flowPathStepDTO.getPassAll(),
//                    flowPathStepDTO.getWithdraw(),
//                    flowPathStepDTO.getGoback(),
//                    ApproveConstant.flowPathStatus.WAITING.getCode(),
//                    ApproveConstant.appiont.NO.getCode(),
//                    ApproveConstant.returnBack.NO.getCode(),
//                    flowPathStepDTO.getExtraContent(),
//                    0,
//                    flowPathStepDTO.getPreId(),
//                    flowPathStepDTO.getStepType()
//            );
////            newStepInfoDTOList.add(FlowPathStepDTO.create(flowPathStepDAO));
//        }
//
//        // 7、获取stepInfoList
//        List<FlowPathStepDTO> newStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
//                .flowPathId(flowPathDAO.getId())
//                .build());
//
//        // 8、存储发起人到 flowPathList
//        // 存储操作历史
//        flowPathHistoryMapper.addFlowPathHistory(
//                FlowPathHistoryDAO
//                        .builder()
//                        .title(flowPathDAO.getTitle())
//                        .flowPathId(flowPathDAO.getId())
//                        .uuid(flowPathDAO.getUuid())
//                        .stepId(0)
//                        .nextStepId(newStepInfoDTOList.get(0).getId())
//                        .action(ApproveConstant.flowPathAction.CREATE.getCode())
//                        .reason("")
//                        .build()
//        );
//        // 存储到 "我的" 审批列表
//        flowPathListMapper.addFlowPathList(
//                FlowPathListDAO
//                        .builder()
//                        .title(flowPathDAO.getTitle())
//                        .flowPathId(flowPathDAO.getId())
//                        .uuid(flowPathDAO.getUuid())
//                        .type(ApproveConstant.flowPathListType.MY.getCode())
//                        .appChannelId(flowPathDAO.getAppChannelId())
//                        .dataChannelId(flowPathDAO.getDataChannelId())
//                        .extraContent(flowPathDAO.getExtraContent())
//                        .build()
//        );
//
//        // 9、获取当前待审核步骤
//        Optional<FlowPathStepDTO> firstWaitingStepInfo = newStepInfoDTOList.stream().filter(newStepInfoDTO -> newStepInfoDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
//        if (firstWaitingStepInfo.isEmpty()) {
//            throw new BsException("创建审批流失败");
//        }
//        FlowPathStepDTO flowPathStepDTO = firstWaitingStepInfo.get();
//
//        // 10、依据当前步骤规则设置待审批人员
//        if (flowPathStepDTO.getPassAll() == ApproveConstant.flowPassAll.ONE_BY_ONE.getCode()) {
//            Optional<FlowPathStepNotificationDTO> firstNotificationInfo = flowPathStepDTO.getNotification().stream().filter(notification -> notification.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
//            if (firstNotificationInfo.isEmpty()) {
//                throw new BsException("创建审批流失败");
//            }
//            FlowPathStepNotificationDTO flowPathStepNotificationDTO = firstNotificationInfo.get();
//
//            setWaitApprove(flowPathDAO, newStepInfoDTOList, flowPathStepNotificationDTO);
//        } else {
//            for (FlowPathStepNotificationDTO notificationInfo : flowPathStepDTO.getNotification()) {
//                setWaitApprove(flowPathDAO, newStepInfoDTOList, notificationInfo);
//            }
//        }
//    }

    /**
     * 设置待审人，并发送通知
     *
     * @param newFlowPathDAO
     * @param newStepInfoDTOList
     * @param flowPathStepNotificationDTO
     */
    private void setWaitApprove(FlowPathDAO newFlowPathDAO, List<FlowPathStepDTO> newStepInfoDTOList, FlowPathStepNotificationDTO flowPathStepNotificationDTO) {
        // 1、未审批人添加待审
        flowPathListMapper.addFlowPathList(
                FlowPathListDAO.builder()
                        .title(newFlowPathDAO.getShowName() + "的" + newFlowPathDAO.getTitle() + "申请")
                        .flowPathId(newFlowPathDAO.getId())
                        .uuid(flowPathStepNotificationDTO.getUuid())
                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
                        .appChannelId(newFlowPathDAO.getAppChannelId())
                        .dataChannelId(newFlowPathDAO.getDataChannelId())
                        .extraContent(JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()))
                        .build()
        );

        // 2、回调业务层-新审批开始流转
        callbackExtend.callback(
                newFlowPathDAO.getApproverUrl(),
                callbackExtend.createNotificationData(
                        newFlowPathDAO.getFlowId(),
                        newFlowPathDAO.getId(),
                        flowPathStepNotificationDTO.getUuid(),
                        "approve",
                        "normal",
                        newFlowPathDAO.getTempShowData(),
                        newFlowPathDAO.getTitle(),
                        JSONUtil.toJsonStr(flowPathStepNotificationDTO.getExtraContent()),
                        newFlowPathDAO.getTempData(),
                        JSONUtil.toJsonStr(newStepInfoDTOList),
                        newFlowPathDAO.getExtraContent(),
                        newFlowPathDAO.getStatus()
                )
        );
    }

    /**
     * 暂停流程，并发送回调
     *
     * @param flowPathId
     */
//    private void interruptApproval(Integer flowPathId) {
//        // 1、更新现有审批流程状态为暂停
//        flowPathMapper.updateFlowPath(
//                FlowPathDAO.builder()
//                        .id(flowPathId)
//                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.YES.getCode())
//                        .build()
//        );
//
//        // 2、获取审批流程数据进行回调
//        // 获取该 撤回目标流程
//        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
//        // 获取 撤回目标流程 步骤
//        List<FlowPathStepDTO> stepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
//                .flowPathId(flowPathId)
//                .build());
//
//        // 3、回调业务层-将原审批暂停
//        callbackExtend.callback(
//                flowPathDAO.getApproverUrl(),
//                callbackExtend.createNotificationData(
//                        flowPathDAO.getFlowId(),
//                        flowPathDAO.getId(),
//                        flowPathDAO.getUuid(),
//                        "approve",
//                        "interrupt",
//                        flowPathDAO.getTempShowData(),
//                        flowPathDAO.getTitle() + "已暂停",
//                        JSONUtil.toJsonStr(flowPathDAO.getExtraContent()),
//                        flowPathDAO.getTempData(),
//                        JSONUtil.toJsonStr(stepInfoDTOList),
//                        flowPathDAO.getExtraContent(),
//                        flowPathDAO.getStatus()
//                )
//        );
//    }

    /**
     * 恢复流程，并发送回调
     *
     * @param flowPathId
     */
//    private void proceedApproval(Integer flowPathId) {
//        // 1、更新撤回目标流程的暂停状态为 恢复
//        flowPathMapper.updateFlowPath(
//                FlowPathDAO.builder()
//                        .id(flowPathId)
//                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
//                        .build()
//        );
//        // 2、获取审批流程数据进行回调
//        // 获取该 撤回目标流程
//        FlowPathDAO withdrawFlowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
//        // 获取 撤回目标流程 步骤
//        List<FlowPathStepDTO> withdrawStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
//                .flowPathId(flowPathId)
//                .build());
//
//        // 3、回调业务层-将原审批恢复
//        callbackExtend.callback(
//                withdrawFlowPathDAO.getApproverUrl(),
//                callbackExtend.createNotificationData(
//                        withdrawFlowPathDAO.getFlowId(),
//                        withdrawFlowPathDAO.getId(),
//                        withdrawFlowPathDAO.getUuid(),
//                        "approve",
//                        "proceed",
//                        withdrawFlowPathDAO.getTempShowData(),
//                        withdrawFlowPathDAO.getTitle() + "已恢复",
//                        JSONUtil.toJsonStr(withdrawFlowPathDAO.getExtraContent()),
//                        withdrawFlowPathDAO.getTempData(),
//                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
//                        withdrawFlowPathDAO.getExtraContent(),
//                        withdrawFlowPathDAO.getStatus()
//                )
//        );
//    }

    /**
     * 正常撤回，直接撤回成功
     *
     * @param flowPathId
     */
//    private void withdrawnPermit(int flowPathId) {
//        // 1、获取 流程信息和步骤信息
//        // 获取该 撤回目标流程
//        FlowPathDAO flowPathDAO = flowPathMapper.getFlowPath(GetFlowPathCondition.builder().id(flowPathId).build());
//        // 获取 撤回目标流程 步骤
//        List<FlowPathStepDTO> withdrawStepInfoDTOList = flowPathStepService.getFlowPathStepList(GetFlowPathStepCondition.builder()
//                .flowPathId(flowPathId)
//                .build());
//
//        // 2、判断该撤回申请是否有关联 撤销目标审批流程 ，如果有，需要把目标流程状态设置为 进行中
//        if (flowPathDAO.getWithdrawPathId() != 0) {
//            proceedApproval(flowPathDAO.getWithdrawPathId());
//        }
//
//        // 3、获取当前审批步骤
//        Optional<FlowPathStepDTO> flowPathStepDTO = withdrawStepInfoDTOList.stream().filter(withdrawStepInfoDTO -> withdrawStepInfoDTO.getStatus() == ApproveConstant.flowPathStatus.WAITING.getCode()).findFirst();
//        if (flowPathStepDTO.isEmpty()) {
//            throw new BsException("撤销目标流程失败");
//        }
//        FlowPathStepDTO withdrawFlowPathStepDTO = flowPathStepDTO.get();
//
//        // 4、更新流程状态
//        flowPathMapper.updateFlowPath(
//                FlowPathDAO.builder()
//                        .id(flowPathId)
//                        .updateTime(new Date())
//                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
//                        .isInterrupt(ApproveConstant.flowPathIsInterrupt.NO.getCode())
//                        .build()
//        );
//
//        // 5、更新flow_path_step表
//        flowPathStepService.update(
//                FlowPathStepDAO.builder()
//                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
//                        .build(),
//                GetFlowPathStepCondition.builder()
//                        .flowPathId(flowPathId)
//                        .stepNo(withdrawFlowPathStepDTO.getStepNo())
//                        .build()
//        );
//
//        // 6、添加历史数据（意义不大）
//        String title = StringUtil.isEmpty(flowPathDAO.getTitle()) ? (flowPathDAO.getFlowTitle() + "已撤回") : flowPathDAO.getTitle();
//        FlowPathHistoryDAO flowPathHistoryDAO = FlowPathHistoryDAO.builder()
//                .title(title)
//                .flowPathId(flowPathId)
//                .uuid(flowPathDAO.getUuid())
//                .stepId(withdrawFlowPathStepDTO.getId())
//                .nextStepId(0)
//                .action(ApproveConstant.flowPathAction.WITHDRAW.getCode())
//                .reason("")
//                .build();
//        flowPathHistoryMapper.addFlowPathHistory(flowPathHistoryDAO);
//
//        // 7、更新 待我 处理的审批到 已处理
//        // 当前实例一起更新
//        flowPathListMapper.update(
//                FlowPathListDAO.builder()
//                        .type(ApproveConstant.flowPathListType.PROCESSED.getCode())
//                        .build(),
//                GetFlowPathListDAOListCondition.builder()
//                        .flowPathId(flowPathId)
//                        .type(ApproveConstant.flowPathListType.WAIT.getCode())
//                        .build()
//        );
//
//        // 8、更新flow_path_step_notification表
////        flowPathStepNotificationService.update(
////                FlowPathStepNotificationDAO.builder()
////                        .status(ApproveConstant.flowPathStatus.WITHDRAWN.getCode())
////                        .updateTime(new Date())
////                        .build(),
////                GetFlowPathStepNotificationCondition.builder()
////                        .flowPathId(withdrawFlowPathStepDTO.getFlowPathId())
////                        .stepNo(withdrawFlowPathStepDTO.getStepNo())
////                        .uuid(flowPathDAO.getUuid())
////                        .type(ApproveConstant.flowPathStepNotificationType.APPROVE.getCode())
////                        .build()
////        );
//
//        /*
//         * 通知回调
//         */
//        callbackExtend.callback(
//                flowPathDAO.getApproverUrl(),
//                callbackExtend.createNotificationData(
//                        flowPathDAO.getFlowId(),
//                        flowPathId,
//                        flowPathDAO.getUuid(),
//                        "approve",
//                        "revocation",
//                        flowPathDAO.getTempShowData(),
//                        title,
//                        "",
//                        flowPathDAO.getTempData(),
//                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
//                        flowPathDAO.getExtraContent(),
//                        flowPathDAO.getStatus()
//                )
//        );
//
//        /*
//         * 拒绝地址回调
//         */
//        Map<String, Object> notificationData = callbackExtend.createNotificationData(
//                flowPathDAO.getFlowId(),
//                flowPathId,
//                flowPathDAO.getUuid(),
//                "approve",
//                "revocation",
//                flowPathDAO.getTempShowData(),
//                title,
//                "",
//                flowPathDAO.getTempData(),
//                JSONUtil.toJsonStr(withdrawStepInfoDTOList),
//                flowPathDAO.getExtraContent(),
//                ApproveConstant.flowPathStatus.WITHDRAWN.getCode()
//        );
//        callbackExtend.callback(flowPathDAO.getRefuseUrl(), notificationData);
//
//        /*
//         * 撤回地址回调
//         */

    /// /        callbackExtend.callback(
    /// /                callbackExtend.createNotificationData(
    /// /                        flowPathDAO.getFlowId(),
    /// /                        flowPathId,
    /// /                        flowPathDAO.getUuid(),
    /// /                        "approve",
    /// /                        "revocation",
    /// /                        flowPathDAO.getTempShowData(),
    /// /                        title,
    /// /                        "",
    /// /                        flowPathDAO.getTempData(),
    /// /                        JSONUtil.toJsonStr(withdrawStepInfoDTOList),
    /// /                        flowPathDAO.getExtraContent(),
    /// /                        flowPathDAO.getStatus()
    /// /                )
    /// /        );
//
//    }
    @Override
    public Map<String, Object> createNotificationData(String flowId, int flowPathId, String uuid, String action, String event, String tempShowData, String title, String extraContent, String tempData, String stepInfoList, String flowPathExtraContent, Integer flowPathStatus) {
        Map<String, Object> notificationData = new HashMap<>();
        notificationData.put("action", action);
        notificationData.put("event", event);
        notificationData.put("title", title);
        notificationData.put("uuid", uuid);
        notificationData.put("flowId", flowId);
        notificationData.put("flowPathId", flowPathId);
        notificationData.put("tempData", tempData);
        notificationData.put("tempShowData", tempShowData);
        notificationData.put("extraContent", extraContent);
        notificationData.put("stepInfoList", stepInfoList);
        notificationData.put("flowPathExtraContent", flowPathExtraContent);
        notificationData.put("flowPathStatus", flowPathStatus);
        return notificationData;
    }

    @Override
    public Integer getFlowCount(GetFlowCondition condition) {
        return flowMapper.getFlowCount(condition);
    }

    @Override
    public List<FlowDTO> getFlowList(GetFlowCondition condition) {
        return flowMapper.getFlowList(condition, 0, 0)
                .stream().map(FlowDTO::create).collect(Collectors.toList());
    }

    @Override
    public List<FlowDTO> getFlowList(GetFlowCondition condition, int start, int limit) {
        return flowMapper.getFlowList(condition, start, limit)
                .stream().map(FlowDTO::create).collect(Collectors.toList());
    }

    @Override
    public List<FlowByListDTO> getFlowListByList(GetFlowCondition condition, int start, int limit) {
        return flowMapper.getFlowListByList(condition, start, limit)
                .stream().map(FlowByListDTO::create).collect(Collectors.toList());
    }

    @Override
    public List<StepDAO> getStepList(MsStepListRequest request) {
        List<StepDAO> stepList = getStepList(GetStepCondition.builder().flowId(request.getFlowId()).build());
        String stepId = request.getStepId();
        if (StrUtil.isEmpty(stepId)) {
            return stepList;
        }
        List<StepDAO> stepDAOList = stepList.stream().filter(step -> step.getSrcId().equals(stepId)).collect(Collectors.toList());
        return stepServiceExtend.getStepList(stepList, stepDAOList.stream().map(StepDAO::getStepId).collect(Collectors.toList()), request.isConditionFlag());
    }

    @Override
    public List<StepDAO> getStepList(GetStepCondition condition) {
        return stepMapper.getStepList(condition, GetStepOrder.builder().build(), 0, 0);
    }

    /**
     * 批量根据 flowId 列表查询所有步骤信息
     *
     * @param flowIds flowId 列表
     * @return 对应所有 flowId 的 StepDAO 集合
     */
    public Map<String, List<StepDAO>> getStepListByFlowIds(List<String> flowIds) {
        if (flowIds == null || flowIds.isEmpty()) {
            return Collections.emptyMap();
        }
        // 1. 调用 Mapper 拿到所有 StepDAO
        List<StepDAO> allSteps = stepMapper.getStepListByFlowIds(flowIds);
        if (allSteps == null || allSteps.isEmpty()) {
            return Collections.emptyMap();
        }
        // 2. 按 flowId 分组
        return allSteps.stream()
                .collect(Collectors.groupingBy(StepDAO::getFlowId));
    }

}
