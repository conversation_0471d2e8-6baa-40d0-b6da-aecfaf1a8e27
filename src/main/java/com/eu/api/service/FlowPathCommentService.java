package com.eu.api.service;


import com.eu.api.domain.condition.GetFlowPathCommentCondition;
import com.eu.api.domain.dao.FlowPathCommentDAO;
import com.eu.api.domain.dto.FlowPathCommentDTO;
import com.eu.api.domain.ms.MsFlowPathCommentAddRequest;
import com.eu.api.domain.ms.MsUpdateDetailsAttachmentRequest;
import com.eu.api.domain.ms.UpdateFlowCommentRequest;

import java.util.List;

/**
 * &#064;DATE: 2024/8/13
 * &#064;AUTHOR: XSL
 *
 */
public interface FlowPathCommentService {

    public FlowPathCommentDAO addComment(MsFlowPathCommentAddRequest request);

    public int getCommentCount(GetFlowPathCommentCondition condition);

    public List<FlowPathCommentDTO> getCommentList(GetFlowPathCommentCondition condition, int start, int limit);

    void updateCommentAttachment(MsUpdateDetailsAttachmentRequest request);

    void updateComment(UpdateFlowCommentRequest request);
}
