package com.eu.common.context;

import com.eu.common.pojo.RequestContext;

import java.util.Objects;

/**
 * 请求来源信息
 * &#064;DATE: 2025/1/14 16:39
 * &#064;AUTHOR: XSL
 *
 */
public class ServletContext {

    /**
     * 获取请求产品ID
     * @return
     */
    public static String getAppChannelId() {
        RequestContext requestContext = RequestServletContext.get();
        if (Objects.isNull(requestContext)) {
            return null;
        } else {
            return requestContext.getAppChannelId();
        }
    }

    /**
     * 获取请求产品ID
     * @return
     */
    public static String getDataChannelId() {
        RequestContext requestContext = RequestServletContext.get();
        if (Objects.isNull(requestContext)) {
            return null;
        } else {
            return requestContext.getDataChannelId();
        }
    }

    /**
     * 获取请求链路ID
     * @return
     */
    public static String getTraceId() {
        RequestContext requestContext = RequestServletContext.get();
        if (Objects.isNull(requestContext)) {
            return null;
        } else {
            return requestContext.getTraceId();
        }
    }

    /**
     * 获取请求IP地址
     * @return
     */
    public static String getIpAddress() {
        RequestContext requestContext = RequestServletContext.get();
        if (Objects.isNull(requestContext)) {
            return null;
        } else {
            return requestContext.getIpAddress();
        }
    }

    /**
     * 获取请求路径
     * @return
     */
    public static String getUri() {
        RequestContext requestContext = RequestServletContext.get();
        if (Objects.isNull(requestContext)) {
            return null;
        } else {
            return requestContext.getUri();
        }
    }


}
