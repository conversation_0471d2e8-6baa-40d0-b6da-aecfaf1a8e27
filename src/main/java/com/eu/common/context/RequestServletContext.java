package com.eu.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.eu.common.pojo.RequestContext;
import com.eu.common.util.StringUtil;

import java.util.Objects;

/**
 * 请求携带信息存储容器 业务不允许直接请求
 * &#064;DATE: 2024/7/9
 * &#064;AUTHOR: XSL
 *
 */
public class RequestServletContext {

    private static final TransmittableThreadLocal<RequestContext> CONTEXT_HOLDER = new TransmittableThreadLocal<>();

    public static void set(RequestContext context) {
        CONTEXT_HOLDER.set(context);
    }

    public static RequestContext get() {
        return CONTEXT_HOLDER.get();
    }

    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    public static boolean isEmpty() {
        RequestContext requestContext = get();
        if (Objects.isNull(requestContext)) {
            return false;
        }
        return StringUtil.isEmpty(requestContext.getAppChannelId()) || StringUtil.isEmpty(requestContext.getDataChannelId());
    }

}
