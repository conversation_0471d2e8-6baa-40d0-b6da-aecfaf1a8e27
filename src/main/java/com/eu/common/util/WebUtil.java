package com.eu.common.util;

import com.eu.common.constant.CodeConstant;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @DATE: 2024/6/17
 * @AUTHOR: XSL
 *
 */
public class WebUtil {

    /**
     * 将数据渲染到前端
     *
     * @param response
     * @param result
     */
    public static void out(HttpServletResponse response, Result result) throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        response.setStatus(200);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        objectMapper.writeValue(response.getOutputStream(), result);
    }

    /**
     * 将数据渲染到前端
     *
     * @param result
     */
    public static void out(Result result) throws IOException {
        HttpServletResponse response = ServletUtil.getResponse();
        out(response, result);
    }

    /**
     * 将数据渲染到前端
     *
     * @param constant
     */
    public static void out(CodeConstant constant) throws IOException {
        HttpServletResponse response = ServletUtil.getResponse();
        out(response, DResult.answer(constant));
    }


}
