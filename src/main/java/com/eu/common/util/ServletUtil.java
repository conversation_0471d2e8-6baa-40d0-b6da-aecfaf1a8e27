package com.eu.common.util;

import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * Servlet工具类
 * @DATE: 2024/6/5
 * @AUTHOR: XSL
 *
 */
public class ServletUtil {

    /**
     * 获取路径上的拼接参数
     *
     * @return
     */
    public static String getParameter(String key) {
        return getRequest().getParameter(key);
    }

    /**
     * 获取String参数
     */
    public static String getParameter(String name, String defaultValue) {
        return StringUtil.toStr(getParameter(name), defaultValue);
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name) {
        return StringUtil.toInt(getParameter(name));
    }

    /**
     * 获取Integer参数
     */
    public static Integer getParameterToInt(String name, Integer defaultValue) {
        return StringUtil.toInt(getParameter(name), defaultValue);
    }

    /**
     * 获取request
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        return getRequestAttributes().getRequest();
    }

    /**
     * 获取response
     *
     * @return
     */
    public static HttpServletResponse getResponse() {
        return getRequestAttributes().getResponse();
    }

    /**
     * 获取请求头参数
     *
     * @param key
     * @return
     */
    public static String getHeader(String key) {
        return getRequest().getHeader(key);
    }

    /**
     * 获取请求方式
     *
     * @return
     */
    public static String getMethod() {
        return getRequest().getMethod();
    }

    /**
     * 获取请求路径
     *
     * @return
     */
    public static String getRequestURI() {
        return getRequest().getRequestURI();
    }

    /**
     * 获取ServletRequestAttributes
     *
     * @return
     */
    private static ServletRequestAttributes getRequestAttributes() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        return (ServletRequestAttributes) attributes;
    }

}
