package com.eu.common.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/**
 * 系统工具类
 * &#064;DATE: 2024/7/5
 * &#064;AUTHOR: XSL
 *
 */
public class SystemUtil extends cn.hutool.system.SystemUtil {

    /**
     * 当前CPU线程数
     * @return
     */
    public static Integer getCPUThread() {
        return Runtime.getRuntime().availableProcessors();
    }

    /**
     * 获取版本号
     * @return version
     */
    public static String getVersion() {
        StringBuilder content = new StringBuilder();
        InputStream inputStream = SystemUtil.class.getClassLoader().getResourceAsStream("version.json");
        if (inputStream != null) {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line);
                }
            } catch (IOException e) {
                return "";
            }
        }
        JSONObject jsonObject = JSONUtil.parseObj(content.toString());
        return jsonObject.getStr("version", "");
    }

}
