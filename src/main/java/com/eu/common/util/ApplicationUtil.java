package com.eu.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Application工具类
 * &#064;DATE: 2024/7/22
 * &#064;AUTHOR: XSL
 *
 */
public class ApplicationUtil {
    private static Map<String, Object> returnMap;
    private static final Lock lock = new ReentrantLock();

    /**
     * 将配置微服务的Map格式转换成可以注入的格式
     */
    public static Map<String, Object> convert(Map<String, Object> map) {
        return convert(JSONUtil.toJsonStr(map));
    }

    /**
     * 将配置微服务的Map格式转换成可以注入的格式
     */
    public static Map<String, Object> convert(String jsonSTR) {
        lock.lock();
        try {
            returnMap = new HashMap<>();
            recursion(jsonSTR, null);
            Map<String, Object> newMap = new HashMap<>(returnMap);
            returnMap = null;
            return newMap;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 递归组装application格式的数据
     * @param jsonValSTR
     */
    private static void recursion(String jsonValSTR, String key) {
        if (StrUtil.isEmpty(jsonValSTR)) {
            return;
        }
        if (StrUtil.isEmpty(key)) {
            if (JSONUtil.isTypeJSONObject(jsonValSTR)) { // 对象 第一次进来
                JSONObject jsonObject = JSONUtil.parseObj(jsonValSTR);
                if (jsonObject.isEmpty()) { // 空字符
                    return;
                }
                Set<String> keySet = jsonObject.keySet();
                for (String keyItem : keySet) {
                    recursion(jsonObject.getStr(keyItem), keyItem);
                }
            }
        } else {
            if (JSONUtil.isTypeJSONObject(jsonValSTR)) {
                JSONObject jsonObject = JSONUtil.parseObj(jsonValSTR);
                if (jsonObject.isEmpty()) {
                    return;
                }
                Set<String> keySet = jsonObject.keySet();
                for (String keyItem : keySet) {
                    recursion(jsonObject.getStr(keyItem), keyItem);
                }
            } else if (JSONUtil.isTypeJSONArray(jsonValSTR)) {  //数组
                JSONArray jsonArray = JSONUtil.parseArray(jsonValSTR);
                if (jsonArray.isEmpty()) {
                    return;
                }
                int size = jsonArray.size();
                List<Object> list = new ArrayList<>();
                for (int i = 0; i < size; i++) {
                    String str = jsonArray.getStr(i);
                    boolean matches = str.matches("-?\\d*(\\.\\d*)?");
                    if (matches) {
                        list.add(new BigDecimal(str));
                    } else {
                        list.add(str);
                    }
                }
                returnMap.put(key, list);
            } else {    //值
                boolean matches = jsonValSTR.matches("-?\\d*(\\.\\d*)?");
                if (matches) {
                    returnMap.put(key, new BigDecimal(jsonValSTR));
                } else {
                    returnMap.put(key, jsonValSTR);
                }
            }
        }
    }

    /**
     * 获取产品自身的产品ID
     * @return
     */
    public static String getConfigUrl() {
        Properties properties = PropertiesUtil.getProperties();
        return properties.getProperty("msConfigDomain", "");
    }

    /**
     * 获取产品自身的数据ID
     * @return
     */
    public static String getAppChannelId() {
        Properties properties = PropertiesUtil.getProperties();
        return properties.getProperty("server.appChannelId", "");
    }

    /**
     * 获取产品自身的数据ID
     * @return
     */
    public static String getDataChannelId() {
        Properties properties = PropertiesUtil.getProperties();
        return properties.getProperty("server.dataChannelId", "");
    }

    /**
     * 获取产品自身的数据ID
     * @return
     */
    public static String getApiKey() {
        Properties properties = PropertiesUtil.getProperties();
        return properties.getProperty("server.apiKey", "");
    }

}
