package com.eu.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.eu.common.pojo.PageLimit;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 分页工具
 * @DATE: 2024/6/7
 * @AUTHOR: XSL
 *
 */
public class PageUtil {


    /**
     * 获取分页对象
     * @param pageNo 页码
     * @param pageSize 每页数据条数
     * @return Page
     */
    public static <T> Page<T> getPage(int pageNo, int pageSize) {
        return new Page<>(pageNo, pageSize);
    }

    /**
     * 前端分页参数 转 SQL查询用分页参数
     * @return PageLimit
     */
    public static PageLimit getPageLimit(Integer page, Integer pageCount) {
        if (Objects.isNull(page) || page == 0) {
            page = 1;
        }
        if (Objects.isNull(pageCount) || pageCount == 0) {
            pageCount = 10;
        }
        return PageLimit.builder().pageStart((page - 1) * pageCount).pageCount(pageCount).build();
    }

    /**
     * 模仿分页操作 截取list集合
     * @param list
     * @return
     * @param <T>
     */
    public static <T> List<T> pageList(List<T> list, Integer page, Integer pageCount) {
        if (Objects.isNull(list) || list.isEmpty()) {
            return new ArrayList<>();
        }
        PageLimit pageLimit = getPageLimit(page, pageCount);
        return list.stream().skip(pageLimit.getPageStart()).limit(pageLimit.getPageCount()).collect(Collectors.toList());
    }


}
