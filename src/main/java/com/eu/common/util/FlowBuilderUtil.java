package com.eu.common.util;

import com.eu.api.domain.dto.flow.Step;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.*;
import java.util.stream.Collectors;

public class FlowBuilderUtil {
    private Map<String, Step> stepMap = new HashMap<>();
    private ObjectMapper mapper = new ObjectMapper();

    /**
     * 构建流程的层级JSON结构
     *
     * @param steps 平面Step列表
     * @return 层级JSON结构的List
     */
    public List<Map<String, Object>> buildFlow(List<Step> steps) {
        // 构建stepId到Step的映射
        for (Step step : steps) {
            stepMap.put(step.getStepId(), step);
        }

        // 找到起始节点
        Step startStep = null;
        for (Step step : steps) {
            String srcId = getSrcId(step);
            if (srcId == null || srcId.isEmpty()) {
                startStep = step;
                break;
            }
        }
        if (startStep == null) {
            return null;
//            throw new IllegalStateException("未找到起始步骤");
        }

        // 使用DFS构建树
        Node root = buildTree(startStep);

        // 构建层级结构
        // 展平树为List（当前需求）
        List<Map<String, Object>> result = new ArrayList<>();
        flattenTree(root, result);
        return result;
    }

    /**
     * 使用DFS构建树形结构
     *
     * @param step 当前步骤
     * @return 构建好的树节点
     */
    private Node buildTree(Step step) {
        Node node = new Node(step);
        List<String> dstIds = getDstIds(step);
        for (String dstId : dstIds) {
            Step nextStep = stepMap.get(dstId);
            if (nextStep != null) {
                Node child = buildTree(nextStep);
                node.children.add(child);
            }
        }
        return node;
    }

    /**
     * 将树展平为List（按深度优先顺序）
     *
     * @param node   当前节点
     * @param result 输出列表
     */
    private void flattenTree(Node node, List<Map<String, Object>> result) {
        Map<String, Object> jsonNode = convertStepToNode(node.step);
        result.add(jsonNode);

        // 递归处理子节点
        for (Node child : node.children) {
            flattenTree(child, result);
        }
    }

    /**
     * 将Step转换为目标JSON节点格式
     */
    private Map<String, Object> convertStepToNode(Step step) {
        Map<String, Object> node = new HashMap<>();
        node.put("title", step.getTitle());
        int nodeType = getNodeType(step);
        node.put("step_id", step.getStepId());
        node.put("type", nodeType);
        node.put("content", step.getContent() != null ? step.getContent() : "");
        node.put("passCons", step.getPassCons() != null && !step.getPassCons().isEmpty() ? Boolean.parseBoolean(step.getPassCons()) : "");

        // 解析notification
        if (step.getNotification() != null && !step.getNotification().isEmpty()) {
            try {
                List<Map<String, Object>> notifications = mapper.readValue(step.getNotification(), List.class);
                node.put("notification", notifications);
                List<String> members = notifications.stream()
                        .map(n -> (String) ((Map) n).get("uuid"))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                node.put("members", members);
            } catch (Exception e) {
                node.put("notification", new ArrayList<>());
                node.put("members", new ArrayList<>());
            }
        } else {
            node.put("notification", new ArrayList<>());
            node.put("members", new ArrayList<>());
        }

        // 解析ccNotification
        if (step.getCcNotification() != null && !step.getCcNotification().isEmpty()) {
            try {
                List<Map<String, Object>> ccNotifications = mapper.readValue(step.getCcNotification(), List.class);
                node.put("ccNotification", ccNotifications);
            } catch (Exception e) {
                node.put("ccNotification", new ArrayList<>());
            }
        } else {
            node.put("ccNotification", new ArrayList<>());
        }

        // 处理conditions字段
        if (step.getConditions() != null && !step.getConditions().isEmpty()) {
            try {
                List<Map<String, Object>> conditions = mapper.readValue(step.getConditions(), List.class);
                node.put("conditions", conditions);
            } catch (Exception e) {
                node.put("conditions", new ArrayList<>());
            }
        } else {
            node.put("conditions", new ArrayList<>());
        }

        node.put("passAll", step.getPassAll());
        try {
            Map<String, Object> extraContent = mapper.readValue(step.getExtraContent(), Map.class);
            node.put("extraContent", extraContent);
        } catch (Exception e) {
            node.put("extraContent", new HashMap<>());
        }

        return node;
    }

    private int getNodeType(Step step) {
        try {
            Map<String, Object> extra = mapper.readValue(step.getExtraContent(), Map.class);
            return extra.containsKey("type") ? (int) extra.get("type") : step.getStepType();
        } catch (Exception e) {
            return step.getStepType();
        }
    }

    // 获取前节点ID
    private String getSrcId(Step step) {
        try {
            Map<String, Object> extra = mapper.readValue(step.getExtraContent(), Map.class);
            return (String) extra.get("srcId");
        } catch (Exception e) {
            return "";
        }
    }

    // 获取后节点ID
    private List<String> getDstIds(Step step) {
        try {
            Map<String, Object> extra = mapper.readValue(step.getExtraContent(), Map.class);
            Object dstIdObj = extra.get("dstId");
            if (dstIdObj instanceof List) {
                return (List<String>) dstIdObj;
            }
            return Collections.emptyList();
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    private static class Node {
        Step step;
        List<Node> children = new ArrayList<>();

        Node(Step step) {
            this.step = step;
        }
    }
}
