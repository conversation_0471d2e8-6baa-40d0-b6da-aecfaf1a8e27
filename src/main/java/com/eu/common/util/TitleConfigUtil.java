package com.eu.common.util;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 *
 */
public class TitleConfigUtil {
    /**
     * 获取匹配 elementId 的值
     * @param jsonArray
     * @return
     */
    public static Map<String,String> getTempShowDataMap(JSONArray jsonArray){
        Map<String,String> resultMap = new HashMap<>();
        if(CollectionUtils.isEmpty(jsonArray)){
            return resultMap;
        }

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject o = jsonArray.getJSONObject(i);
            String componentName = "";
            if (o.containsKey("componentName")){
                componentName = o.getStr("componentName");
                checkFunc(o,componentName,resultMap);
                //resultMap.put(o.getStr("elementId"),value);
            }
        }
        return resultMap;
    }

    public static void checkFunc(JSONObject jsonObject,String componentName,Map<String,String> resultMap){
        if ("input".equals(componentName) || "amount".equals(componentName) || "textarea".equals(componentName)
                || "date-picker".equals(componentName) || "time-picker".equals(componentName) || "text".equals(componentName)
                || "equation".equals(componentName)|| "identifier".equals(componentName)) {
            getInput(jsonObject,resultMap);
        } else if ("select".equals(componentName)) {
            getSelect(jsonObject, resultMap);
        } else if("belong".equals(componentName)){
            getBelong(jsonObject, resultMap);
        }else if("user".equals(componentName)){
            getUser(jsonObject, resultMap);
        }else if("department".equals(componentName)){
            getDepartment(jsonObject, resultMap);
        }else if("associated".equals(componentName)){
            getAssociated(jsonObject, resultMap);
        }else if ("details".equals(componentName)) {
            getDetails(jsonObject, resultMap);
        }else if("data-picker".equals(componentName)){
            // getDataPicker(jsonObject, resultMap);
        }else if("autocomplete".equals(componentName)){
            //getAutocomplete(jsonObject, resultMap);
        }
    }

    public static void getDataPicker(JSONObject jsonObject,Map<String,String> map){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        if (jsonObject.containsKey("value")&&jsonObject.containsKey("label")&&jsonObject.containsKey("field")){
            if(jsonObject.get("value").toString().startsWith("[") && jsonObject.get("value").toString().endsWith("]")){
                JSONArray jsonArray = jsonObject.getJSONArray("value");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                    String label = item.getStr("label");
                    String value = item.getStr("value");
                    map.put(jsonObject.getStr("field")+"_"+label+"_"+jsonObject.getStr("label"),value);
                }
            }
        }
    }

    public static void getAutocomplete(JSONObject jsonObject,Map<String,String> map){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        if (jsonObject.containsKey("value")&&jsonObject.containsKey("label")&&jsonObject.containsKey("field")){
            //value是数组证明是多选下拉框
            if(jsonObject.get("value").toString().startsWith("[") && jsonObject.get("value").toString().endsWith("]")){
                JSONArray jsonArray = jsonObject.getJSONArray("value");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                    String label = item.getStr("label");
                    String value = item.getStr("value");
                    map.put(jsonObject.getStr("field")+"_"+label+"_"+jsonObject.getStr("label"),value);
                }
                //value是对象证明是单选下拉框
            }else if(jsonObject.get("value").toString().startsWith("{") && jsonObject.get("value").toString().endsWith("}")){
                JSONObject jsonValue = jsonObject.getJSONObject("value");
                map.put(jsonObject.getStr("field")+"_"+jsonValue.getStr("label")+"_"+jsonObject.getStr("label"),jsonValue.getStr("value"));
            }

        }
    }

    public static void getDetails(JSONObject jsonObject, Map<String,String> map){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        if (!jsonObject.containsKey("value") || !jsonObject.containsKey("label") || !jsonObject.containsKey("field")) {
            return ;
        }

        JSONArray jsonArray = jsonObject.getJSONArray("value");
        //循环行
        for (int i = 0; i < jsonArray.size(); i++) {
            //判断行是list还是object
            if (jsonArray.get(i) instanceof JSONArray) {
                Map<String,String> detailMap = getTempShowDataMap(jsonArray.getJSONArray(i));
                if (CollectionUtils.isEmpty(detailMap)){
                    continue;
                }
                for (Map.Entry<String, String> entry : detailMap.entrySet()) {
                    map.put(entry.getKey(), entry.getValue());
                }
                detailMap.clear();
            } else if (jsonArray.get(i) instanceof JSONObject) {
                JSONObject o = jsonArray.getJSONObject(i);
                if (o.containsKey("componentName")){
                    String componentName = o.getStr("componentName");
                    checkFunc(o,componentName,map);
                }
            }
        }
    }

    public static void getInput(JSONObject jsonObject,Map<String,String> resultMap) {
        if (Objects.isNull(jsonObject)) {
            return ;
        }
        if (jsonObject.containsKey("value") ) {
            resultMap.put(jsonObject.getStr("elementId"),jsonObject.getStr("value"));
            //return jsonObject.getStr("value");
        }
    }

    public static void getSelect(JSONObject jsonObject,Map<String,String> resultMap){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (jsonObject.containsKey("value")&&jsonObject.containsKey("label")&&jsonObject.containsKey("field")){
            //value是数组证明是多选下拉框
            if(jsonObject.get("value").toString().startsWith("[") && jsonObject.get("value").toString().endsWith("]")){
                JSONArray jsonArray = jsonObject.getJSONArray("value");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                    String value = item.getStr("label");
                    stringBuilder.append(value);
                }
                resultMap.put(jsonObject.getStr("elementId"),stringBuilder.toString());
                //return stringBuilder.toString();
                //value是对象证明是单选下拉框
            }else if(jsonObject.get("value").toString().startsWith("{") && jsonObject.get("value").toString().endsWith("}")){
                JSONObject jsonValue = jsonObject.getJSONObject("value");
                resultMap.put(jsonObject.getStr("elementId"),jsonValue.getStr("label"));
               // return jsonValue.getStr("label");
            }

        }
      //  return "";
    }

    public static void getUser(JSONObject jsonObject,Map<String,String> resultMap){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (jsonObject.containsKey("value") && jsonObject.containsKey("label") && jsonObject.containsKey("field")
                && jsonObject.get("value") instanceof JSONArray) {
            JSONArray jsonArray = jsonObject.getJSONArray("value");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                String name = item.getStr("name");
                stringBuilder.append(name);
            }
        }
        resultMap.put(jsonObject.getStr("elementId"),stringBuilder.toString());
        //return stringBuilder.toString();
    }

    public static void getDepartment(JSONObject jsonObject,Map<String,String> resultMap){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (jsonObject.containsKey("value")&&jsonObject.containsKey("label")&&jsonObject.containsKey("field")){
            //value是数组证明是多选下拉框
            if(jsonObject.get("value").toString().startsWith("[") && jsonObject.get("value").toString().endsWith("]")){
                JSONArray jsonArray = jsonObject.getJSONArray("value");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                    String name = item.getStr("name");
                    stringBuilder.append(name);
                }
                //value是对象证明是单选下拉框
            }else if(jsonObject.get("value").toString().startsWith("{") && jsonObject.get("value").toString().endsWith("}")){
                JSONObject jsonValue = jsonObject.getJSONObject("value");
                stringBuilder.append(jsonValue.getStr("name"));
            }

        }
        resultMap.put(jsonObject.getStr("elementId"),stringBuilder.toString());
        //return stringBuilder.toString();
    }
    public static void getAssociated(JSONObject jsonObject,Map<String,String> resultMap){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        StringBuilder stringBuilder = new StringBuilder();
        if (jsonObject.containsKey("value") && jsonObject.containsKey("label") && jsonObject.containsKey("field")
                && jsonObject.get("value") instanceof JSONArray) {
            JSONArray jsonArray = jsonObject.getJSONArray("value");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject item = jsonArray.getJSONObject(i); // 获取数组中的每个对象
                String title = item.getStr("title");
                stringBuilder.append(title);
            }
        }
        resultMap.put(jsonObject.getStr("elementId"),stringBuilder.toString());
        //return stringBuilder.toString();
    }

    public static void getBelong(JSONObject jsonObject,Map<String,String> resultMap){
        if (Objects.isNull(jsonObject)){
            return ;
        }
        if (jsonObject.containsKey("value")&&jsonObject.containsKey("label")&&jsonObject.containsKey("field")){
            JSONObject jsonValue = jsonObject.getJSONObject("value");
            resultMap.put(jsonObject.getStr("elementId"),jsonValue.getStr("name"));
//            return jsonValue.getStr("name");
        }
    }

    public static void main(String[] args) {
        System.out.println(getTempShowDataMap(JSONUtil.parseArray("[{\"componentName\":\"textarea\",\"elementId\":\"k1NZ1m16iSxp\",\"field\":\"olGbhkYa2XSQ\",\"label\":\"加班事由\",\"value\":\"加班333\",\"isShowInList\":0},{\"componentName\":\"date-picker\",\"elementId\":\"vUbbPrC4nGFm\",\"field\":\"yL57QhgD6cwS\",\"label\":\"开始时间\",\"value\":\"2024-04-18 10:25\",\"isShowInList\":0},{\"componentName\":\"date-picker\",\"elementId\":\"eLhE7Igfv5Sh\",\"field\":\"LKjjbrXhFrnM\",\"label\":\"结束时间\",\"value\":\"2024-04-19 10:25\",\"isShowInList\":0},{\"componentName\":\"input\",\"elementId\":\"g4M4tobIDhu7\",\"field\":\"oIfVLyhSr0R9\",\"label\":\"加班时长\",\"value\":\"8\",\"isShowInList\":0},{\"componentName\":\"audit\",\"elementId\":\"uXRcjc3D9ta0\",\"field\":\"JAckIypY5dYR\",\"label\":\"审批\",\"value\":\"\",\"isShowInList\":0},{\"componentName\":\"input\",\"elementId\":\"u7IQGuF8220u\",\"field\":\"GvlCrLILDlTc\",\"label\":\"表单标识\",\"value\":\"overtime\",\"isShowInList\":0}]")));
    }

}
