package com.eu.common.util;

/**
 * &#064;DATE: 2025/3/18 10:34
 * &#064;AUTHOR: XSL
 *
 */
public class CommonUtil {

    /**
     * 判断是否不是本地IDEA启动
     * @return
     */
    public static boolean isNotIDEA() {
        String javaHome = System.getProperty("java.home");
        if (javaHome.contains("IntelliJ IDEA") || javaHome.contains("JetBrains")) {
            return false;
        }
        String classPath = System.getProperty("java.class.path");
        return !classPath.contains("idea") && !classPath.contains("IntelliJ");  // 本地IDE启动
    }

    /**
     * 判断是否不是Linux系统启动
     * @return
     */
    public static boolean isNotLinux() {
        return !System.getProperty("os.name").toLowerCase().contains("linux");  // 如果是 Linux，返回 false
    }

}
