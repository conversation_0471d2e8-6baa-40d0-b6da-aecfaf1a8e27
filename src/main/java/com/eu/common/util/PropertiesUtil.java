package com.eu.common.util;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Properties;

/**
 * 读取配置文件配置工具类
 * &#064;DATE: 2024/7/9
 * &#064;AUTHOR: XSL
 *
 */
public class PropertiesUtil {

    /**
     * 读取指定properties文件
     * @return
     * @throws IOException
     */
    public static Properties getProperties() {
        Properties properties;
        try {
            properties = getProperties(System.getProperty("user.dir") + File.separator + "config" + File.separator + "application.properties");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return properties;
    }

    /**
     * 读取指定properties文件
     * @param fileName 文件路径
     * @return
     * @throws IOException
     */
    public static Properties getProperties(String fileName) throws IOException {
        File applicationConfigFile = new File(fileName);
        Properties applicationProperties = new Properties();
        if (applicationConfigFile.exists()) {
            InputStream inputStream = Files.newInputStream(applicationConfigFile.toPath());
            applicationProperties.load(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        } else {
            throw new FileNotFoundException(fileName);
        }
        return applicationProperties;
    }
}
