package com.eu.common.util;

import cn.hutool.core.date.DateUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * @DATE: 2024/3/4
 * @AUTHOR: XSL
 * 时间工具类
 */
public class TimeUtil extends DateUtil {

    private final static DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final static SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    /**
     * 获取当前时间戳
     * @return
     */
    public static Long getTimestamp() {
        return Instant.now().toEpochMilli();
    }

    /**
     * 获取当前时间
     * yyyy-MM-dd
     *
     * @return yyyy-MM-dd
     */
    public static String getNowDateStr() {
        return LocalDateTime.now().format(dateFormatter);
    }

    /**
     * 获取当前时间
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getNowTimeStr() {
        return LocalDateTime.now().format(timeFormatter);
    }

    /**
     * 获取几天前的日期
     * yyyy-MM-dd
     *
     * @param day 天
     * @return
     */
    public static String getAgoDaysStr(int day) {
        return nowDate().minusDays(day).format(dateFormatter);
    }

    /**
     * 获取几周前的日期
     * yyyy-MM-dd
     *
     * @param week 周
     * @return
     */
    public static String getAgoWeeksStr(int week) {
        return nowDate().minusWeeks(week).format(dateFormatter);
    }

    /**
     * 获取几月前的日期
     * yyyy-MM-dd
     *
     * @param month 月
     * @return
     */
    public static String getAgoMonthsStr(int month) {
        return nowDate().minusMonths(month).format(dateFormatter);
    }

    /**
     * 获取几年前的日期
     * yyyy-MM-dd
     *
     * @param year 年
     * @return
     */
    public static String getAgoYearsStr(int year) {
        return nowDate().minusYears(year).format(dateFormatter);
    }

    /**
     * 获取本月的最后一天
     * yyyy-MM-dd
     *
     * @return
     */
    public static String getMonthLastDay() {
        return nowDate().with(TemporalAdjusters.lastDayOfMonth()).format(dateFormatter);
    }

    /**
     * 获取本周的第一天
     * yyyy-MM-dd
     *
     * @return
     */
    public static String getWeekFirstDay() {
        LocalDate firstDayOfWeek = nowDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        return firstDayOfWeek.format(dateFormatter);
    }

    /**
     * 获取本周的最后一天
     * yyyy-MM-dd
     *
     * @return
     */
    public static String getWeekLastDay() {
        return nowDate().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY)).format(dateFormatter);
    }

    /**
     * 获取传入时间所在月最后一天
     * @param year
     * @param month
     * @return
     */
    public static String getWeekLastDay(int year, int month) {
        LocalDate dateOfMarch = LocalDate.of(year, month, 1);
        LocalDate lastDayOfMarch = dateOfMarch.withDayOfMonth(dateOfMarch.getMonth().length(dateOfMarch.isLeapYear()));
        return lastDayOfMarch.format(dateFormatter);
    }

    /**
     * Date 转 字符串
     * @param date
     * @return
     */
    public static String toStringDate(Date date) {
        return SDF.format(date);
    }

    /**
     * 获取当前时间
     * @return LocalDate
     */
    private static LocalDate nowDate() {
        return LocalDate.now();
    }

    /**
     * 获取当前时间
     * @return LocalDateTime
     */
    public static LocalDateTime nowDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 获取距离当前时间有多远
     * @param parsedTime
     * @return
     */
    public static Duration getDistance(String parsedTime) {
        return getDistance(LocalDateTime.parse(parsedTime, timeFormatter));
    }

    /**
     * 获取距离当前时间有多少
     * @param parsedTime
     * @return
     */
    public static Duration getDistance(LocalDateTime parsedTime) {
        return Duration.between(parsedTime, nowDateTime());
    }

    public static LocalDateTime stringToLocalDateTime(String dateTimeStr) {
        if(StringUtil.isEmpty(dateTimeStr)){
            return null;
        }
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static LocalDateTime getMaxLocalDateTime() {
        String maxDatetime = "2099-12-31 23:59:59";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(maxDatetime, dateTimeFormatter);
    }

    /**
     * 获取最大时间
     * @return
     */
    public static Date getMaxDate() {
        String maxDatetime = "2099-12-31 23:59:59";
        return string2Date(maxDatetime);
    }

    /**
     * 转换指定时间
     * @param dateString
     * @return
     */
    public static LocalDateTime parseLocalDateTime(String dateString) {
        if(StringUtil.isEmpty(dateString)){
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            return LocalDateTime.parse(dateString, formatter);
        } catch (DateTimeParseException e) {
            return null;
        }
    }

    public static Date string2Date(String dateTime) {
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return string2Date(dateTime, f);
    }

    public static Date string2Date(String dateTime, SimpleDateFormat f) {
        Date d = null;
        if(!StringUtil.isEmpty(dateTime) && Objects.nonNull(f)){
            try {
                d = f.parse(dateTime);
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return d;
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        try {
            Instant instant = date.toInstant();
            ZoneId systemZone = ZoneId.systemDefault();
            return instant.atZone(systemZone).toLocalDateTime();
        } catch (Exception e) {
            return null;
        }
    }
}
