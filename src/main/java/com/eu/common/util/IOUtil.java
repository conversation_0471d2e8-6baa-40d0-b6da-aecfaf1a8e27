package com.eu.common.util;

import cn.hutool.core.io.IoUtil;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

/**
 * 文件流相关操作工具类
 * @DATE: 2024/6/12
 * @AUTHOR: XSL
 *
 */
public class IOUtil extends IoUtil {

    /**
     * 读取指定路径文件的文本或JSON格式文件内容
     * @param filePath
     * @return
     */
    public static String reader(String filePath) {
        StringBuilder builder = new StringBuilder();
        try {

            FileReader fileReader = new FileReader(filePath);   // 使用FileReader打开文件
            BufferedReader bufferedReader = new BufferedReader(fileReader);  // 将FileReader包装进BufferedReader以提高读取效率
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                builder.append(line);
            }
            bufferedReader.close(); // 关闭资源，先关闭外层包装的流，再关闭底层的流
            fileReader.close();
        } catch (IOException e) {
            System.out.println("读取文件时发生错误：" + e.getMessage());// 处理文件读取异常
        }
        return builder.toString();
    }

}
