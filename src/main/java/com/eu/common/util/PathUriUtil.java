package com.eu.common.util;

import lombok.Getter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 路径工具类
 * &#064;DATE: 2025/1/17 17:06
 * &#064;AUTHOR: XSL
 *
 */
public class PathUriUtil {

    @Getter
    private static final Set<String> paths;

    static {
        paths = new HashSet<>();
        RequestMappingHandlerMapping handlerMapping = SpringUtil.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = handlerMapping.getHandlerMethods();

        for (Map.Entry<RequestMappingInfo, HandlerMethod> entry : map.entrySet()) {
            RequestMappingInfo requestMappingInfo = entry.getKey();
//            Set<String> directPaths = handlerMapping.getHandlerMethods()
            Set<String> patterns = requestMappingInfo.getPatternsCondition().getPatterns();
            paths.addAll(patterns);
        }
    }

    /**
     * 判断路径是否存在
     * @param path
     * @return
     */
    public static boolean containsPath(String path) {
        return paths.contains(path);
    }

}
