package com.eu.common.result;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * &#064;DATE: 2025/2/11 10:15
 * &#064;AUTHOR: XSL
 *
 */
@Data
public class DataList<T> {

    /**
     * 数据
     */
    private List<T> list;

    /**
     * 数量
     */
    private Long count;

    public static <T> DataList<T> create(List<T> list, Long count) {
        DataList<T> dataList = new DataList<>();
        dataList.setList(list);
        dataList.setCount(count);
        return dataList;
    }

    public static <T> DataList<T> create() {
        return create(new ArrayList<>(), 0L);
    }

    public static <T> DataList<T> create(List<T> list) {
        return create(list, (long) list.size());
    }

    public static <T> DataList<T> create(long count) {
        return create(new ArrayList<>(), count);
    }

}
