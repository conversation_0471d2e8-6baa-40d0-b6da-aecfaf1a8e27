package com.eu.common.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 公共返回封装基类
 * &#064;DATE:  2024/5/20
 * &#064;AUTHOR:  XSL
 *
 */
@Data
public class Result implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 请求是否成功
     */
    private Integer code;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 发生时间
     */
    private String dateTime;

}
