package com.eu.common.result;

import com.eu.common.constant.CodeConstant;
import com.eu.common.util.TimeUtil;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 多条数据返回封装类
 * &#064;DATE:  2024/5/20
 * &#064;AUTHOR:  XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LResult<T> extends Result {

    /**
     * 数据
     */
    private DataList<T> data;

    /**
     * 成功
     * @return
     */
    public static <T> LResult<T> success() {
        LResult<T> result = new LResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        DataList<T> dataList = new DataList<>();
        dataList.setCount(0L);
        dataList.setList(new ArrayList<>());
        result.setData(dataList);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 成功
     * @param list 全部数据
     * @return
     */
    public static <T> LResult<T> success(List<T> list) {
        return success(list, list.size());
    }

    /**
     * 成功
     * @param list 全部数据
     * @return
     */
    public static <T> LResult<T> success(DataList<T> list) {
        LResult<T> result = new LResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        result.setData(list);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 成功
     * @param list 当前页数据
     * @param count 数据总数
     * @return
     */
    public static <T> LResult<T> success(List<T> list, Long count) {
        LResult<T> result = new LResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        DataList<T> dataList = new DataList<>();
        dataList.setCount(count);
        dataList.setList(list);
        result.setData(dataList);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 成功
     * @param list 当前页数据
     * @param count 数据总数
     * @return
     */
    public static <T> LResult<T> success(List<T> list, Integer count) {
        LResult<T> result = new LResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        DataList<T> dataList = new DataList<>();
        dataList.setList(list);
        if (count != null) {
            dataList.setCount(Integer.toUnsignedLong(count));
        } else {
            dataList.setCount(0L);
        }
        result.setData(dataList);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 失败
     * @return
     */
    public static <T> LResult<T> fail() {
        LResult<T> result = new LResult<>();
        result.setCode(CodeConstant.ERROR.getCode());
        result.setMessage(CodeConstant.ERROR.getMessage());
        DataList<T> dataList = new DataList<>();
        dataList.setCount(0L);
        dataList.setList(new ArrayList<>());
        result.setData(dataList);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }


}
