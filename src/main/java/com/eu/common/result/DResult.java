package com.eu.common.result;

import com.eu.common.constant.CodeConstant;
import com.eu.common.util.TimeUtil;
import lombok.*;

/**
 * 单条数据返回封装类
 * &#064;DATE:  2024/5/20
 * &#064;AUTHOR:  XSL
 *
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DResult<T> extends Result {

    /**
     * 数据体
     */
    private T data;

    /**
     * 成功
     * @return
     */
    public static <T> DResult<T> success() {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        result.setData(null);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 成功
     * @param data
     * @return
     */
    public static <T> DResult<T> success(T data) {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(CodeConstant.SUCCESS.getMessage());
        result.setData(data);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 成功
     * @param data
     * @param message
     * @return
     */
    public static <T> DResult<T> success(T data, String message) {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.SUCCESS.getCode());
        result.setMessage(message);
        result.setData(data);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 失败
     * @return
     */
    public static <T> DResult<T> fail() {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.ERROR.getCode());
        result.setMessage(CodeConstant.ERROR.getMessage());
        result.setData(null);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 失败
     * @param message
     * @return
     */
    public static <T> DResult<T> fail(String message) {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.ERROR.getCode());
        result.setMessage(message);
        result.setData(null);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 失败
     * @param message
     * @return
     */
    public static <T> DResult<T> fail(T data, String message) {
        DResult<T> result = new DResult<>();
        result.setCode(CodeConstant.ERROR.getCode());
        result.setMessage(message);
        result.setData(data);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 响应
     * @param codeConstant
     * @return
     */
    public static <T> DResult<T> answer(CodeConstant codeConstant) {
        DResult<T> result = new DResult<>();
        result.setCode(codeConstant.getCode());
        result.setMessage(codeConstant.getMessage());
        result.setData(null);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }

    /**
     * 响应
     * @param code
     * @param message
     * @return
     */
    public static <T> DResult<T> answer(Integer code, String message) {
        DResult<T> result = new DResult<>();
        result.setCode(code);
        result.setMessage(message);
        result.setData(null);
        result.setDateTime(TimeUtil.getNowTimeStr());
        return result;
    }


}
