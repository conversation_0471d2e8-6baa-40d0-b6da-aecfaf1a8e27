package com.eu.common.base;


import com.eu.common.constant.CodeConstant;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;

/**
 * &#064;DATE: 2024/11/1
 * &#064;AUTHOR: XSL
 *
 */
public class BaseController {

    /**
     * 请求参数为空
     * @return
     */
    public final Result none() {
        return DResult.answer(CodeConstant.BUSINESS_PARAM_NONE);
    }

    /**
     * 字段不能为空时调用
     * @param message
     * @return
     */
    public final Result none(String message) {
        return DResult.answer(CodeConstant.BUSINESS_PARAM_NONE.getCode(), message);
    }


}
