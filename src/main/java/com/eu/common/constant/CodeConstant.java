package com.eu.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码封装
 * @DATE: 2024/5/20
 * @AUTHOR: XSL
 *
 */
@Getter
@AllArgsConstructor
public enum CodeConstant {

    SUCCESS(20000, "成功"),
    ERROR(50000, "失败"),
    SERVER_ERROR(50000, "审批流错误"),
    HTTP_ERROR(55555, "请求第三方发生错误"),
    BUSINESS_ERROR(50000, "请求业务发生未知错误"),
    BUSINESS_PARAM_NONE(60001, "请求参数不能为空"),
    MS_ERROR(50000, "微服务请求异常"),
    AUTHORITY_DENIED(400, "未携带来源信息"),
    PATH_NONE(404, "请求路径不存在");

    private final Integer code;
    private final String message;


}
