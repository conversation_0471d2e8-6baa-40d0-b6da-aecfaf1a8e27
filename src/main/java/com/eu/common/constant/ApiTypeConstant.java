package com.eu.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 接口操作类型
 * @DATE: 2024/5/31
 * @AUTHOR: XSL
 *
 */
@Getter
@AllArgsConstructor
public enum ApiTypeConstant {

    INSERT(1, "新增"),
    DELETE(2, "删除"),
    UPDATE(3, "更新"),
    SELECT(4, "查询"),
    INFO(5, "详情"),
    EXPORT(6, "导出"),
    DOWNLOAD(7, "下载");


    private final Integer code;
    private final String message;

}
