package com.eu.common.application;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.eu.common.holder.ApplicationHolder;
import com.eu.common.ms.config.ConfigMs;
import com.eu.common.ms.config.pojo.request.ConfigRequest;
import com.eu.common.param.MicroServiceParam;
import com.eu.common.param.SystemParam;
import com.eu.common.util.ApplicationUtil;
import com.eu.common.util.PropertiesUtil;
import com.eu.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * 启动注入实现方法,归为配置类
 * &#064;DATE: 2024/7/22
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
public class ApplicationConfig {

    private static Map<String, Object> CONFIG;

    /**
     * 获取配置微服务
     * @return Map
     */
    public static Map<String, Object> getConfigMap() {
        Properties properties = PropertiesUtil.getProperties();
        Object msConfigDomain = properties.get("msConfigDomain");
        String appChannelId = properties.get("server.appChannelId").toString();
        if (Objects.isNull(msConfigDomain) || Objects.isNull(appChannelId)) {
            log.info("缺少主要配置项");
            System.exit(90002);
        }
        CONFIG = ApplicationUtil.convert(ConfigMs.config(ConfigRequest.builder().appChannelId(appChannelId).build()));

        CONFIG.put("spring.datasource.url", "**********************************************************************************************************");

        properties.forEach((Key, value) -> CONFIG.put(Key.toString(), value));
        systemConfig(CONFIG);

        return CONFIG;

    }

    /**
     * 注入参数 及 热更新
     */
    public static void inject(boolean flag) {
        JSONObject jsonObject;
        if (flag) {
            jsonObject = JSONUtil.parseObj(getConfigMap());
        } else {
            jsonObject = JSONUtil.parseObj(CONFIG);
        }
        ApplicationHolder.setMicroServiceParam(jsonObject.toBean(MicroServiceParam.class));
        ApplicationHolder.setSystemParam(jsonObject.toBean(SystemParam.class));
    }

    /**
     * 注入系统配置
     * @param configMap
     */
    public static void systemConfig(Map<String, Object> configMap) {
        if (Objects.isNull(configMap) || configMap.isEmpty()) {
            return;
        }

        /*
         * Spring
         */
        configMap.put("spring.main.banner-mode", "off");

        /*
         * Tomcat
         */
//        configMap.put("server.tomcat.uri-encoding", "UTF-8");
//        configMap.put("server.tomcat.accept-count", 1000);
//        configMap.put("server.tomcat.threads.max", 800);
//        configMap.put("server.tomcat.threads.min-spare", 100);

        /*
         * mybatis-plus
         */
        configMap.put("mybatis-plus.global-config.banner", false);
        configMap.put("mybatis-plus.mapperLocations", "classpath*:mapper/**/*Mapper.xml");
        configMap.put("mybatis-plus.type-aliases-package", "com.eu.api.domain.entity");
        if (StringUtil.logRank((String) configMap.getOrDefault("logging.level.root", "OFF")) > 3) {
            configMap.put("mybatis-plus.configuration.log-impl", "org.apache.ibatis.logging.stdout.StdOutImpl");
        }

    }

}
