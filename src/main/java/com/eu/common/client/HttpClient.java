package com.eu.common.client;

import com.eu.common.exception.HttpException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * &#064;DATE: 2025/1/14 16:02
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
public class HttpClient {

    private static final OkHttpClient client;

    static {
        client = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)  // 设置连接超时时间
                .readTimeout(5, TimeUnit.SECONDS)     // 设置读取超时时间
                .writeTimeout(5, TimeUnit.SECONDS)    // 设置写入超时时间
                .build();
    }

    /**
     * GET 请求
     * @param url
     * @return
     */
    public static String get(String url) {
        return get(url, new HashMap<>());
    }


    /**
     * GET 请求
     * @param url 请求地址
     * @return
     */
    public static String get(String url, Map<String, String> header) {
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(header))
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("" + response);
            }
            ResponseBody body = response.body();
            if (Objects.isNull(body)) {
                return null;
            }
            return body.string();
        } catch (Exception exception) {
            throw new HttpException();
        }
    }

    /**
     * POST 请求
     * @param url
     * @param json
     * @return
     */
    public static String post(String url, String json) {
        return post(url, json, new HashMap<>());
    }

    /**
     * POST 请求
     * @param url
     * @param json
     * @return
     */
    public static String post(String url, String json, Map<String, String> header) {
        MediaType JSON = MediaType.get("application/json; charset=utf-8");
        RequestBody requestBody = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .headers(Headers.of(header))
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("" + response);
            }
            ResponseBody body = response.body();
            if (Objects.isNull(body)) {
                return null;
            }
            return body.string();
        } catch (Exception exception) {
            log.error(exception.getMessage(), exception);
            throw new HttpException();
        }
    }

    /**
     * 异步 GET 请求
     * @param url
     * @param callback
     */
    public static void getAsync(String url, Callback callback) {
        Request request = new Request.Builder()
                .url(url)
                .build();
        client.newCall(request).enqueue(callback);
    }

    /**
     * 异步 POST 请求
     * @param url
     * @param json
     * @param callback
     */
    public static void postAsync(String url, String json, Callback callback) {
        MediaType JSON = MediaType.get("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(json, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        client.newCall(request).enqueue(callback);
    }


}