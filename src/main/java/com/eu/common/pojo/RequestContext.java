package com.eu.common.pojo;

import lombok.*;

/**
 * 请求头部信息
 * &#064;DATE:  2024/5/26
 * &#064;AUTHOR:  XSL
 *
 */
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class RequestContext {

    /**
     * 产品ID
     */
    private String appChannelId;

    /**
     * 数据渠道ID
     */
    private String dataChannelId;

    /**
     * 链路ID
     */
    private String traceId;

    /**
     * 请求时的IP地址
     */
    private String ipAddress;

    /**
     * 接口请求的地址
     */
    private String uri;

}
