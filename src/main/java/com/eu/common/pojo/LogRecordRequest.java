package com.eu.common.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 日志记录实体类
 * &#064;DATE: 2024/7/23
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogRecordRequest {

    /**
     * 接口名称
     */
    private String name;

    /**
     * 接口所属模块
     */
    private String module;

    /**
     * 接口操作类型
     */
    private Integer type;

    /**
     * IP地址
     */
    private String requestIP;

    /**
     * 产品ID
     */
    private String requestAppChannelId;

    /**
     * 数据ID
     */
    private String requestDataChannelId;

    /**
     * 链路ID(整条流水线)
     */
    private String traceId;

    /**
     * 消耗时间
     */
    private Long elapsedTime;

    /**
     * 请求方式
     */
    private Integer method;

    /**
     * 请求参数
     */
    private String requestBody;

    /**
     * 返回参数
     */
    private String responseBody;

    /**
     * 请求接口
     */
    private String requestURI;

    /**
     * 成功或失败标识
     */
    private String success;

    /**
     * 错误信息
     */
    private String exception;

    /**
     * 日志创建时间
     */
    private String createTime;

    /**
     * 方法路径
     */
    private String methodPath;

}
