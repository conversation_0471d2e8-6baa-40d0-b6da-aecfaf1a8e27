package com.eu.common.annotation;


import com.eu.common.constant.ApiTypeConstant;

import java.lang.annotation.*;

/**
 * 日志记录注解
 * @DATE: 2024/5/19
 * @AUTHOR: XSL
 *
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.METHOD})
public @interface LogRecord {

    /**
     * 功能名称
     * @return
     */
    String name();

    /**
     * 功能模块
     * @return
     */
    String module();

    /**
     * 操作类型
     * @return
     */
    ApiTypeConstant type();

}
