package com.eu.common.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁用注解(锁方法)
 * &#064;DATE:  2024/12/25
 * &#064;AUTHOR:  XSL
 *
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.METHOD})
public @interface MLock {

    /**
     * 排队获取锁的等待时长
     * @return
     */
    int loading() default 5;

    /**
     * 上锁最长时间,超过后自动释放锁
     * @return
     */
    int unLock() default 3;

    /**
     * 上锁时间单位
     * @return
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

}
