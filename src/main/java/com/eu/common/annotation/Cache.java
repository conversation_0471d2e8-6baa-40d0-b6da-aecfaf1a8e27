package com.eu.common.annotation;

import java.lang.annotation.*;
import java.time.temporal.ChronoUnit;

/**
 * 读取缓存中的数据
 * &#064;DATE:  2024/12/23
 * &#064;AUTHOR:  XSL
 *
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.METHOD})
public @interface Cache {

    /**
     * 键前缀
     * @return
     */
    String prefix();

    /**
     * 键
     * @return
     */
    String key();

    /**
     * 缓存时间
     * @return
     */
    long cache() default 0;

    /**
     * 缓存时间单位
     * @return
     */
    ChronoUnit timeUnit() default ChronoUnit.MINUTES;

}
