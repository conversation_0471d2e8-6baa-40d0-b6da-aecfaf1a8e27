package com.eu.common.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 分布式锁用注解(锁数据)
 * &#064;DATE:  2024/12/24
 * &#064;AUTHOR:  XSL
 *
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.PARAMETER, ElementType.METHOD})
public @interface DLock {

    /**
     * 键前缀
     * @return
     */
    String prefix();

    /**
     * 键
     * @return
     */
    String key();

    /**
     * 排队获取锁的等待时长
     * @return
     */
    long loading() default 5;

    /**
     * 上锁最长时间,超过后自动释放锁
     * @return
     */
    long unLock() default 3;

    /**
     * 上锁时间单位
     * @return
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

}
