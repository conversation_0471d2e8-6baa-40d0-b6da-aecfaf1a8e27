package com.eu.common.param;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 微服务相关配置 实体类
 * &#064;DATE: 2024/7/9
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Component
public class MicroServiceParam {

    /**
     * 配置微服务地址
     */
    @Value("${ms.configDomain:}")
    private String msConfigDomain;

    /**
     * 日志微服务地址
     */
    @Value("${ms.logDomain:}")
    private String msLogDomain;

}
