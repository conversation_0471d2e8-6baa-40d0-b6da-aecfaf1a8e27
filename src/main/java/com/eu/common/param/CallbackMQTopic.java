package com.eu.common.param;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * &#064;DATE: 2025/4/28 18:07
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Component
public class CallbackMQTopic {

    /**
     * 青易发票处理回调   apis/invoice/card/approveCallback
     */
    @Value("${mq.invoiceCardApproveCallback:}")
    private String invoiceCardApproveCallback;

    /**
     * 青易通过通知   apis/approval/callback/pass
     */
    @Value("${mq.approvalCallbackPass:}")
    private String approvalCallbackPass;

    /**
     * 青易PHP通过通知    api/approvePass/approval/approval
     */
    @Value("${mq.approvePassApprovalApproval:}")
    private String approvePassApprovalApproval;

    /**
     * 青易审批流拒绝回调    apis/approval/callback/reject
     */
    @Value("${mq.approvalCallbackReject:}")
    private String approvalCallbackReject;

    /**
     * 青易PHP拒绝通知    api/approveReject/approval/approval
     */
    @Value("${mq.approveRejectApprovalApproval:}")
    private String approveRejectApprovalApproval;

    /**
     * 青易审批流通知回调    apis/approval/callback/notify
     */
    @Value("${mq.approvalCallbackNotify:}")
    private String approvalCallbackNotify;

    /**
     * 青易PHP审批通知    api/approveNotify/approval/approval
     */
    @Value("${mq.approveNotifyApprovalApproval:}")
    private String approveNotifyApprovalApproval;

    /**
     * 吉牛瑞联表单回调     apis/template/callback
     */
    @Value("${mq.templateCallback:}")
    private String templateCallback;

    /**
     * 吉牛客商回调       apis/template/customerCallback
     */
    @Value("${mq.templateCustomerCallback:}")
    private String templateCustomerCallback;

    /**
     * 吉牛通过回调       api/MsApproval/pass
     */
    @Value("${mq.msApprovalPass:}")
    private String msApprovalPass;

    /**
     * 吉牛拒绝回调地址     api/MsApproval/refuse
     */
    @Value("${mq.msApprovalRefuse:}")
    private String msApprovalRefuse;

}
