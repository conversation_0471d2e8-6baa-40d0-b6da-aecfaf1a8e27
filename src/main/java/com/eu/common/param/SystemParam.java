package com.eu.common.param;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * &#064;DATE: 2025/1/16 10:34
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Component
public class SystemParam {

    /**
     * 产品ID
     */
    @Value("${server.appChannelId:}")
    private String appChannelId;

    /**
     * 数据ID
     */
    @Value("${server.dataChannelId:}")
    private String dataChannelId;

    /**
     * API_KEY
     */
    @Value("${server.apiKey:}")
    private String apiKey;

}
