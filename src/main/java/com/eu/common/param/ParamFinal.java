package com.eu.common.param;

import com.eu.common.util.IpUtil;
import com.eu.common.util.SystemUtil;

/**
 * &#064;DATE: 2025/1/14 15:24
 * &#064;AUTHOR: XSL
 *
 */
public class ParamFinal {

    /**
     * 自动放行
     */
    public static final String[] PASS_URI_ARRAY = {"/error", "/ping", "/heart"};

    /**
     * 请求头 产品ID
     */
    public static final String HEADER_APP_CHANNEL_ID = "x-app_channel_id";

    /**
     * 请求头 数据ID
     */
    public static final String HEADER_DATA_CHANNEL_ID = "x-data_channel_id";

    /**
     * 请求头 本次请求链路ID
     */
    public static final String HEADER_TRACE_ID = "traceId";

    /**
     * 项目启动时间key
     */
    public static final String BOOT_BOOT_KEY = "ms-approve:bootTime";

    /**
     * 公网IP地址
     */
    public static final String PUBLIC_NETWORK = IpUtil.getPublicNetwork();

    /**
     * 内网IP地址
     */
    public static final String PRIVATE = IpUtil.getPrivateNetwork();

    /**
     * 系统版本号
     */
    public static final String VERSION = SystemUtil.getVersion();

}
