package com.eu.common.holder;

import java.util.HashMap;
import java.util.Map;

/**
 * &#064;DATE: 2025/1/23 11:15
 * &#064;AUTHOR: XSL
 *
 */
public class HttpHeaderHolder {

    public static Map<String, String> getHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("x-app_channel_id", ApplicationHolder.getAppChannelId());
        header.put("x-data_channel_id", ApplicationHolder.getDataChannelId());
        return header;
    }
}
