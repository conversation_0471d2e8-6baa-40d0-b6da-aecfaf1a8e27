package com.eu.common.holder;

import com.eu.common.param.MicroServiceParam;
import com.eu.common.param.SystemParam;
import com.eu.common.util.ApplicationUtil;
import com.eu.common.util.SpringUtil;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 获取注入的参数
 * &#064;DATE: 2024/7/19
 * &#064;AUTHOR: XSL
 */
@Component
public class ApplicationHolder implements ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(ApplicationHolder.class);
    private static ApplicationContext applicationContext;

    private static MicroServiceParam microServiceParam = SpringUtil.getBean(MicroServiceParam.class);
    private static SystemParam systemParam = SpringUtil.getBean(SystemParam.class);

    public static void setMicroServiceParam(MicroServiceParam microServiceParam) {
        ApplicationHolder.microServiceParam = microServiceParam;
    }

    public static void setSystemParam(SystemParam systemParam) {
        ApplicationHolder.systemParam = systemParam;
    }

    /**
     * 获取配置微服务地址
     *
     * @return String 配置微服务地址
     */
    public static String getConfigURL() {
        MicroServiceParam microServiceParam = null;
        try {
            microServiceParam = ApplicationHolder.applicationContext.getBean(MicroServiceParam.class);
        } catch (BeansException e) {
            log.error("获取配置微服务配置失败");
        }
        return microServiceParam != null ? microServiceParam.getMsConfigDomain() : "";
    }

    /**
     * 获取日志微服务地址
     *
     * @return String 日志微服务地址
     */
    public static String getLogURL() {
        MicroServiceParam microServiceParam = null;
        try {
            microServiceParam = ApplicationHolder.applicationContext.getBean(MicroServiceParam.class);
        } catch (BeansException e) {
            log.error("获取微日志服务配置失败");
        }
        return microServiceParam != null ? microServiceParam.getMsLogDomain() : "";
    }

    /**
     * 获取产品ID
     *
     * @return
     */
    public static String getAppChannelId() {
        SystemParam systemParam = null;
        try {
            systemParam = ApplicationHolder.applicationContext.getBean(SystemParam.class);
        } catch (BeansException e) {
            log.error("获取系统配置失败");
        }
        return systemParam != null ? systemParam.getAppChannelId() : "";
    }

    /**
     * 获取数据ID
     *
     * @return
     */
    public static String getDataChannelId() {
        SystemParam systemParam = null;
        try {
            systemParam = ApplicationHolder.applicationContext.getBean(SystemParam.class);
        } catch (BeansException e) {
            log.error("获取系统配置失败");
        }
        return systemParam != null ? systemParam.getDataChannelId() : "";
    }

    /**
     * 获取数据ID
     *
     * @return
     */
    public static String getApiKey() {
        SystemParam systemParam = null;
        try {
            systemParam = ApplicationHolder.applicationContext.getBean(SystemParam.class);
        } catch (BeansException e) {
            log.error("获取系统配置失败");
        }
        return systemParam != null ? systemParam.getApiKey() : "";
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        ApplicationHolder.applicationContext = applicationContext;
    }
}