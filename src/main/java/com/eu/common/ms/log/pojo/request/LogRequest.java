package com.eu.common.ms.log.pojo.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * &#064;DATE: 2025/1/15 10:32
 * &#064;AUTHOR: XSL
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogRequest {

    /**
     * 业务日志对象（JSON 类型）数组，序列化后字符串
     *
     */
    private List<String> content;

}
