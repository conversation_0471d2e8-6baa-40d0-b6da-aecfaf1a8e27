package com.eu.common.ms.log;

import com.eu.common.holder.ApplicationHolder;
import com.eu.common.ms.log.pojo.request.LogRequest;
import com.mssdk.log.Log;
import com.mssdk.log.response.GetLogMessageResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * &#064;DATE: 2025/1/14 18:16
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
public class LogMs {

    private static Log getLog() {
        return new Log(
                ApplicationHolder.getLogURL(),
                ApplicationHolder.getAppChannelId(),
                ApplicationHolder.getDataChannelId(),
                ApplicationHolder.getApiKey(),
                true,
                ""
        );
    }


    /**
     * 日志新增
     *
     * @param logRequest
     */
    public static void logAdd(LogRequest logRequest) {
        GetLogMessageResponse getLogMessageResponse = getLog().addLog(logRequest.getContent());
        log.info("日志{}",getLogMessageResponse.getMessage());
    }

}
