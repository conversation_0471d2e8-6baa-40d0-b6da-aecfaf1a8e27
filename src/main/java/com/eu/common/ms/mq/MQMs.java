package com.eu.common.ms.mq;

import com.eu.common.holder.ApplicationHolder;
import com.eu.common.util.SpringUtil;
import com.mssdk.BalancerManager;
import com.mssdk.messageagent.MessageAgent;

public class MQMs {

    private final static BalancerManager balancerManager = SpringUtil.getBean(BalancerManager.class);

    private static MessageAgent getMsMessageAgentInstance() {
        String MS_MESSAGE_AGENT = "ms-message-agent";
        return new MessageAgent(balancerManager.getUrl(MS_MESSAGE_AGENT), ApplicationHolder.getAppChannelId(),
                ApplicationHolder.getDataChannelId(), ApplicationHolder.getApiKey(),
                "");
    }

    /**
     * 推送消息队列
     * @param topicNo 主题ID
     * @param data 数据
     * @param delay
     */
    public static void push(String topicNo, String data, int delay) {
        getMsMessageAgentInstance().push(
                topicNo,
                data,
                delay,
                ApplicationHolder.getAppChannelId(),
                ApplicationHolder.getDataChannelId()
        );
    }
}
