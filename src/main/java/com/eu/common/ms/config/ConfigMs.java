package com.eu.common.ms.config;

import com.eu.common.holder.ApplicationHolder;
import com.eu.common.ms.config.pojo.request.ConfigRequest;
import com.eu.common.util.ApplicationUtil;
import com.mssdk.configuration.Config;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * &#064;DATE: 2025/1/15 12:07
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
public class ConfigMs {

    private static Config getConfig() {
        return new Config(
                ApplicationHolder.getConfigURL(),
                ApplicationHolder.getAppChannelId(),
                ApplicationHolder.getDataChannelId(),
                ApplicationHolder.getApiKey(),
                "");
    }

    private static Config getConfig(String configURL, String appChannelId, String dataChannelId, String apiKey) {
        return new Config(configURL, appChannelId, dataChannelId, apiKey, "");
    }

    /**
     * 获取对应公司的配置项
     * @param configRequest
     * @return
     */
    public static Map<String, Object> config(ConfigRequest configRequest) {
        return getConfig(ApplicationUtil.getConfigUrl(), ApplicationUtil.getConfigUrl(), ApplicationUtil.getDataChannelId(), ApplicationUtil.getApiKey()).getConfig(configRequest.getAppChannelId());
    }

}
