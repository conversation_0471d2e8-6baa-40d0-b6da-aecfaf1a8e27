package com.eu;

import com.eu.common.application.ApplicationConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @DATE: 2024/5/19
 * @AUTHOR: XSL
 *
 */
@SpringBootApplication
@EnableAsync
public class ApplicationMain {
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(ApplicationMain.class);
        app.setDefaultProperties(ApplicationConfig.getConfigMap());
        app.run(args);
    }
}