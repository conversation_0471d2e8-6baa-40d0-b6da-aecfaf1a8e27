package com.eu.framework.aspect;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.eu.common.annotation.LogRecord;
import com.eu.common.constant.MethodConstant;
import com.eu.common.context.RequestServletContext;
import com.eu.common.context.ServletContext;
import com.eu.common.holder.ApplicationHolder;
import com.eu.common.ms.log.LogMs;
import com.eu.common.ms.log.pojo.request.LogRequest;
import com.eu.common.pojo.LogRecordRequest;
import com.eu.common.util.ServletUtil;
import com.eu.common.util.StringUtil;
import com.eu.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 日志注解实现类
 * &#064;DATE:  2024/5/31
 * &#064;AUTHOR:  XSL
 *
 */
@Slf4j
@Aspect
@Component
@Order(2)
public class LogRecordAspect {

    /**
     * 存储计时器对象
     */
    public static final TransmittableThreadLocal<TimeInterval> stopWatchTTL = new TransmittableThreadLocal<>();

    /**
     * 方法前调用
     * @param joinPoint
     * @param logRecord
     */
    @Before(value = "@annotation(logRecord)")
    public void before(JoinPoint joinPoint, LogRecord logRecord) {
        stopWatchTTL.set(TimeUtil.timer());
    }

    /**
     * 成功后执行
     *
     * @param logRecord    注解
     * @param responseBody 响应参数
     */
    @AfterReturning(pointcut = "@annotation(logRecord)", returning = "responseBody")
    public void doAfterReturning(JoinPoint joinPoint, LogRecord logRecord, final Object responseBody) {
        actuator(joinPoint, logRecord, responseBody, null);
    }

    /**
     * 发生异常后执行
     *
     * @param logRecord 注解
     * @param exception 错误信息
     */
    @AfterThrowing(value = "@annotation(logRecord)", throwing = "exception")
    public void doAfterThrowing(JoinPoint joinPoint, LogRecord logRecord, final Exception exception) {
        actuator(joinPoint, logRecord, null, exception);
    }

    /**
     * 执行器
     * @param joinPoint 切面对象
     * @param logRecord 注解
     * @param responseBody 响应信息
     * @param exception 错误异常
     */
    private void actuator(final JoinPoint joinPoint, final LogRecord logRecord, final Object responseBody, final Exception exception) {
        try {
            final String method = ServletUtil.getMethod();
            if (RequestServletContext.isEmpty()) {
                return;
            }
            String ipAddress = ServletContext.getIpAddress();
            String appChannelId = ApplicationHolder.getAppChannelId();
            String dataChannelId = ApplicationHolder.getDataChannelId();
            String traceId = ServletContext.getTraceId();
            String uri = ServletContext.getUri();
//            CompletableFuture.runAsync(() -> {
                Map<String, Object> requestMap = new HashMap<>();
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                String[] parameterNameList = signature.getParameterNames(); // 获取方法参数名
                Object[] args = joinPoint.getArgs();
                int length = args.length;
                for (int i = 0; i < length; i++) {
                    requestMap.put(parameterNameList[i], args[i]);
                }
                LogMs.logAdd(
                        LogRequest.builder().content(
                                Collections.singletonList(
                                        simplifySTR(
                                                LogRecordRequest.builder()
                                                        .name(logRecord.name())
                                                        .module(logRecord.module())
                                                        .type(logRecord.type().getCode())
                                                        .requestIP(ipAddress)
                                                        .requestAppChannelId(appChannelId)
                                                        .requestDataChannelId(dataChannelId)
                                                        .traceId(traceId)
                                                        .elapsedTime(stopWatchTTL.get().interval())
                                                        .method(convert(method))
                                                        .requestBody(JSONUtil.toJsonStr(requestMap))
                                                        .responseBody(JSONUtil.toJsonStr(responseBody))
                                                        .requestURI(uri)
                                                        .success(Objects.isNull(exception) ? "true" : "false")
                                                        .exception(Objects.isNull(exception) ? "" : JSONUtil.toJsonStr(exception.getStackTrace()))
                                                        .createTime(DateUtil.now())
                                                        .build()
                                        )
                                )
                        ).build()
                );
//                stopWatchTTL.remove();
//            });
            stopWatchTTL.remove();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 请求类型转对应的标识
     * @param method
     * @return
     */
    private int convert(String method) {
        switch (method) {
            case "GET":
                return MethodConstant.GET.getCode();
            case "POST":
                return MethodConstant.POST.getCode();
            case "HEAD":
                return MethodConstant.HEAD.getCode();
            case "OPTIONS":
                return MethodConstant.OPTIONS.getCode();
            case "PUT":
                return MethodConstant.PUT.getCode();
            case "PATCH":
                return MethodConstant.PATCH.getCode();
            case "DELETE":
                return MethodConstant.DELETE.getCode();
            case "TRACE":
                return MethodConstant.TRACE.getCode();
            case "CONNECT":
                return MethodConstant.CONNECT.getCode();
            default:
                return MethodConstant.DEFAULT.getCode();
        }
    }

    /**
     * 简化日志长度 将长度限制在 1w 个字符以内
     * @param logRecordRequest
     * @return
     */
    private String simplifySTR(LogRecordRequest logRecordRequest) {
        String jsonStr = JSONUtil.toJsonStr(logRecordRequest);
        if (jsonStr.length() > 80000) {
            logRecordRequest.setResponseBody("");
            jsonStr = JSONUtil.toJsonStr(logRecordRequest);
        }
        if (jsonStr.length() > 80000) {
            logRecordRequest.setRequestBody("");
            jsonStr = JSONUtil.toJsonStr(logRecordRequest);
        }
        if (jsonStr.length() > 80000) {
            String exception = logRecordRequest.getException();
            if (StringUtil.isNotEmpty(exception)) {
                while (jsonStr.length() > 80000) {
                    exception = logRecordRequest.getException();
                    if (JSONUtil.isTypeJSONArray(exception)) {
                        JSONArray jsonArray = JSONUtil.parseArray(exception);
                        if (jsonArray.size() > 1) {
                            List<String> exceptionListSTR = jsonArray.toList(String.class).stream().limit(jsonArray.size() / 2).collect(Collectors.toList());
                            logRecordRequest.setException(JSONUtil.quote(JSONUtil.toJsonStr(exceptionListSTR)));
                            jsonStr = JSONUtil.toJsonStr(logRecordRequest);
                        } else {
                            logRecordRequest.setException("");
                            jsonStr = JSONUtil.toJsonStr(logRecordRequest);
                            break;
                        }
                    } else {
                        break;
                    }
                }
            }
        }

        return jsonStr;
    }

}
