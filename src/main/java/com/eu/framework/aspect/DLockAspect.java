package com.eu.framework.aspect;

import com.eu.common.annotation.DLock;
import com.eu.common.client.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解功能实现类
 * @DATE: 2024/5/31
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Aspect
@Component
@Order(0)
public class DLockAspect {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AspectExtend aspectExtend;

    @Around(value = "@annotation(dLock)")
    public Object around(ProceedingJoinPoint joinPoint, DLock dLock) throws Throwable {
        String clazzName = joinPoint.getTarget().getClass().getName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNameList = signature.getParameterNames(); // 获取方法参数名
        Object[] args = joinPoint.getArgs(); // 获取方法参数值

        String redisKey = aspectExtend.getRedisKey(dLock.prefix(), dLock.key(), clazzName, signature, args, parameterNameList);
        RLock lock = redisCache.getLock(redisKey);
        boolean isLocked = lock.tryLock(dLock.loading(), dLock.unLock(), TimeUnit.SECONDS);
        try {
            if (isLocked) {
                log.info("开启锁  --> {}", redisKey);
            } else {
                throw new Throwable("上锁失败");
            }
            return joinPoint.proceed();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放锁  --> {}", redisKey);
            }
        }

    }
}
