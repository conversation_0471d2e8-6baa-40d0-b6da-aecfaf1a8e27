package com.eu.framework.aspect;

import com.eu.common.annotation.CacheDel;
import com.eu.common.client.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 删除缓存注解实现方法
 * @DATE: 2024/6/6
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class CacheDelAspect {

    @Autowired
    private RedisCache redisCache;
    @Autowired
    private AspectExtend aspectExtend;

    @Around(value = "@annotation(cacheDel)")
    public Object around(ProceedingJoinPoint joinPoint, CacheDel cacheDel) throws Throwable {
        String clazzName = joinPoint.getTarget().getClass().getName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNameList = signature.getParameterNames(); // 获取方法参数名
        Object[] args = joinPoint.getArgs(); // 获取方法参数值

        String redisKey = aspectExtend.getRedisKey(cacheDel.prefix(), cacheDel.key(), clazzName, signature, args, parameterNameList);

        Boolean remove = redisCache.remove(redisKey);
        if (remove) {
            log.info("删除缓存 缓存键 --> {}", redisKey);
        }
        return joinPoint.proceed();
    }

}
