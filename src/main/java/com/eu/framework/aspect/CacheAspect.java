package com.eu.framework.aspect;

import cn.hutool.json.JSONUtil;
import com.eu.common.annotation.Cache;
import com.eu.common.client.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 缓存注解实现方法
 * @DATE: 2024/6/6
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Aspect
@Component
@Order(1)
public class CacheAspect {

    @Autowired
    private AspectExtend aspectExtend;
    @Autowired
    private RedisCache redisCache;

    @Around(value = "@annotation(cache)")
    public Object around(ProceedingJoinPoint joinPoint, Cache cache) throws Throwable {
        String clazzName = joinPoint.getTarget().getClass().getName();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNameList = signature.getParameterNames(); // 获取方法参数名
        Object[] args = joinPoint.getArgs(); // 获取方法参数值

        String redisKey = aspectExtend.getRedisKey(cache.prefix(), cache.key(), clazzName, signature, args, parameterNameList);

        Object cacheObject = redisCache.getObject(redisKey);
        if (Objects.nonNull(cacheObject)) {
            return cacheObject;
        }
        Object proceed = joinPoint.proceed();
        try {
            return proceed;
        } finally {
            redisCache.setObject(redisKey, proceed);
            log.info("进行缓存 -> {}", JSONUtil.toJsonPrettyStr(proceed));
        }
    }

}
