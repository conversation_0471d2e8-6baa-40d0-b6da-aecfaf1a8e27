package com.eu.framework.aspect;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * 切面扩展类
 * @DATE: 2024/6/20
 * @AUTHOR: XSL
 *
 */
@Component
public class AspectExtend {

    /**
     * 通过前缀获取最终的键
     * @param prefix 键前缀
     * @param key 键
     * @return
     */
    public String getRedisKey(String prefix, String key, String clazzName, MethodSignature signature, Object[] args, String[] parameterNameList) {
        StringBuilder redisKey = new StringBuilder();
        String methodName = signature.getMethod().getName();
        if (prefix.isEmpty() || key.isEmpty()) {
            redisKey.append(clazzName.replaceAll("\\.", "")).append(methodName);
        } else {
            redisKey.append(prefix).append(":");

            String[] split = key.split("\\.");
            if (split.length > 1) {
                String paramName = split[0];    //参数名称
                String paramType = split[1];    //值的属性名

                for (int i = 0; i < parameterNameList.length; i++) {
                    if (parameterNameList[i].equals(paramName)) {
                        JSONObject jsonObject = JSONUtil.parseObj(args[i]);
                        redisKey.append(jsonObject.getStr(paramType));
                        break;
                    }
                }

            } else if (split.length == 1) {
                String paramName = split[0];    //参数值
                for (int i = 0; i < parameterNameList.length; i++) {
                    if (parameterNameList[i].equals(paramName)) {
                        redisKey.append(args[i].toString());
                        break;
                    }
                }
            }
        }
        return redisKey.toString();
    }


}
