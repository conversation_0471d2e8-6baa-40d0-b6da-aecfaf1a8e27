package com.eu.framework.aspect;

import com.eu.common.annotation.MLock;
import com.eu.common.client.RedisCache;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 分布式锁注解功能实现类
 * @DATE: 2024/5/31
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Aspect
@Component
@Order(0)
public class MLockAspect {

    @Autowired
    private RedisCache redisCache;

    @Around(value = "@annotation(mLock)")
    public Object around(ProceedingJoinPoint joinPoint, MLock mLock) throws Throwable {
        StringBuffer redisKey = new StringBuffer();
        redisKey.append(joinPoint.getTarget().getClass().getName().replaceAll("\\.", "")).append(((MethodSignature) joinPoint.getSignature()).getMethod().getName());

        RLock lock = redisCache.getLock(redisKey.toString());
        boolean isLocked = lock.tryLock(mLock.loading(), mLock.unLock(), TimeUnit.SECONDS);
        try {
            if (isLocked) {
                log.info("开启锁  --> {}", redisKey);
            } else {
                throw new Throwable("上锁失败");
            }
            return joinPoint.proceed();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.info("释放锁  --> {}", redisKey);
            }
        }

    }
}
