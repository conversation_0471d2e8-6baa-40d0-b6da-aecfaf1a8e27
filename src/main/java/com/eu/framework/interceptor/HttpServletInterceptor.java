package com.eu.framework.interceptor;

import com.eu.common.context.RequestServletContext;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * &#064;DATE: 2025/1/14 14:03
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class HttpServletInterceptor implements HandlerInterceptor {

    /**
     * 请求进来前执行
     * 查看是否携带有效凭证
     * @param request 请求
     * @param response 响应
     * @param handler 执行器
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
//        if (RequestServletContext.isEmpty()) {
//            log.info(CodeConstant.AUTHORITY_DENIED.getMessage());
//            throw new AuthorityException(CodeConstant.AUTHORITY_DENIED.getMessage());
//        }
        return HandlerInterceptor.super.preHandle(request, response, handler);
    }

    /**
     * 业务执行完成后执行
     * @param request
     * @param response
     * @param handler
     * @param modelAndView
     * @throws Exception
     */
    @Override
    public void postHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    /**
     * 在所有的处理器执行完成后执行
     * @param request
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
        RequestServletContext.clear();
        HandlerInterceptor.super.afterCompletion(request, response, handler, ex);
    }

}
