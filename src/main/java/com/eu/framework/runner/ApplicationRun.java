package com.eu.framework.runner;

import com.eu.common.application.ApplicationConfig;
import com.eu.framework.server.Microservice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @DATE: 2024/5/19
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class ApplicationRun implements ApplicationRunner {

    @Autowired
    private Microservice microservice;

    /**
     * 启动会调用方法
     * @param args
     * @throws Exception
     */
    @Override
    public void run(ApplicationArguments args) {
        ApplicationConfig.inject(false);
//        microservice.register();
        microservice.bootTime();
        microservice.logInfo();
    }


}
