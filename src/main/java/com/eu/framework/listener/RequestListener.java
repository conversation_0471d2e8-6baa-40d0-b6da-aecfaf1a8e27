package com.eu.framework.listener;

import cn.hutool.core.date.TimeInterval;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.eu.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequestEvent;
import javax.servlet.ServletRequestListener;

/**
 * 请求监视的执行优先与过滤器和拦截器,监视的是Servlet的生命周期
 * @DATE: 2024/6/24
 * @AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class RequestListener implements ServletRequestListener {

    /**
     * 存储计时器对象
     */
    public static final TransmittableThreadLocal<TimeInterval> stopWatchTTL = new TransmittableThreadLocal<>();

    /**
     *
     * @param sre
     */
    @Override
    public void requestInitialized(ServletRequestEvent sre) {
        stopWatchTTL.set(TimeUtil.timer());
        ServletRequestListener.super.requestInitialized(sre);
    }


    @Override
    public void requestDestroyed(ServletRequestEvent sre) {
        ServletRequestListener.super.requestDestroyed(sre);
        log.info("总耗时 --> {}", stopWatchTTL.get().interval());
        stopWatchTTL.remove();
    }
}
