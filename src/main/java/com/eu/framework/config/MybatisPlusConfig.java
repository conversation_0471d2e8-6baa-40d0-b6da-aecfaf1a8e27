package com.eu.framework.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @DATE: 2024/5/19
 * @AUTHOR: XSL
 *
 */
@Configuration
public class MybatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        /*
         * 数据变动插件
         */
//        DataChangeRecorderInnerInterceptor dataChangeRecorderInnerInterceptor = new DataChangeRecorderInnerInterceptor();
//        dataChangeRecorderInnerInterceptor.setBatchUpdateLimit(10000).openBatchUpdateLimitation();
//        interceptor.addInnerInterceptor(dataChangeRecorderInnerInterceptor);

        /*
         * 非法SQL拦截
         */
//        interceptor.addInnerInterceptor(new IllegalSQLInnerInterceptor());

        /*
         * 拦截全表的更新及删除语句
         */
//        interceptor.addInnerInterceptor(new BlockAttackInnerInterceptor());

        /*
         * 分页基础配置 如果多个插件 ,分页要在最后
         */
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        paginationInnerInterceptor.setDbType(DbType.MYSQL);//指令数据库的分页类型
        paginationInnerInterceptor.setOverflow(true);   //设置请求的页面大于最大页后操作， true调回到首页，false 继续请求  默认false
        paginationInnerInterceptor.setMaxLimit(500L);    //设置最大单页限制数量，默认 500 条，-1 不受限制
        interceptor.addInnerInterceptor(paginationInnerInterceptor);

        return interceptor;
    }
}
