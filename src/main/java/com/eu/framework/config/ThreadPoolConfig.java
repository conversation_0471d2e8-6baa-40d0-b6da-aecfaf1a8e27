package com.eu.framework.config;

import com.eu.common.util.SystemUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.*;

/**
 * @DATE: 2024/6/5
 * @AUTHOR: XSL
 *
 */
@Configuration
public class ThreadPoolConfig {

//    /**
//     * 传统线程池
//     * @return
//     */
//    @Bean
//    public ThreadPoolExecutor threadPoolExecutor() {
//        int cpuThread = SystemUtil.getCPUThread();
//        return new ThreadPoolExecutor(
//                cpuThread * 2,
//                cpuThread * 4,
//                20L,
//                TimeUnit.SECONDS,
//                new LinkedBlockingQueue<>(30),
//                Executors.defaultThreadFactory(),
//                new ThreadPoolExecutor.CallerRunsPolicy());
//    }

//    /**
//     * 定时任务线程池
//     * @return
//     */
//    @Bean
//    public ScheduledThreadPoolExecutor scheduledThreadPoolExecutor() {
//        return new ScheduledThreadPoolExecutor(
//                SystemUtil.getCPUThread(),
//                Executors.defaultThreadFactory(),
//                new ThreadPoolExecutor.CallerRunsPolicy());
//    }


}
