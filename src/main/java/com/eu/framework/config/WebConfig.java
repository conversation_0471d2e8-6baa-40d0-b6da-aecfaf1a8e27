package com.eu.framework.config;

import com.eu.common.param.ParamFinal;
import com.eu.framework.interceptor.HttpServletInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * &#064;DATE: 2025/1/14 14:02
 * &#064;AUTHOR: XSL
 *
 */
@Configuration
@EnableTransactionManagement
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private HttpServletInterceptor httpServletInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(httpServletInterceptor).addPathPatterns("/**").excludePathPatterns(ParamFinal.PASS_URI_ARRAY);
        WebMvcConfigurer.super.addInterceptors(registry);
    }

}