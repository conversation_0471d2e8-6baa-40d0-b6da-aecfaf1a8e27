package com.eu.framework.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @DATE: 2024/5/31
 * @AUTHOR: XSL
 *
 */
@Configuration
public class RedisConfig {

    /**
     * Redis地址
     */
    @Value("${spring.data.redis.host}")
    public String host;

    /**
     * Redis端口
     */
    @Value("${spring.data.redis.port:6379}")
    public String port;

    /**
     * Redis数据库
     */
    @Value("${spring.data.redis.database:0}")
    public int dataBase;

    /**
     * Redis密码
     */
    @Value("${spring.data.redis.password:}")
    public String password;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://" + host + ":" + port)
                .setPassword(password)  //设置密码
                .setDatabase(dataBase)  //指定库
                .setConnectionMinimumIdleSize(3) // 最小空闲连接数
                .setConnectionPoolSize(20)         // 连接池大小
                .setConnectTimeout(3000)           // 连接超时时间
                .setTimeout(3000)                 // 命令执行超时时间
                .setIdleConnectionTimeout(10000);  // 空闲连接超时时间;
        config.setCodec(new JsonJacksonCodec());    //配置序列化
        return Redisson.create(config);
    }

}
