package com.eu.framework.server;

import com.eu.common.client.RedisCache;
import com.eu.common.param.ParamFinal;
import com.eu.common.util.CommonUtil;
import com.eu.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.Inet4Address;
import java.net.UnknownHostException;

/**
 * &#064;DATE: 2025/1/22 15:17
 * &#064;AUTHOR: XSL
 *
 */
@Slf4j
@Component
public class Microservice {

    @Autowired
    private Environment environment;
    @Autowired
    private RedisCache redisCache;

    /**
     * 打印信息
     */
    public void logInfo() {
        System.out.println("项目初始化并启动完成");
        System.out.println("Name : " + environment.getProperty("spring.application.name", "ms-approve"));
        System.out.println("Time : " + TimeUtil.getNowTimeStr());
        System.out.println("Version : " + ParamFinal.VERSION);
//        System.out.println("MySQL : " + StringUtil.mysqlSub(environment.getProperty("spring.datasource.url")));
//        System.out.println("Redis : " + environment.getProperty("spring.data.redis.host") + ":" + environment.getProperty("spring.data.redis.port", "6379") + "/" + environment.getProperty("spring.data.redis.database", "0"));
        try {
            System.out.println("Ip : " + Inet4Address.getLocalHost().getHostAddress());
        } catch (UnknownHostException ignored) {
        }
        System.out.println("Port : " + environment.getProperty("server.port", String.class, "8080"));
//        System.out.println("msConfigDomain : " + environment.getProperty("msConfigDomain"));
//        System.out.println("msLogDomain : " + environment.getProperty("msLogDomain"));
        System.out.println("AppChannelId : " + environment.getProperty("server.appChannelId"));
        System.out.println("DataChannelId : " + environment.getProperty("server.dataChannelId"));
        System.out.println("Dependencies : MySQL/1," + "Redis/" + (redisCache.ping() ? 1 : 0));
    }

    /**
     * 记录启动时间
     */
    public void bootTime() {
        boolean notLinux = CommonUtil.isNotLinux();
        if (notLinux) {
            return;
        }
        redisCache.setStr(ParamFinal.BOOT_BOOT_KEY, TimeUtil.getNowTimeStr());
    }


}
