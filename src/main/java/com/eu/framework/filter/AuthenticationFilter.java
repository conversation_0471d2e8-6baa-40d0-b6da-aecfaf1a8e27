package com.eu.framework.filter;

import com.eu.common.constant.CodeConstant;
import com.eu.common.context.RequestServletContext;
import com.eu.common.param.ParamFinal;
import com.eu.common.pojo.RequestContext;
import com.eu.common.util.IpUtil;
import com.eu.common.util.PathUriUtil;
import com.eu.common.util.StringUtil;
import com.eu.common.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 令牌过滤器
 * &#064;DATE:  2024/6/3
 * &#064;AUTHOR:  XSL
 *
 */
@Slf4j
@Component
public class AuthenticationFilter extends OncePerRequestFilter {

    @Override
    protected void doFilterInternal(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain filterChain) throws ServletException, IOException {
        String requestURI = request.getRequestURI();
        if (PathUriUtil.containsPath(requestURI)) {
            this.headerInfo(request);
            filterChain.doFilter(request, response);
        } else {
            log.error(CodeConstant.PATH_NONE.getMessage() + ": {}", requestURI);
            WebUtil.out(CodeConstant.PATH_NONE);
        }
    }

    /**
     * 获取头部信息
     * @param request
     */
    private void headerInfo(HttpServletRequest request) {
        String appChannelId = request.getHeader(ParamFinal.HEADER_APP_CHANNEL_ID);
        String dataChannelId = request.getHeader(ParamFinal.HEADER_DATA_CHANNEL_ID);
        String traceId = request.getHeader(ParamFinal.HEADER_TRACE_ID);
        String ipAddr = IpUtil.getIpAddr(request);
        String requestURI = request.getRequestURI();

        if (StringUtil.isNotEmpty(appChannelId)) {
            logger.info("appChannelId -> " + appChannelId);
        }
        if (StringUtil.isNotEmpty(dataChannelId)) {
            logger.info("dataChannelId -> " + dataChannelId);
        }
        if (StringUtil.isNotEmpty(traceId)) {
            logger.info("traceId -> " + traceId);
        }
        if (StringUtil.isNotEmpty(ipAddr)) {
            logger.info("ip -> " + ipAddr);
        }
        logger.info("requestURI -> " + requestURI);

        RequestServletContext.set(
                RequestContext.builder()
                        .appChannelId(appChannelId)
                        .dataChannelId(dataChannelId)
                        .traceId(traceId)
                        .ipAddress(ipAddr)
                        .uri(requestURI)
                        .build()
        );
    }


}
