package com.eu.framework.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.eu.common.util.TimeUtil;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 该类配置的方法是为执行SQL时对应字段的数据,同时字段需要注解绑定(将注解绑定的数据进行识别,如果有 就拼接设置好的数据)
 * &#064;DATE:  2024/5/19
 * &#064;AUTHOR:  XSL
 *
 */
@Component
public class MybatisPlusHandler implements MetaObjectHandler {

    /**
     * 插入语句识别拼接
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        LocalDateTime localDateTime = TimeUtil.nowDateTime();
        this.setFieldValByName("createTime", localDateTime, metaObject);
        this.setFieldValByName("updateTime", localDateTime, metaObject);
    }

    /**
     * 更新语句识别拼接
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", TimeUtil.nowDateTime(), metaObject);
    }
}
