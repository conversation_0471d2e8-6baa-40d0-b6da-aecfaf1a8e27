package com.eu.framework.handler;

import com.eu.common.constant.CodeConstant;
import com.eu.common.exception.AuthorityException;
import com.eu.common.exception.BsException;
import com.eu.common.exception.GeneralException;
import com.eu.common.exception.HttpException;
import com.eu.common.result.DResult;
import com.eu.common.result.Result;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

/**
 * 全局异常捕获
 * &#064;DATE:  2024/5/20
 * &#064;AUTHOR:  XSL
 *
 */
@RestControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * 业务异常捕获
     * @param exception
     * @return
     */
    @ExceptionHandler(BsException.class)
    public Result exceptionHandler(BsException exception) {
        String message = exception.getMessage();
        exception.printStackTrace();
        return DResult.answer(CodeConstant.BUSINESS_ERROR.getCode(), message);
    }

    /**
     * 业务异常捕获
     * @param exception
     * @return
     */
    @ExceptionHandler(AuthorityException.class)
    public Result exceptionHandler(AuthorityException exception) {
        String message = exception.getMessage();
        exception.printStackTrace();
        return DResult.answer(CodeConstant.AUTHORITY_DENIED.getCode(), message);
    }

    /**
     * 第三方请求出现错误
     * @param exception
     * @return
     */
    @ExceptionHandler(HttpException.class)
    public Result exceptionHandler(HttpException exception) {
        String message = exception.getMessage();
        exception.printStackTrace();
        return DResult.answer(CodeConstant.HTTP_ERROR.getCode(), message);
    }

    /**
     * 框架运行异常
     * @param exception
     * @return
     */
    @ExceptionHandler(GeneralException.class)
    public Result exceptionHandler(GeneralException exception) {
        exception.printStackTrace();
        return DResult.answer(exception.getCodeConstant());
    }

    /**
     * 全部的异常捕获
     * @param exception
     * @return
     */
    @ExceptionHandler(Exception.class)
    public Result exceptionHandler(Exception exception) {
        exception.printStackTrace();
        return DResult.answer(CodeConstant.SERVER_ERROR);
    }

}
