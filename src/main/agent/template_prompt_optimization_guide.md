# 通用提示词优化指南

## 🎯 提示词优化原则

### 1. **明确性原则**
- 使用具体、明确的描述
- 避免模糊或歧义的表达
- 提供充分的上下文信息

**示例对比**：
```
❌ 差的提示词：
"帮我写个登录功能"

✅ 好的提示词：
"请为当前项目实现手机号验证码登录功能，使用项目的状态管理方案，包含输入验证、网络请求和错误处理"
```

### 2. **结构化原则**
使用清晰的结构组织提示词：
- 角色定义
- 任务描述
- 技术要求
- 输出格式
- 约束条件

### 3. **上下文丰富原则**
提供足够的项目背景信息：
- 技术栈
- 架构模式
- 现有代码结构
- 项目规范

## 🔧 优化技巧

### 1. **使用角色扮演**
```markdown
# 角色定义
你是一个有丰富经验的开发工程师，专门负责当前项目的开发和维护。你熟悉项目的技术栈、架构模式和整体结构。
```

### 2. **提供具体示例**
```markdown
# 代码风格示例
请参考以下现有代码风格：
[提供项目中的实际代码示例]
```

### 3. **分步骤描述**
```markdown
请按以下步骤实现：
1. 创建业务逻辑类
2. 实现核心功能方法
3. 添加错误处理逻辑
4. 创建对应的视图层
5. 实现用户交互逻辑
```

### 4. **约束条件明确**
```markdown
# 必须遵循
- 使用项目配置的命令格式
- 遵循项目现有的架构模式
- 包含完整的错误处理
- 使用项目定义的常量和工具类

# 禁止使用
- 硬编码的字符串和数值
- 过时的API或方法
- 不符合项目规范的代码风格
```

## 📝 常见场景优化

### 1. **功能开发提示词**
```markdown
# 优化前
"帮我实现一个列表页面"

# 优化后
"请为当前项目实现数据列表页面：

## 功能要求
- 分页加载数据
- 支持下拉刷新和上拉加载更多
- 显示关键信息字段
- 点击跳转到详情页

## 技术实现
- 使用项目的状态管理方案
- 网络请求使用项目统一的网络层
- UI 使用项目配置的组件库
- 遵循项目现有的架构模式

## 输出要求
1. 业务逻辑类实现
2. 视图层实现
3. 相关的数据模型
4. 错误处理逻辑
5. 使用说明"
```

### 2. **问题排查提示词**
```markdown
# 优化前
"我的应用崩溃了，怎么办？"

# 优化后
"项目在特定设备上出现崩溃问题：

## 问题描述
- 应用在特定操作后崩溃
- 主要发生在特定设备或系统版本
- 其他平台运行正常

## 错误信息
[粘贴完整的崩溃日志]

## 环境信息
- 技术栈版本信息
- 开发工具版本
- 最近的代码变更

## 需要的帮助
1. 分析崩溃原因
2. 提供解决方案
3. 预防类似问题的建议
4. 相关的调试命令"
```

### 3. **代码优化提示词**
```markdown
# 优化前
"这段代码有问题吗？[代码]"

# 优化后
"请审查以下项目中的代码：

## 代码内容
[粘贴具体代码]

## 审查重点
- 是否符合项目架构规范
- 性能是否可以优化
- 错误处理是否完善
- 代码可读性和可维护性
- 是否遵循最佳实践

## 期望输出
1. 问题识别和分析
2. 具体的改进建议
3. 优化后的代码示例
4. 相关的最佳实践说明"
```

## 🚀 高级优化技巧

### 1. **链式提示**
将复杂任务分解为多个相关的提示词：

```markdown
# 第一步：架构设计
"请为当前项目设计某个模块的架构..."

# 第二步：具体实现
"基于上面的架构设计，请实现核心业务逻辑..."

# 第三步：UI 实现
"基于前面的逻辑实现，请创建用户界面..."
```

### 2. **上下文延续**
在对话中保持上下文的连续性：

```markdown
"基于我们刚才讨论的功能实现，现在请添加相关的扩展功能..."
```

### 3. **反馈循环**
根据 AI 的回复进行进一步优化：

```markdown
"你提供的代码很好，但是我希望在某个方面更加详细..."
```

## 📊 效果评估

### 好的提示词特征
- ✅ 回复准确且符合项目规范
- ✅ 代码可以直接使用或稍作修改
- ✅ 包含完整的实现逻辑
- ✅ 提供了有用的补充说明

### 需要优化的信号
- ❌ 回复过于通用，缺乏项目特色
- ❌ 代码不符合项目架构
- ❌ 缺少重要的错误处理
- ❌ 使用了错误的技术方案

## 💡 实用建议

1. **保存常用模板**：将经常使用的提示词保存为模板
2. **迭代优化**：根据效果不断调整提示词
3. **团队共享**：与团队成员分享有效的提示词
4. **文档记录**：记录优化过程和效果对比
5. **定期更新**：随着项目发展更新提示词模板

## 🎯 模板使用指南

### 选择合适的模板
根据具体需求选择对应的提示词模板，并填入具体信息。

### 自定义调整
根据实际情况调整模板内容，添加项目特定的要求。

### 持续优化
根据 AI 的回复质量，不断优化提示词的表达方式。
