# 通用 AI 助手使用指南

## 🎯 概述

本指南提供了与 AI 助手高效协作的通用方法和最佳实践，适用于任何技术栈和项目类型。

## 📋 基本原则

### 1. **明确性原则**
- 提供具体、明确的需求描述
- 避免模糊或歧义的表达
- 包含充分的上下文信息

### 2. **结构化原则**
- 使用清晰的结构组织请求
- 分步骤描述复杂任务
- 明确输入和输出要求

### 3. **迭代改进原则**
- 根据反馈调整请求方式
- 逐步细化需求
- 持续优化协作效果

## 🔧 有效沟通技巧

### 角色定义
```markdown
# 示例
你是一个有丰富经验的软件开发工程师，专门负责 [项目类型] 的开发和维护。
你熟悉 [技术栈]、[架构模式] 和 [开发规范]。
```

### 任务描述
```markdown
# 结构化任务描述
## 背景
[项目背景和当前状况]

## 目标
[要实现的具体目标]

## 要求
[技术要求和约束条件]

## 输出
[期望的输出格式和内容]
```

### 上下文提供
- 技术栈信息
- 项目架构
- 现有代码结构
- 相关规范和约定

## 📝 常见场景模板

### 功能开发
```markdown
请帮我实现 [功能名称]：

## 功能需求
- [具体功能点1]
- [具体功能点2]
- [具体功能点3]

## 技术要求
- 使用 [技术栈]
- 遵循 [架构模式]
- 包含 [特定要求]

## 输出要求
1. 完整的实现代码
2. 必要的错误处理
3. 代码注释和说明
4. 使用示例
```

### 问题排查
```markdown
遇到以下问题需要帮助：

## 问题描述
[详细描述问题现象]

## 错误信息
[粘贴完整的错误日志]

## 环境信息
- 技术栈版本
- 操作系统
- 相关工具版本

## 期望帮助
1. 问题原因分析
2. 解决方案
3. 预防措施
```

### 代码审查
```markdown
请审查以下代码：

## 代码内容
[粘贴具体代码]

## 审查重点
- 代码质量
- 性能优化
- 安全性
- 可维护性

## 期望输出
1. 问题识别
2. 改进建议
3. 优化示例
```

## 🚀 高级技巧

### 1. **链式对话**
将复杂任务分解为多个步骤：
```markdown
# 第一步：设计
请设计 [功能] 的整体架构

# 第二步：实现
基于上面的设计，请实现核心逻辑

# 第三步：优化
请优化上面的实现，重点关注性能
```

### 2. **上下文延续**
在对话中保持连续性：
```markdown
基于我们刚才讨论的 [内容]，现在请 [新的要求]
```

### 3. **反馈循环**
根据回复进行进一步优化：
```markdown
你的建议很好，但我希望在 [方面] 更加详细...
```

## 📊 质量评估

### 好的请求特征
- ✅ 需求明确具体
- ✅ 上下文信息充分
- ✅ 输出要求清晰
- ✅ 技术约束明确

### 需要改进的信号
- ❌ 回复过于通用
- ❌ 缺少关键信息
- ❌ 不符合项目规范
- ❌ 无法直接使用

## 💡 最佳实践

### 1. **准备工作**
- 明确目标和需求
- 收集相关信息
- 准备示例和参考

### 2. **沟通过程**
- 使用结构化的描述
- 提供充分的上下文
- 及时反馈和调整

### 3. **结果验证**
- 检查输出质量
- 验证可用性
- 记录经验教训

## 🔄 持续改进

### 记录和总结
- 保存有效的提示词模板
- 记录成功的协作模式
- 总结常见问题和解决方案

### 团队协作
- 分享有效的沟通方式
- 建立团队规范
- 定期优化协作流程

### 工具和方法
- 使用模板提高效率
- 建立知识库
- 自动化常见任务

## 📞 故障排除

### 常见问题
1. **回复不够具体**
   - 增加上下文信息
   - 明确技术要求
   - 提供具体示例

2. **代码不符合规范**
   - 说明项目规范
   - 提供代码示例
   - 明确约束条件

3. **理解有偏差**
   - 重新组织描述
   - 分步骤说明
   - 提供更多背景

### 解决策略
- 逐步细化需求
- 提供更多示例
- 调整沟通方式
- 寻求澄清和确认

---

**提示**: 有效的 AI 协作需要练习和调整。根据具体项目和团队需求，适当调整这些通用指南。
