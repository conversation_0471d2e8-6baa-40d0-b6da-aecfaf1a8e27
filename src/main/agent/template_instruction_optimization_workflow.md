# 通用指令优化工作流

## 🔄 工作流程

每次收到用户指令时，我将按以下步骤进行：

### 1. **指令分析阶段**
- 识别指令类型和意图
- 分析当前指令的完整性
- 识别缺失的关键信息
- 评估指令的清晰度

### 2. **优化建议阶段**
- 提供优化后的指令版本
- 说明优化的原因和改进点
- 标注关键的补充信息
- 确认是否需要额外的上下文

### 3. **执行确认阶段**
- 询问是否使用优化后的指令
- 或者按原指令执行
- 记录优化模式的偏好

### 4. **任务执行阶段**
- 按照确认的指令执行任务
- 应用项目规范和最佳实践
- 提供高质量的输出结果

## 📋 优化模板

### 指令分析模板
```
## 📝 原始指令分析
**指令类型**: [功能开发/问题排查/代码优化/文档编写/其他]
**完整性评分**: [1-10分]
**主要问题**: 
- 缺失的关键信息
- 模糊的表达
- 不明确的要求

## ✨ 优化建议
**优化后的指令**:
[提供结构化、明确的指令版本]

**优化要点**:
- 添加的上下文信息
- 明确的技术要求
- 具体的输出格式
- 相关的约束条件

## 🎯 执行确认
是否使用优化后的指令执行任务？
- [ ] 使用优化版本
- [ ] 使用原始指令
- [ ] 需要进一步调整
```

## 🔧 通用优化规则

### 1. **技术栈补充**
如果指令涉及开发任务，自动补充：
- 项目使用的技术栈信息
- 状态管理方案
- 网络请求方式
- 项目架构规范

### 2. **命令格式规范**
根据项目配置使用正确的命令格式

### 3. **上下文增强**
根据指令类型自动添加：
- 项目结构信息
- 相关的现有代码
- 技术约束条件
- 输出格式要求

### 4. **错误处理要求**
开发类指令自动添加：
- 异常处理要求
- 用户友好的错误提示
- 日志记录规范

## 📊 优化示例

### 示例 1: 功能开发指令

**原始指令**:
"帮我写个用户设置页面"

**优化分析**:
- 指令类型: 功能开发
- 完整性评分: 3/10
- 主要问题: 缺少具体功能要求、技术实现细节、UI设计要求

**优化后的指令**:
```
请为当前项目实现用户设置页面：

## 功能要求
- 显示用户基本信息（头像、昵称、联系方式）
- 提供设置选项（通知设置、隐私设置、关于我们）
- 支持退出登录功能
- 支持头像更换功能

## 技术实现
- 使用项目现有的状态管理方案
- 遵循项目现有架构模式
- 网络请求使用项目统一的网络层
- 本地存储使用项目配置的存储方案

## UI 设计
- 遵循项目设计规范
- 使用项目定义的颜色和字体常量
- 支持屏幕适配
- 提供加载状态和错误状态

## 输出要求
1. 业务逻辑类实现
2. 视图层实现
3. 相关的路由配置
4. 必要的错误处理
5. 使用说明和注释
```

### 示例 2: 问题排查指令

**原始指令**:
"应用启动很慢"

**优化分析**:
- 指令类型: 问题排查
- 完整性评分: 2/10
- 主要问题: 缺少具体症状、环境信息、复现步骤

**优化后的指令**:
```
项目启动性能问题排查：

## 问题描述
- 应用启动时间: [具体秒数]
- 主要发生在: [冷启动/热启动/特定场景]
- 影响平台: [iOS/Android/Web/全平台]
- 问题出现时间: [最近开始/一直存在]

## 环境信息
- 技术栈版本信息
- 设备型号: [具体设备]
- 系统版本: [具体版本]
- 构建模式: [Debug/Release]

## 性能数据
- 启动时间测量结果
- 内存使用情况
- CPU 使用率
- 相关的性能日志

## 分析要求
1. 识别性能瓶颈
2. 提供优化方案
3. 给出具体的实现步骤
4. 包含验证方法
5. 使用项目配置的命令格式进行分析
```

## 🎯 使用方式

从现在开始，当您发送指令时，我会：

1. **立即分析**您的指令
2. **提供优化建议**
3. **等待您的确认**
4. **执行优化后的任务**

您可以通过以下方式控制这个流程：

- 说"直接执行"跳过优化步骤
- 说"只要优化建议"仅获取优化版本
- 说"详细分析"获取更深入的指令分析

## 📈 持续改进

我会根据您的反馈不断优化这个工作流：
- 记录您的偏好设置
- 学习您的指令模式
- 调整优化策略
- 提高优化质量

准备好了吗？请发送您的下一个指令，我将为您进行优化分析！
